// TESTE ESPECÍFICO PARA TOKEN REAL
// Execute no console do app para verificar

console.log('🎯 TESTE ESPECÍFICO DO TOKEN');
console.log('============================');

// Importar serviço
import { firebaseExpoService } from './src/services/firebaseExpoService';

async function testarToken() {
  console.log('🚀 Iniciando teste do token...');
  
  try {
    // Inicializar serviço
    const token = await firebaseExpoService.initialize();
    
    console.log('📊 RESULTADO DO TESTE:');
    console.log('======================');
    
    if (token) {
      console.log('✅ Token obtido:', token);
      
      // Verificar tipo
      if (token.startsWith('ExponentPushToken[')) {
        console.log('🎯 TIPO: TOKEN REAL (ExponentPushToken)');
        console.log('✅ SUCESSO! Token real obtido');
      } else if (token.startsWith('tecbiz_dev_')) {
        console.log('❌ TIPO: TOKEN SIMULADO (tecbiz_dev)');
        console.log('💀 FALHA! Sistema caiu no fallback');
      } else {
        console.log('⚠️ TIPO: TOKEN DESCONHECIDO');
        console.log('🔍 Formato:', token.substring(0, 50));
      }
      
      // Verificar storage
      const { nativeStorage } = await import('./src/utils/nativeStorage');
      const storedToken = await nativeStorage.getItem('push_token');
      const tokenType = await nativeStorage.getItem('token_type');
      
      console.log('');
      console.log('💾 DADOS NO STORAGE:');
      console.log('Token salvo:', storedToken);
      console.log('Tipo salvo:', tokenType);
      
    } else {
      console.log('❌ NENHUM TOKEN OBTIDO');
      console.log('💀 FALHA TOTAL');
    }
    
  } catch (error) {
    console.log('❌ ERRO NO TESTE:', error);
  }
}

// Executar teste
testarToken();

console.log('');
console.log('📋 INSTRUÇÕES:');
console.log('1. Execute este código no console do app');
console.log('2. Aguarde o resultado');
console.log('3. Se mostrar "TOKEN REAL", está funcionando');
console.log('4. Se mostrar "TOKEN SIMULADO", ainda há problema');
console.log('5. Verifique os logs detalhados acima');

// Teste adicional - verificar ambiente
console.log('');
console.log('🔍 INFORMAÇÕES DO AMBIENTE:');
import Constants from 'expo-constants';
console.log('Execution Environment:', Constants.executionEnvironment);
console.log('App Ownership:', Constants.appOwnership);
console.log('Is Device:', require('expo-device').isDevice);
console.log('Platform:', require('react-native').Platform.OS);
