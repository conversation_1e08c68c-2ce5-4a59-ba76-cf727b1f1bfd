// Componente de ícone de notificação com badge
import React, { useState, useEffect } from 'react';
import { TouchableOpacity, Text, View, StyleSheet } from 'react-native';
import pushNotificationService from '../services/pushNotificationService';
import { notificationService } from '../services/notificationService';

interface NotificationIconProps {
  onPress: () => void;
  size?: number;
  color?: string;
}

export const NotificationIcon: React.FC<NotificationIconProps> = ({
  onPress,
  size = 24,
  color = '#007bff'
}) => {
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    loadUnreadCount();

    // Atualizar a cada 5 segundos para resposta mais rápida
    const interval = setInterval(loadUnreadCount, 5000);

    return () => clearInterval(interval);
  }, []);

  // Atualizar quando o componente recebe foco
  useEffect(() => {
    const focusListener = () => {
      console.log('🔔 NotificationIcon recebeu foco - atualizando contador');
      loadUnreadCount();
    };

    // Simular evento de foco (em um app real, usaria navigation listeners)
    const focusInterval = setInterval(focusListener, 2000);

    return () => clearInterval(focusInterval);
  }, []);

  const loadUnreadCount = async () => {
    try {
      let finalCount = 0;

      if (pushNotificationService.isServiceInitialized()) {
        finalCount = pushNotificationService.getUnreadCount();
      }

      setUnreadCount(finalCount);
    } catch (error) {
      setUnreadCount(0);
    }
  };

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      {/* Ícone de sino (usando texto como ícone simples) */}
      <Text style={[styles.icon, { fontSize: size, color }]}>🔔</Text>
      
      {/* Badge com contador */}
      {unreadCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>
            {unreadCount > 99 ? '99+' : unreadCount.toString()}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 8,
  },
  icon: {
    textAlign: 'center',
  },
  badge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default NotificationIcon;
