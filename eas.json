{"cli": {"version": ">= 16.17.0", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "credentialsSource": "local"}, "ios": {"simulator": true, "credentialsSource": "remote"}}, "test": {"distribution": "internal", "android": {"buildType": "apk", "credentialsSource": "local"}, "ios": {"simulator": false, "credentialsSource": "remote"}}, "production": {"android": {"buildType": "app-bundle", "credentialsSource": "remote"}, "ios": {"credentialsSource": "remote"}, "autoIncrement": false}, "production-aab": {"extends": "production", "android": {"buildType": "app-bundle", "credentialsSource": "local"}}}, "submit": {"production": {}}}