{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"strict": true, "jsx": "react-native", "target": "esnext", "module": "esnext", "lib": ["ESNext", "DOM"], "allowJs": true, "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "skipLibCheck": true}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}