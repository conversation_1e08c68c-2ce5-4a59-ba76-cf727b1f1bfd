// Storage wrapper - AsyncStorage removido, usando apenas storage em memória

// AsyncStorage removido completamente
let isAsyncStorageAvailable = false;

// Storage em memória
let memoryStorage: { [key: string]: string } = {};

// Interface do Storage
interface StorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Implementação do Storage usando apenas memória
class StorageWrapper implements StorageInterface {
  async getItem(key: string): Promise<string | null> {
    try {
      // Usar apenas memória
      return memoryStorage[key] || null;
    } catch (error) {
      console.log(`❌ Erro ao obter item ${key}:`, error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      // Salvar apenas em memória
      memoryStorage[key] = value;
    } catch (error) {
      console.log(`❌ Erro ao salvar item ${key}:`, error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      // Remove apenas da memória
      delete memoryStorage[key];
    } catch (error) {
      console.log(`❌ Erro ao remover item ${key}:`, error);
    }
  }

  async clear(): Promise<void> {
    try {
      // Limpa apenas memória
      memoryStorage = {};
    } catch (error) {
      console.log('❌ Erro ao limpar storage:', error);
    }
  }

  // Método para verificar se AsyncStorage real está disponível
  isRealStorageAvailable(): boolean {
    return false; // AsyncStorage removido
  }

  // Método para obter tipo de storage em uso
  getStorageType(): string {
    return 'Memory'; // Sempre memória agora
  }
}

// Inst�ncia singleton
export const storage = new StorageWrapper();

// Export para compatibilidade
export default storage;

// Logs informativos
console.log(`💾 Storage inicializado: ${storage.getStorageType()}`);
console.log('⚠️ Usando storage em memória - dados serão perdidos ao fechar o app');
