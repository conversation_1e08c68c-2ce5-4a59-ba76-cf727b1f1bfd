import { create } from 'zustand';
import { ApiResponse, UsuarioData, SinteticoData, atualizarDadosUsuario } from '../services/api';

interface UserState {
  // Dados do usuário
  userData: ApiResponse | null;
  usuarioData: UsuarioData | null;
  sinteticoData: SinteticoData | null;
  
  // Estados específicos para reatividade
  statusCartao: 'L' | 'B' | 'C' | null; // L=Liberado, B=Bloqueado, C=Cancelado
  saldoMensal: string;
  saldoTotal: string;
  limiteMensal: string;
  limiteTotal: string;
  
  // Estados de controle
  isLoading: boolean;
  lastUpdated: number;
  
  // Ações
  setUserData: (data: ApiResponse) => void;
  updateStatusCartao: (status: 'L' | 'B' | 'C') => void;
  updateSaldos: (saldoMensal: string, saldoTotal?: string, limiteMensal?: string, limiteTotal?: string) => void;
  refreshUserData: () => Promise<boolean>;
  clearUserData: () => void;
}

export const useUserStore = create<UserState>((set, get) => ({
  // Estado inicial
  userData: null,
  usuarioData: null,
  sinteticoData: null,
  statusCartao: null,
  saldoMensal: '0',
  saldoTotal: '0',
  limiteMensal: '0',
  limiteTotal: '0',
  isLoading: false,
  lastUpdated: 0,

  // Ação para definir dados do usuário (login)
  setUserData: (data: ApiResponse) => {
    // Extrair dados do usuário
    const usuarioItem = Array.isArray(data)
      ? data.find(item => 'usuario' in item) as UsuarioData | undefined
      : null;

    // Extrair dados sintéticos (saldo)
    const sinteticoItem = Array.isArray(data)
      ? data.find(item => 'sintetico' in item) as SinteticoData | undefined
      : null;

    set({
      userData: data,
      usuarioData: usuarioItem || null,
      sinteticoData: sinteticoItem || null,
      statusCartao: usuarioItem?.usuario?.sitcar as 'L' | 'B' | 'C' || null,
      saldoMensal: sinteticoItem?.sintetico?.saldo_mensal || '0',
      saldoTotal: sinteticoItem?.sintetico?.saldo_total || '0',
      limiteMensal: sinteticoItem?.sintetico?.limite_mensal || '0',
      limiteTotal: sinteticoItem?.sintetico?.limite_total || '0',
      lastUpdated: Date.now(),
    });
  },

  // Ação para atualizar apenas o status do cartão
  updateStatusCartao: (status: 'L' | 'B' | 'C') => {
    set({
      statusCartao: status,
      lastUpdated: Date.now(),
    });
    
    // Também atualiza no userData se existir
    const { userData, usuarioData } = get();
    if (userData && usuarioData) {
      const updatedUsuarioData = {
        ...usuarioData,
        usuario: {
          ...usuarioData.usuario,
          sitcar: status,
        },
      };
      
      const updatedUserData = Array.isArray(userData) 
        ? userData.map(item => 
            'usuario' in item ? updatedUsuarioData : item
          )
        : userData;
      
      set({ 
        userData: updatedUserData,
        usuarioData: updatedUsuarioData,
      });
    }
  },

  // Ação para atualizar saldos
  updateSaldos: (saldoMensal: string, saldoTotal?: string, limiteMensal?: string, limiteTotal?: string) => {
    set({
      saldoMensal,
      saldoTotal: saldoTotal || get().saldoTotal,
      limiteMensal: limiteMensal || get().limiteMensal,
      limiteTotal: limiteTotal || get().limiteTotal,
      lastUpdated: Date.now(),
    });
  },

  // Ação para atualizar dados do servidor
  refreshUserData: async (): Promise<boolean> => {
    const { usuarioData } = get();
    
    if (!usuarioData?.usuario?.codass || !usuarioData?.usuario?.codent) {
      console.log('❌ Zustand: Dados insuficientes para atualização');
      return false;
    }

    set({ isLoading: true });

    try {
      console.log('🔄 Zustand: Buscando dados atualizados do servidor...');
      const response = await atualizarDadosUsuario(
        usuarioData.usuario.codass,
        usuarioData.usuario.codent
      );

      if (response.success && response.data) {
        // Usa a própria ação setUserData para atualizar tudo
        get().setUserData(response.data);
        console.log('✅ Zustand: Dados atualizados com sucesso do servidor');
        return true;
      } else {
        console.log('❌ Zustand: Falha ao atualizar dados:', response.message);
        return false;
      }
    } catch (error) {
      console.log('❌ Zustand: Erro ao atualizar dados:', error);
      return false;
    } finally {
      set({ isLoading: false });
    }
  },

  // Ação para limpar dados (logout)
  clearUserData: () => {
    console.log('🧹 Zustand: Limpando dados do usuário');
    set({
      userData: null,
      usuarioData: null,
      sinteticoData: null,
      statusCartao: null,
      saldoMensal: '0',
      saldoTotal: '0',
      limiteMensal: '0',
      limiteTotal: '0',
      isLoading: false,
      lastUpdated: 0,
    });
  },
}));

// Seletores úteis para componentes
export const useStatusCartao = () => useUserStore(state => state.statusCartao);
export const useSaldos = () => useUserStore(state => ({
  saldoMensal: state.saldoMensal,
  saldoTotal: state.saldoTotal,
  limiteMensal: state.limiteMensal,
  limiteTotal: state.limiteTotal,
}));
export const useUsuarioData = () => useUserStore(state => state.usuarioData);
export const useIsLoading = () => useUserStore(state => state.isLoading);
