#!/usr/bin/env node

/**
 * SCRIPT PARA CONFIGURAR FCM NO EAS
 * Execute: node configurar_fcm_eas.js
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🔧 CONFIGURANDO FCM PARA EAS BUILD');
console.log('==================================\n');

function log(message, type = 'info') {
    const colors = {
        info: '\x1b[36m',
        success: '\x1b[32m',
        error: '\x1b[31m',
        warning: '\x1b[33m',
        reset: '\x1b[0m'
    };
    
    const color = colors[type] || colors.info;
    const timestamp = new Date().toLocaleTimeString();
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

try {
    // 1. Verificar se está logado no Expo
    log('🔍 Verificando login no Expo...', 'info');
    
    try {
        const whoami = execSync('npx expo whoami', { encoding: 'utf8' }).trim();
        log(`✅ Logado como: ${whoami}`, 'success');
    } catch (error) {
        log('❌ Não está logado no Expo', 'error');
        log('💡 Execute: npx expo login', 'warning');
        process.exit(1);
    }
    
    // 2. Verificar google-services.json
    log('📁 Verificando google-services.json...', 'info');
    
    if (!fs.existsSync('google-services.json')) {
        log('❌ Arquivo google-services.json não encontrado', 'error');
        process.exit(1);
    }
    
    const googleServices = JSON.parse(fs.readFileSync('google-services.json', 'utf8'));
    const serverKey = googleServices.client[0].api_key[0].current_key;
    
    log(`✅ Arquivo encontrado`, 'success');
    log(`🔑 Chave FCM: ${serverKey.substring(0, 20)}...`, 'info');
    
    // 3. Configurar FCM no EAS
    log('🚀 Configurando FCM no EAS...', 'warning');
    
    try {
        // Comando para configurar FCM
        const command = `npx expo push:android:upload --api-key ${serverKey}`;
        log(`📤 Executando: ${command}`, 'info');
        
        const result = execSync(command, { 
            encoding: 'utf8',
            stdio: 'pipe'
        });
        
        log('✅ FCM configurado com sucesso no EAS!', 'success');
        log(`📋 Resultado: ${result.trim()}`, 'info');
        
    } catch (error) {
        log('⚠️ Erro ao configurar FCM, tentando método alternativo...', 'warning');
        
        // Método alternativo: usar eas credentials
        try {
            log('🔄 Tentando configurar via EAS credentials...', 'info');
            
            const credentialsCommand = `npx eas credentials:configure --platform android`;
            log(`📤 Execute manualmente: ${credentialsCommand}`, 'warning');
            log('💡 Quando perguntado, escolha "Google Service Account Key"', 'info');
            log(`💡 Use a chave: ${serverKey}`, 'info');
            
        } catch (credError) {
            log('❌ Erro no método alternativo', 'error');
        }
    }
    
    // 4. Verificar configuração
    log('🔍 Verificando configuração...', 'info');
    
    try {
        const showResult = execSync('npx expo push:android:show', { 
            encoding: 'utf8',
            stdio: 'pipe'
        });
        
        if (showResult.includes('FCM server key')) {
            log('✅ FCM configurado corretamente!', 'success');
            log(`📋 Configuração: ${showResult.trim()}`, 'info');
        } else {
            log('⚠️ FCM pode não estar configurado corretamente', 'warning');
        }
        
    } catch (error) {
        log('⚠️ Não foi possível verificar a configuração', 'warning');
    }
    
    // 5. Instruções finais
    console.log('\n' + '='.repeat(60));
    log('🎯 CONFIGURAÇÃO CONCLUÍDA!', 'success');
    
    console.log('\n📋 PRÓXIMOS PASSOS:');
    log('1. Fazer novo build EAS:', 'info');
    log('   eas build --platform android --profile preview --clear-cache', 'warning');
    
    log('2. Testar push notification:', 'info');
    log('   php test_send_notification.php', 'warning');
    
    log('3. Se ainda não funcionar:', 'info');
    log('   - Aguardar build completar', 'info');
    log('   - Instalar novo APK', 'info');
    log('   - Fazer login novamente', 'info');
    log('   - Obter novo token', 'info');
    log('   - Testar novamente', 'info');
    
    console.log('\n🔑 INFORMAÇÕES IMPORTANTES:');
    log(`📱 Package: com.tecbiz.tecbizassociadospush`, 'info');
    log(`🆔 Project ID: tecbizappass`, 'info');
    log(`🔑 FCM Key: ${serverKey.substring(0, 20)}...`, 'info');
    
    console.log('\n🎉 CONFIGURAÇÃO FINALIZADA!');
    
} catch (error) {
    log(`💥 ERRO CRÍTICO: ${error.message}`, 'error');
    console.log('\n🔧 SOLUÇÕES ALTERNATIVAS:');
    log('1. Configurar manualmente via Expo Dashboard', 'info');
    log('2. Usar eas credentials:configure', 'info');
    log('3. Verificar se está logado: npx expo whoami', 'info');
    process.exit(1);
}
