import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  Dimensions,
  ScrollView,
} from 'react-native';
import Cores from '../constants/Cores';
import SafeHeader from '../components/SafeHeader';
import Footer from '../components/Footer';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import { HOSTS } from '../config/hosts';

const { width } = Dimensions.get('window');

interface CartaoVirtualScreenProps {
  navigation: {
    goBack: () => void;
    navigate: (screen: 'Login' | 'Home' | 'Extrato' | 'CartaoVirtual' | 'Saldo') => void;
  };
}

const CartaoVirtualScreen: React.FC<CartaoVirtualScreenProps> = ({ navigation }) => {
  const { getUsuarioData } = useAppContext();
  const [cartaoVisivel, setCartaoVisivel] = useState(false);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [logoCarregado, setLogoCarregado] = useState(false);
  const [erroLogo, setErroLogo] = useState(false);

  const usuarioData = getUsuarioData();

  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack());

  useEffect(() => {
    const carregarLogo = async () => {
      if (usuarioData?.usuario?.codent) {
        // Resetar estados de erro
        setErroLogo(false);
        setLogoCarregado(false);

        console.log('🖼️ Iniciando carregamento do logo para codent:', usuarioData.usuario.codent);

        // Testar diferentes URLs para encontrar o logo
        const urlEncontrada = await testarUrlsLogo(usuarioData.usuario.codent);

        if (urlEncontrada) {
          setLogoUrl(urlEncontrada);
        } else {
          // Fallback para URL padrão
          const baseUrl = HOSTS.HOST_QUENTE.replace('tecbiz.php', '');
          const logoPath = `figuras/LOGOTIPO_ETD${usuarioData.usuario.codent}.JPG`;
          const fallbackUrl = baseUrl + logoPath;

          console.log('⚠️ Usando URL fallback:', fallbackUrl);
          setLogoUrl(fallbackUrl);
        }
      }
    };

    carregarLogo();
  }, [usuarioData]);

  const toggleCartaoVisibilidade = () => {
    setCartaoVisivel(!cartaoVisivel);
  };

  const formatarCartao = (cartao: string) => {
    if (!cartao) return '#### #### #### ####';
    return cartao.replace(/(\d{4})(?=\d)/g, '$1 ');
  };

  const obterSituacaoCartao = () => {
    if (!usuarioData?.usuario?.sitcar) return null;
    
    if (usuarioData.usuario.sitcar !== 'L') {
      let situacao = 'CANCELADO';
      if (usuarioData.usuario.sitcar === 'B') {
        situacao = 'BLOQUEADO';
      }
      return `CARTÃO ${situacao}`;
    }
    return null;
  };

  const gerarQRCodeUrl = () => {
    if (!usuarioData?.usuario?.cartaoFormatado) return '';
    return `https://quickchart.io/qr?text=${encodeURIComponent(usuarioData.usuario.cartaoFormatado)}`;
  };

  // Função para testar diferentes URLs de logo
  const testarUrlsLogo = async (codent: string) => {
    const urlsParaTestar = [
      `http://www2.tecbiz.com.br/tecbiz/figuras/LOGOTIPO_ETD${codent}.JPG`,
      `http://www2.tecbiz.com.br/tecbiz/figuras/LOGOTIPO_ETD${codent}.jpg`,
      `http://www2.tecbiz.com.br/tecbiz/figuras/logotipo_etd${codent}.jpg`,
      `http://www2.tecbiz.com.br/figuras/LOGOTIPO_ETD${codent}.JPG`,
    ];

    console.log('🔍 Testando URLs de logo para codent:', codent);

    for (const url of urlsParaTestar) {
      try {
        console.log('🌐 Testando URL:', url);
        const response = await fetch(url, { method: 'HEAD' });
        if (response.ok) {
          console.log('✅ URL encontrada:', url);
          return url;
        }
      } catch (error) {
        console.log('❌ URL falhou:', url, error);
      }
    }

    console.log('⚠️ Nenhuma URL de logo funcionou para codent:', codent);
    return null;
  };

  if (!usuarioData) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="Cartão Virtual"
          onBackPress={() => navigation.goBack()}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Dados do usuário não encontrados</Text>
        </View>
      </View>
    );
  }

  const situacaoCartao = obterSituacaoCartao();

  return (
    <View style={styles.container}>
      <SafeHeader
        title="Cartão Virtual"
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Cartão Virtual */}
        <View style={styles.cartaoContainer}>
          <View style={styles.cartao}>
            {/* Header do Cartão */}
            <View style={styles.cartaoHeader}>
              <Text style={styles.cartaoTitulo}>TecBiz Associado</Text>
            </View>

            {/* Logo e Entidade Centralizados */}
            <View style={styles.cartaoBody}>
              <View style={styles.logoEntidadeContainer}>
                {logoUrl && !erroLogo ? (
                  <Image
                    source={{ uri: logoUrl }}
                    style={styles.logoEmpresa}
                    resizeMode="contain"
                    onError={(error) => {
                      console.log('❌ Erro ao carregar logo:', error.nativeEvent.error);
                      console.log('🔗 URL que falhou:', logoUrl);
                      setErroLogo(true);
                      setLogoCarregado(false);
                    }}
                    onLoad={() => {
                      console.log('✅ Logo carregado com sucesso:', logoUrl);
                      setLogoCarregado(true);
                      setErroLogo(false);
                    }}
                  />
                ) : (
                  <View style={styles.logoPlaceholder}>
                    <Text style={styles.logoText}>
                      {erroLogo ? 'Logo\nIndisponível' : 'TecBiz'}
                    </Text>
                  </View>
                )}
                <Text style={styles.entidadeNomeCentralizado}>{usuarioData.usuario.etd}</Text>
              </View>

              {/* Informações do Portador */}
              <View style={styles.infoContainer}>
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Nome:</Text>
                  <Text style={styles.infoValue}>{usuarioData.usuario.portador}</Text>
                </View>
                
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Matr.:</Text>
                  <Text style={styles.infoValue}>{usuarioData.usuario.matricula}</Text>
                </View>
                
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Cartão:</Text>
                  <Text style={styles.infoValue}>
                    {cartaoVisivel 
                      ? formatarCartao(usuarioData.usuario.cartaoFormatado)
                      : '#### #### #### ####'
                    }
                  </Text>
                  <TouchableOpacity 
                    style={styles.eyeButton}
                    onPress={toggleCartaoVisibilidade}
                  >
                    <Text style={styles.eyeIcon}>
                      {cartaoVisivel ? '🚫' : '👁'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>

          {/* Status do Cartão */}
          {situacaoCartao && (
            <View style={styles.situacaoContainer}>
              <Text style={styles.situacaoTexto}>{situacaoCartao}</Text>
            </View>
          )}
        </View>

        {/* QR Code */}
        <View style={styles.qrContainer}>
          <Text style={styles.qrTitulo}>QR Code do Cartão</Text>
          <View style={styles.qrCodeContainer}>
            {usuarioData.usuario.cartaoFormatado ? (
              <Image 
                source={{ uri: gerarQRCodeUrl() }}
                style={styles.qrCode}
                resizeMode="contain"
              />
            ) : (
              <View style={styles.qrPlaceholder}>
                <Text style={styles.qrPlaceholderText}>QR Code indisponível</Text>
              </View>
            )}
          </View>
          <Text style={styles.qrDescricao}>
            Use este QR Code para identificação rápida
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <Footer currentScreen="CartaoVirtual" navigation={navigation} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  cartaoContainer: {
    marginBottom: 30,
  },
  cartao: {
    backgroundColor: 'white',
    borderRadius: 15,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  cartaoHeader: {
    backgroundColor: Cores.primaria,
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  cartaoTitulo: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cartaoBody: {
    padding: 20,
  },
  logoEntidadeContainer: {
    alignItems: 'center',
    marginBottom: 25,
    paddingVertical: 15,
  },
  logoEmpresa: {
    width: 120,
    height: 80,
    marginBottom: 15,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
  },
  logoPlaceholder: {
    width: 120,
    height: 80,
    backgroundColor: Cores.bordaClara,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: Cores.bordaMedia,
    borderStyle: 'dashed',
  },
  logoText: {
    color: Cores.textoMedio,
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 16,
  },
  entidadeNomeCentralizado: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    textAlign: 'center',
    paddingHorizontal: 10,
    lineHeight: 22,
  },
  infoContainer: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    minWidth: 60,
  },
  infoValue: {
    fontSize: 14,
    color: Cores.textoEscuro,
    flex: 1,
    marginLeft: 10,
  },
  eyeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: Cores.primaria + '20',
  },
  eyeIcon: {
    fontSize: 18,
  },
  situacaoContainer: {
    marginTop: 15,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  situacaoTexto: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  qrContainer: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  qrTitulo: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 15,
  },
  qrCodeContainer: {
    backgroundColor: '#f8f8f8',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  qrCode: {
    width: 150,
    height: 150,
  },
  qrPlaceholder: {
    width: 150,
    height: 150,
    backgroundColor: Cores.bordaClara,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  qrPlaceholderText: {
    color: Cores.textoMedio,
    fontSize: 12,
    textAlign: 'center',
  },
  qrDescricao: {
    fontSize: 14,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
});

export default CartaoVirtualScreen;
