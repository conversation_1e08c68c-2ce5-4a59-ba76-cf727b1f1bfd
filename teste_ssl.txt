SSL Report: www2.tecbiz.com.br (**************)
Assessed on:  Mon, 04 Aug 2025 18:00:39 UTC | Hide | Clear cacheScan Another »
Summary
Overall Rating
A+
020406080100
Certificate 
Protocol Support 
Key Exchange 
Cipher Strength 

Visit our documentation page for more information, configuration guides, and books. Known issues are documented here.
This server supports TLS 1.3.  MORE INFO »
HTTP Strict Transport Security (HSTS) with long duration deployed on this server.  MORE INFO »
Certificate #1: RSA 2048 bits (SHA256withRSA)

Server Key and Certificate #1
Subject	*.tecbiz.com.br
Fingerprint SHA256: ce733c39d8607fc0d5c0fa01ada808c1da8386af69ea73d84a9a0d90c4157206
Pin SHA256: y5Lv46DFNV0/xKzrpVLvRIDbecdVu72W146aVfTaTuo=
Common names	*.tecbiz.com.br
Alternative names	*.tecbiz.com.br tecbiz.com.br
Serial Number	64ee2517b56136d4ae4c62b6
Valid from	Thu, 01 Aug 2024 03:57:23 UTC
Valid until	Tue, 02 Sep 2025 03:57:22 UTC (expires in 28 days, 9 hours)
Key	RSA 2048 bits (e 65537)
Weak key (Debian)	No
Issuer	GlobalSign GCC R6 AlphaSSL CA 2023
AIA: http://secure.globalsign.com/cacert/gsgccr6alphasslca2023.crt
Signature algorithm	SHA256withRSA
Extended Validation	No
Certificate Transparency	Yes (certificate)
OCSP Must Staple	No
Revocation information	CRL, OCSP
CRL: http://crl.globalsign.com/gsgccr6alphasslca2023.crl
OCSP: http://ocsp.globalsign.com/gsgccr6alphasslca2023
Revocation status	Good (not revoked)
DNS CAA	No (more info)
Trusted	Yes
Mozilla  Apple  Android  Java  Windows 



Additional Certificates (if supplied)
Certificates provided	2 (3060 bytes)
Chain issues	None
#2
Subject	GlobalSign GCC R6 AlphaSSL CA 2023
Fingerprint SHA256: e46fb2a75097a345d4246dcf44a10daa71d9fd0ebfab61ba67e6db84ee5b6cab
Pin SHA256: JdFERRONSeokpPRwHKoZgZPPGO+7YwoMHGHoe1BAq3c=
Valid until	Sun, 19 Jul 2026 00:00:00 UTC (expires in 11 months and 14 days)
Key	RSA 2048 bits (e 65537)
Issuer	GlobalSign
Signature algorithm	SHA256withRSA



Show Certification PathsCertification Paths
Click here to expand

Configuration

Protocols
TLS 1.3	Yes
TLS 1.2	Yes
TLS 1.1	No
TLS 1.0	No
SSL 3	No
SSL 2	No



Cipher Suites
# TLS 1.3 (suites in server-preferred order)
TLS_AES_256_GCM_SHA384 (0x1302)   ECDH x25519 (eq. 3072 bits RSA)   FS	256
TLS_CHACHA20_POLY1305_SHA256 (0x1303)   ECDH x25519 (eq. 3072 bits RSA)   FS	256
TLS_AES_128_GCM_SHA256 (0x1301)   ECDH x25519 (eq. 3072 bits RSA)   FS	128
# TLS 1.2 (suites in server-preferred order)
TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 (0xc030)   ECDH x25519 (eq. 3072 bits RSA)   FS	256
TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256 (0xc02f)   ECDH x25519 (eq. 3072 bits RSA)   FS	128
TLS_DHE_RSA_WITH_AES_256_GCM_SHA384 (0x9f)   DH 2048 bits   FS	256
TLS_DHE_RSA_WITH_AES_128_GCM_SHA256 (0x9e)   DH 2048 bits   FS	128
TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384 (0xc028)   ECDH x25519 (eq. 3072 bits RSA)   FS   WEAK	256
TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA (0xc014)   ECDH x25519 (eq. 3072 bits RSA)   FS   WEAK	256
TLS_DHE_RSA_WITH_AES_256_CCM_8 (0xc0a3)   DH 2048 bits   FS	256
TLS_DHE_RSA_WITH_AES_256_CCM (0xc09f)   DH 2048 bits   FS	256
TLS_DHE_RSA_WITH_AES_256_CBC_SHA256 (0x6b)   DH 2048 bits   FS   WEAK	256
TLS_DHE_RSA_WITH_AES_256_CBC_SHA (0x39)   DH 2048 bits   FS   WEAK	256



Handshake Simulation
Android 4.4.2	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Android 5.0.0	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256   ECDH secp256r1  FS
Android 6.0	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256   ECDH secp256r1  FS
Android 7.0	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Android 8.0	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Android 8.1	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Android 9.0	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
BingPreview Jan 2015	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Chrome 49 / XP SP3	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256   ECDH secp256r1  FS
Chrome 69 / Win 7  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Chrome 70 / Win 10	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Chrome 80 / Win 10  R	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Firefox 31.3.0 ESR / Win 7	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256   ECDH secp256r1  FS
Firefox 47 / Win 7  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256   ECDH secp256r1  FS
Firefox 49 / XP SP3	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Firefox 62 / Win 7  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Firefox 73 / Win 10  R	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Googlebot Feb 2018	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
IE 11 / Win 7  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_DHE_RSA_WITH_AES_256_GCM_SHA384   DH 2048  FS
IE 11 / Win 8.1  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_DHE_RSA_WITH_AES_256_GCM_SHA384   DH 2048  FS
IE 11 / Win Phone 8.1  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA   ECDH secp256r1  FS
IE 11 / Win Phone 8.1 Update  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_DHE_RSA_WITH_AES_256_GCM_SHA384   DH 2048  FS
IE 11 / Win 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Edge 15 / Win 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Edge 16 / Win 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Edge 18 / Win 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
Edge 13 / Win Phone 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Java 8u161	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Java 11.0.3	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH secp256r1  FS
Java 12.0.1	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH secp256r1  FS
OpenSSL 1.0.1l  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
OpenSSL 1.0.2s  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
OpenSSL 1.1.0k  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH x25519  FS
OpenSSL 1.1.1c  R	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Safari 6 / iOS 6.0.1	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384   ECDH secp256r1  FS
Safari 7 / iOS 7.1  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384   ECDH secp256r1  FS
Safari 7 / OS X 10.9  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384   ECDH secp256r1  FS
Safari 8 / iOS 8.4  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384   ECDH secp256r1  FS
Safari 8 / OS X 10.10  R	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384   ECDH secp256r1  FS
Safari 9 / iOS 9  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Safari 9 / OS X 10.11  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Safari 10 / iOS 10  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Safari 10 / OS X 10.12  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Safari 12.1.2 / MacOS 10.14.6 Beta  R	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Safari 12.1.1 / iOS 12.3.1  R	-  	TLS 1.3	TLS_AES_256_GCM_SHA384   ECDH x25519  FS
Apple ATS 9 / iOS 9  R	RSA 2048 (SHA256)  	TLS 1.2 > http/1.1  	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
Yahoo Slurp Jan 2015	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
YandexBot Jan 2015	RSA 2048 (SHA256)  	TLS 1.2	TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384   ECDH secp256r1  FS
# Not simulated clients (Protocol mismatch)
Android 2.3.7   No SNI 2	Protocol mismatch (not simulated)
Android 4.0.4	Protocol mismatch (not simulated)
Android 4.1.1	Protocol mismatch (not simulated)
Android 4.2.2	Protocol mismatch (not simulated)
Android 4.3	Protocol mismatch (not simulated)
Baidu Jan 2015	Protocol mismatch (not simulated)
IE 6 / XP   No FS 1   No SNI 2	Protocol mismatch (not simulated)
IE 7 / Vista	Protocol mismatch (not simulated)
IE 8 / XP   No FS 1   No SNI 2	Protocol mismatch (not simulated)
IE 8-10 / Win 7  R	Protocol mismatch (not simulated)
IE 10 / Win Phone 8.0	Protocol mismatch (not simulated)
Java 6u45   No SNI 2	Protocol mismatch (not simulated)
Java 7u25	Protocol mismatch (not simulated)
OpenSSL 0.9.8y	Protocol mismatch (not simulated)
Safari 5.1.9 / OS X 10.6.8	Protocol mismatch (not simulated)
Safari 6.0.4 / OS X 10.8.4  R	Protocol mismatch (not simulated)
(1) Clients that do not support Forward Secrecy (FS) are excluded when determining support for it.
(2) No support for virtual SSL hosting (SNI). Connects to the default site if the server uses SNI.
(3) Only first connection attempt simulated. Browsers sometimes retry with a lower protocol version.
(R) Denotes a reference browser or client, with which we expect better effective security.
(All) We use defaults, but some platforms do not use their best protocols and features (e.g., Java 6 & 7, older IE).
(All) Certificate trust is not checked in handshake simulation, we only perform TLS handshake.



Protocol Details
Secure Renegotiation	Supported
Secure Client-Initiated Renegotiation	No
Insecure Client-Initiated Renegotiation	No
BEAST attack	Mitigated server-side (more info)  
POODLE (SSLv3)	No, SSL 3 not supported (more info)
POODLE (TLS)	No (more info)
Zombie POODLE	No (more info)   TLS 1.2 : 0xc014
GOLDENDOODLE	No (more info)   TLS 1.2 : 0xc014
OpenSSL 0-Length	No (more info)   TLS 1.2 : 0xc014
Sleeping POODLE	No (more info)   TLS 1.2 : 0xc014
Downgrade attack prevention	Yes, TLS_FALLBACK_SCSV supported (more info)
SSL/TLS compression	No
RC4	No
Heartbeat (extension)	No
Heartbleed (vulnerability)	No (more info)
Ticketbleed (vulnerability)	No (more info)
OpenSSL CCS vuln. (CVE-2014-0224)	No (more info)
OpenSSL Padding Oracle vuln.
(CVE-2016-2107)	No (more info)
ROBOT (vulnerability)	No (more info)
Forward Secrecy	Yes (with most browsers)   ROBUST (more info)
ALPN	Yes   http/1.1
NPN	No
Session resumption (caching)	Yes
Session resumption (tickets)	Yes
OCSP stapling	No
Strict Transport Security (HSTS)	Yes
max-age=31536000; includeSubDomains; preload
HSTS Preloading	Not in: Chrome  Edge  Firefox  IE 
Public Key Pinning (HPKP)	No (more info)
Public Key Pinning Report-Only	No
Public Key Pinning (Static)	No (more info)
Long handshake intolerance	No
TLS extension intolerance	No
TLS version intolerance	No
Incorrect SNI alerts	No
Uses common DH primes	No
DH public server param (Ys) reuse	No
ECDH public server param reuse	No
Supported Named Groups	x25519, secp256r1, x448, secp521r1, secp384r1 (server preferred order)
SSL 2 handshake compatibility	No
0-RTT enabled	No



HTTP Requests
1 https://www2.tecbiz.com.br/  (HTTP/1.1 200 OK)



Miscellaneous
Test date	Mon, 04 Aug 2025 17:59:07 UTC
Test duration	91.397 seconds
HTTP status code	200
HTTP server signature	Apache/2.4.62 (Debian)
Server hostname	vps54771.publiccloud.com.br
