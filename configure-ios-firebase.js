#!/usr/bin/env node

/**
 * Script para configurar Firebase para iOS
 * Configura push notifications APNs no Firebase Console
 */

const fs = require('fs');
const path = require('path');

// Cores para logs
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const color = colors[type] || colors.cyan;
  console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

function main() {
  log('🍎 Configuração Firebase para iOS - TecBiz', 'green');
  log('================================================', 'blue');
  
  // 1. Verificar arquivos necessários
  log('📋 Verificando arquivos necessários...', 'yellow');
  
  const requiredFiles = [
    'GoogleService-Info.plist',
    'app.config.js',
    'google-services.json'
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    log(`❌ Arquivos faltando: ${missingFiles.join(', ')}`, 'red');
    return;
  }
  
  log('✅ Todos os arquivos necessários encontrados', 'green');
  
  // 2. Verificar configuração do app.config.js
  log('🔍 Verificando app.config.js...', 'yellow');
  
  const appConfig = fs.readFileSync('app.config.js', 'utf8');
  
  if (appConfig.includes('googleServicesFile: "./GoogleService-Info.plist"')) {
    log('✅ GoogleService-Info.plist configurado no app.config.js', 'green');
  } else {
    log('⚠️ GoogleService-Info.plist não encontrado no app.config.js', 'yellow');
  }
  
  // 3. Verificar bundle identifier
  if (appConfig.includes('bundleIdentifier: "com.tecbiz.tecbizassociadospush"')) {
    log('✅ Bundle identifier correto: com.tecbiz.tecbizassociadospush', 'green');
  } else {
    log('⚠️ Bundle identifier pode estar incorreto', 'yellow');
  }
  
  // 4. Verificar configurações de push notifications
  if (appConfig.includes('UIBackgroundModes: ["remote-notification"]')) {
    log('✅ Background modes configurado para push notifications', 'green');
  } else {
    log('⚠️ Background modes não configurado', 'yellow');
  }
  
  // 5. Instruções para o Firebase Console
  log('', 'reset');
  log('🔥 PRÓXIMOS PASSOS NO FIREBASE CONSOLE:', 'magenta');
  log('================================================', 'blue');
  log('1. Acesse: https://console.firebase.google.com/', 'cyan');
  log('2. Selecione o projeto: tecbizappass', 'cyan');
  log('3. Vá em Project Settings > General', 'cyan');
  log('4. Na seção "Your apps", clique em "Add app" > iOS', 'cyan');
  log('5. Use o Bundle ID: com.tecbiz.tecbizassociadospush', 'cyan');
  log('6. Baixe o GoogleService-Info.plist (substitua o atual se necessário)', 'cyan');
  log('', 'reset');
  
  log('📱 CONFIGURAR APNs (Apple Push Notifications):', 'magenta');
  log('================================================', 'blue');
  log('1. No Firebase Console, vá em Project Settings > Cloud Messaging', 'cyan');
  log('2. Na seção iOS, clique em "Upload" para APNs certificate', 'cyan');
  log('3. Use o certificado APNs que foi criado pelo EAS Build', 'cyan');
  log('4. Ou configure APNs Authentication Key (recomendado)', 'cyan');
  log('', 'reset');
  
  log('🔑 CHAVE APNs JÁ CONFIGURADA PELO EAS:', 'green');
  log('O EAS Build já criou e configurou a chave APNs automaticamente!', 'green');
  log('Você pode verificar em: https://expo.dev/accounts/mauriciocdz07/projects/TecBizExpoApp/credentials', 'cyan');
  log('', 'reset');
  
  log('✅ Configuração iOS pronta para build!', 'green');
  log('Execute: eas build --platform ios --profile production', 'cyan');
}

if (require.main === module) {
  main();
}

module.exports = { main };
