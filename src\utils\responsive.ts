// Utilitários para responsividade
import { Dimensions, PixelRatio } from 'react-native';

// Obter dimensões da tela
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Dimensões de referência (baseado em um dispositivo médio)
const REFERENCE_WIDTH = 375; // iPhone X/11/12 width
const REFERENCE_HEIGHT = 812; // iPhone X/11/12 height

/**
 * Escala horizontal baseada na largura da tela
 */
export const scaleWidth = (size: number): number => {
  return (SCREEN_WIDTH / REFERENCE_WIDTH) * size;
};

/**
 * Escala vertical baseada na altura da tela
 */
export const scaleHeight = (size: number): number => {
  return (SCREEN_HEIGHT / REFERENCE_HEIGHT) * size;
};

/**
 * Escala de fonte responsiva
 */
export const scaleFontSize = (size: number): number => {
  const scale = SCREEN_WIDTH / REFERENCE_WIDTH;
  const newSize = size * scale;
  
  // Limitar o tamanho mínimo e máximo da fonte
  const minSize = size * 0.8;
  const maxSize = size * 1.2;
  
  return Math.max(minSize, Math.min(maxSize, newSize));
};

/**
 * Escala moderada (menos agressiva)
 */
export const moderateScale = (size: number, factor: number = 0.5): number => {
  return size + (scaleWidth(size) - size) * factor;
};

/**
 * Verificar se é uma tela pequena
 */
export const isSmallScreen = (): boolean => {
  return SCREEN_WIDTH < 350;
};

/**
 * Verificar se é uma tela média-pequena (para resolver problemas específicos como 1612x720)
 */
export const isMediumSmallScreen = (): boolean => {
  return SCREEN_WIDTH >= 350 && SCREEN_WIDTH < 380;
};

/**
 * Verificar se é uma tela média
 */
export const isMediumScreen = (): boolean => {
  return SCREEN_WIDTH >= 380 && SCREEN_WIDTH <= 400;
};

/**
 * Verificar se é uma tela grande
 */
export const isLargeScreen = (): boolean => {
  return SCREEN_WIDTH > 400;
};

/**
 * Obter padding horizontal responsivo
 */
export const getResponsivePadding = (): number => {
  if (isSmallScreen()) return 15;
  if (isMediumSmallScreen()) return 18; // Ajuste específico para resoluções como 1612x720
  if (isMediumScreen()) return 20;
  if (isLargeScreen()) return 25;
  return 20;
};

/**
 * Obter tamanho de fonte responsivo para títulos
 */
export const getResponsiveTitleSize = (baseSize: number): number => {
  if (isSmallScreen()) return baseSize - 2;
  if (isMediumSmallScreen()) return baseSize - 1; // Ajuste mais suave para telas médias-pequenas
  if (isMediumScreen()) return baseSize;
  if (isLargeScreen()) return baseSize + 2;
  return baseSize;
};

/**
 * Obter tamanho de fonte responsivo para texto normal
 */
export const getResponsiveTextSize = (baseSize: number): number => {
  if (isSmallScreen()) return baseSize - 1;
  if (isMediumSmallScreen()) return Math.max(baseSize - 0.5, baseSize - 1); // Ajuste mais suave
  if (isMediumScreen()) return baseSize;
  if (isLargeScreen()) return baseSize + 1;
  return baseSize;
};

/**
 * Obter altura mínima responsiva para botões
 */
export const getResponsiveButtonHeight = (): number => {
  if (isSmallScreen()) return 45;
  if (isMediumSmallScreen()) return 48; // Altura intermediária para telas médias-pequenas
  if (isMediumScreen()) return 50;
  if (isLargeScreen()) return 55;
  return 50;
};

/**
 * Obter espaçamento vertical responsivo
 */
export const getResponsiveSpacing = (baseSpacing: number): number => {
  if (isSmallScreen()) return baseSpacing * 0.8;
  if (isMediumSmallScreen()) return baseSpacing * 0.9; // Espaçamento intermediário
  if (isMediumScreen()) return baseSpacing;
  if (isLargeScreen()) return baseSpacing * 1.2;
  return baseSpacing;
};

/**
 * Verificar se precisa de scroll horizontal
 */
export const needsHorizontalScroll = (contentWidth: number): boolean => {
  return contentWidth > SCREEN_WIDTH;
};

/**
 * Obter largura máxima para conteúdo
 */
export const getMaxContentWidth = (): number => {
  return SCREEN_WIDTH - (getResponsivePadding() * 2);
};

/**
 * Informações da tela atual
 */
export const screenInfo = {
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  isSmall: isSmallScreen(),
  isLarge: isLargeScreen(),
  pixelRatio: PixelRatio.get(),
  fontScale: PixelRatio.getFontScale(),
};

/**
 * Breakpoints responsivos
 */
export const breakpoints = {
  small: 350,
  mediumSmall: 380, // Novo breakpoint para resolver problemas específicos
  medium: 400,
  large: 450,
  xlarge: 500,
};

/**
 * Verificar breakpoint atual
 */
export const getCurrentBreakpoint = (): 'small' | 'mediumSmall' | 'medium' | 'large' | 'xlarge' => {
  if (SCREEN_WIDTH <= breakpoints.small) return 'small';
  if (SCREEN_WIDTH <= breakpoints.mediumSmall) return 'mediumSmall';
  if (SCREEN_WIDTH <= breakpoints.medium) return 'medium';
  if (SCREEN_WIDTH <= breakpoints.large) return 'large';
  return 'xlarge';
};

/**
 * Obter configurações responsivas para grid
 */
export const getGridConfig = (itemsPerRow: number = 3) => {
  const padding = getResponsivePadding();
  const gap = isSmallScreen() ? 8 : 12;
  const availableWidth = SCREEN_WIDTH - (padding * 2) - (gap * (itemsPerRow - 1));
  const itemWidth = availableWidth / itemsPerRow;
  
  return {
    itemWidth,
    gap,
    padding,
    itemsPerRow: itemWidth < 80 ? Math.max(2, itemsPerRow - 1) : itemsPerRow,
  };
};

/**
 * Obter estilos responsivos para texto
 */
export const getResponsiveTextStyles = (baseSize: number) => ({
  fontSize: getResponsiveTextSize(baseSize),
  lineHeight: getResponsiveTextSize(baseSize) * 1.4,
});

/**
 * Obter estilos responsivos para containers
 */
export const getResponsiveContainerStyles = () => ({
  paddingHorizontal: getResponsivePadding(),
  paddingVertical: getResponsiveSpacing(20),
});

/**
 * Detectar se a resolução pode causar problemas de layout
 * Especialmente útil para resoluções próximas como 1640x720 vs 1612x720
 */
export const isProblematicResolution = (): boolean => {
  // Detectar resoluções que podem causar problemas específicos
  const problematicWidths = [
    { min: 1610, max: 1620 }, // Faixa problemática identificada
    { min: 360, max: 370 },   // Outras faixas que podem causar problemas
  ];

  return problematicWidths.some(range =>
    SCREEN_WIDTH >= range.min && SCREEN_WIDTH <= range.max
  );
};

/**
 * Obter ajustes específicos para resoluções problemáticas
 */
export const getProblematicResolutionAdjustments = () => {
  if (!isProblematicResolution()) return {};

  return {
    // Ajustes mais conservadores para evitar quebras
    fontSize: -1,
    padding: -2,
    margin: -1,
    // Forçar uso de flexShrink e ajustes automáticos
    flexAdjustments: {
      flexShrink: 1,
      minWidth: 0,
      overflow: 'hidden' as 'hidden',
    }
  };
};

export default {
  scaleWidth,
  scaleHeight,
  scaleFontSize,
  moderateScale,
  isSmallScreen,
  isMediumSmallScreen,
  isMediumScreen,
  isLargeScreen,
  getResponsivePadding,
  getResponsiveTitleSize,
  getResponsiveTextSize,
  getResponsiveButtonHeight,
  getResponsiveSpacing,
  needsHorizontalScroll,
  getMaxContentWidth,
  screenInfo,
  breakpoints,
  getCurrentBreakpoint,
  getGridConfig,
  getResponsiveTextStyles,
  getResponsiveContainerStyles,
  isProblematicResolution,
  getProblematicResolutionAdjustments,
};
