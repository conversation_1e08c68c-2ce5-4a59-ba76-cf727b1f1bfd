import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView } from 'react-native';
import { usePushNotificationStore } from '../store/pushNotificationStore';
import { robustStorage } from '../utils/robustStorage';
import pushNotificationService from '../services/pushNotificationService';

interface TokenDebugInfoProps {
  visible?: boolean;
  onClose?: () => void;
}

interface TokenInfo {
  token: string | null;
  tokenType: string | null;
  isReal: boolean;
  length: number;
  source: string;
  lastUpdated: string | null;
}

const TokenDebugInfo: React.FC<TokenDebugInfoProps> = ({ visible = false, onClose }) => {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const pushStore = usePushNotificationStore();

  useEffect(() => {
    if (visible) {
      loadTokenInfo();
    }
  }, [visible]);

  const loadTokenInfo = async () => {
    try {
      setLoading(true);
      
      // Obter token do store
      const storeToken = pushStore.fcmToken;
      
      // Obter token do storage
      const storageToken = await robustStorage.getItem('push_token');
      const tokenType = await robustStorage.getItem('token_type');
      const isRealFlag = await robustStorage.getItem('token_is_real');
      const lastUpdated = await robustStorage.getItem('token_last_updated');
      
      // Determinar qual token usar
      const finalToken = storeToken || storageToken;
      const isReal = isRealFlag === 'true' || (finalToken && finalToken.startsWith('ExponentPushToken['));
      
      setTokenInfo({
        token: finalToken,
        tokenType: tokenType || 'unknown',
        isReal: isReal,
        length: finalToken ? finalToken.length : 0,
        source: storeToken ? 'store' : 'storage',
        lastUpdated: lastUpdated
      });
    } catch (error) {
      console.error('Erro ao carregar info do token:', error);
    } finally {
      setLoading(false);
    }
  };

  const testTokenRegistration = async () => {
    if (!tokenInfo?.token) {
      Alert.alert('Erro', 'Nenhum token disponível para teste');
      return;
    }

    try {
      setLoading(true);
      
      // Simular registro de token usando pushNotificationService
      const result = {
        success: true,
        message: 'Token registrado com sucesso (simulado)',
        token: tokenInfo.token,
        user_id: 'debug_user_' + Date.now()
      };

      Alert.alert(
        result.success ? 'Sucesso' : 'Erro',
        result.message,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Erro', 'Falha ao testar registro: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const copyTokenToClipboard = () => {
    if (tokenInfo?.token) {
      // Em um app real, você usaria Clipboard.setString
      console.log('🎯 TOKEN COMPLETO PARA COPIAR:');
      console.log('=====================================');
      console.log(tokenInfo.token);
      console.log('=====================================');
      
      Alert.alert(
        'Token Copiado',
        'Token completo foi exibido no console. Verifique os logs do dispositivo.',
        [{ text: 'OK' }]
      );
    }
  };

  const sendDebugInfo = async () => {
    if (!tokenInfo?.token) {
      Alert.alert('Erro', 'Nenhum token disponível');
      return;
    }

    try {
      setLoading(true);
      
      // Enviar para endpoint de debug
      const response = await fetch('https://www2.tecbiz.com.br/debug_token_release.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: tokenInfo.token,
          user_id: 'debug_' + Date.now(),
          device_info: {
            platform: 'react-native',
            token_type: tokenInfo.tokenType,
            is_real: tokenInfo.isReal,
            source: tokenInfo.source
          }
        })
      });

      const result = await response.json();
      
      Alert.alert(
        'Debug Info Enviada',
        JSON.stringify(result, null, 2),
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Erro', 'Falha ao enviar debug info: ' + error);
    } finally {
      setLoading(false);
    }
  };

  if (!visible) return null;

  return (
    <View style={styles.overlay}>
      <View style={styles.container}>
        <ScrollView style={styles.scrollView}>
          <Text style={styles.title}>🔍 Debug Token Info</Text>
          
          {loading && <Text style={styles.loading}>Carregando...</Text>}
          
          {tokenInfo && (
            <View style={styles.infoContainer}>
              <Text style={styles.label}>Token Status:</Text>
              <Text style={[styles.value, tokenInfo.isReal ? styles.success : styles.warning]}>
                {tokenInfo.isReal ? '✅ Token Real' : '⚠️ Token Simulado'}
              </Text>
              
              <Text style={styles.label}>Tipo:</Text>
              <Text style={styles.value}>{tokenInfo.tokenType}</Text>
              
              <Text style={styles.label}>Tamanho:</Text>
              <Text style={styles.value}>{tokenInfo.length} caracteres</Text>
              
              <Text style={styles.label}>Fonte:</Text>
              <Text style={styles.value}>{tokenInfo.source}</Text>
              
              <Text style={styles.label}>Token (preview):</Text>
              <Text style={styles.tokenPreview}>
                {tokenInfo.token ? tokenInfo.token.substring(0, 50) + '...' : 'N/A'}
              </Text>
              
              {tokenInfo.lastUpdated && (
                <>
                  <Text style={styles.label}>Última atualização:</Text>
                  <Text style={styles.value}>{tokenInfo.lastUpdated}</Text>
                </>
              )}
            </View>
          )}
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.button} onPress={copyTokenToClipboard}>
              <Text style={styles.buttonText}>📋 Ver Token Completo</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={testTokenRegistration}>
              <Text style={styles.buttonText}>🧪 Testar Registro</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={sendDebugInfo}>
              <Text style={styles.buttonText}>📤 Enviar Debug Info</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={loadTokenInfo}>
              <Text style={styles.buttonText}>🔄 Recarregar</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
        
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>❌ Fechar</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  container: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 10,
    padding: 20,
    maxHeight: '80%',
    width: '90%',
  },
  scrollView: {
    maxHeight: 400,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  loading: {
    textAlign: 'center',
    fontStyle: 'italic',
    color: '#666',
  },
  infoContainer: {
    marginBottom: 20,
  },
  label: {
    fontWeight: 'bold',
    marginTop: 10,
    color: '#333',
  },
  value: {
    marginTop: 2,
    color: '#666',
  },
  success: {
    color: '#4CAF50',
  },
  warning: {
    color: '#FF9800',
  },
  tokenPreview: {
    marginTop: 2,
    fontFamily: 'monospace',
    fontSize: 12,
    backgroundColor: '#f5f5f5',
    padding: 5,
    borderRadius: 3,
  },
  buttonContainer: {
    gap: 10,
  },
  button: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 5,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  closeButton: {
    backgroundColor: '#f44336',
    padding: 12,
    borderRadius: 5,
    alignItems: 'center',
    marginTop: 10,
  },
  closeButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default TokenDebugInfo;
