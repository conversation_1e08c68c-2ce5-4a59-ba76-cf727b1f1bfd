#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando se o projeto está pronto para build...\n');

const checks = [
  {
    name: 'react-native.config.js existe',
    check: () => fs.existsSync('react-native.config.js'),
    fix: 'Execute: node fix-edge-to-edge.js'
  },
  {
    name: 'babel.config.js existe',
    check: () => fs.existsSync('babel.config.js'),
    fix: 'Arquivo babel.config.js foi criado automaticamente'
  },
  {
    name: 'google-services.json existe',
    check: () => fs.existsSync('google-services.json'),
    fix: 'Copie o arquivo google-services.json para a raiz do projeto'
  },
  {
    name: 'Package name correto no app.config.js',
    check: () => {
      try {
        const configContent = fs.readFileSync('app.config.js', 'utf8');
        return configContent.includes('package: "com.tecbiz.tecbizassociadospush"');
      } catch {
        return false;
      }
    },
    fix: 'Verifique o package name no app.config.js'
  },
  {
    name: 'Package name correto no build.gradle',
    check: () => {
      try {
        const gradle = fs.readFileSync('android/app/build.gradle', 'utf8');
        return gradle.includes('com.tecbiz.tecbizassociadospush');
      } catch {
        return false;
      }
    },
    fix: 'Verifique o package name no android/app/build.gradle'
  },
  {
    name: 'Edge-to-edge desabilitado',
    check: () => {
      try {
        const props = fs.readFileSync('android/gradle.properties', 'utf8');
        return props.includes('expo.edgeToEdgeEnabled=false');
      } catch {
        return false;
      }
    },
    fix: 'Adicione expo.edgeToEdgeEnabled=false no android/gradle.properties'
  },
  {
    name: 'Styles.xml sem referência ao EdgeToEdge',
    check: () => {
      try {
        const styles = fs.readFileSync('android/app/src/main/res/values/styles.xml', 'utf8');
        return !styles.includes('Theme.EdgeToEdge');
      } catch {
        return false;
      }
    },
    fix: 'Execute: node fix-edge-to-edge.js para corrigir styles.xml'
  }
];

let allPassed = true;

checks.forEach((check, index) => {
  const passed = check.check();
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${check.name}`);
  
  if (!passed) {
    console.log(`   💡 Solução: ${check.fix}`);
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(50));

if (allPassed) {
  console.log('🎉 PROJETO PRONTO PARA BUILD!');
  console.log('\n🚀 Execute agora:');
  console.log('eas build --platform android --profile preview --clear-cache');
} else {
  console.log('⚠️ CORRIJA OS PROBLEMAS ACIMA ANTES DO BUILD');
  console.log('\n🔧 Para aplicar correções automaticamente:');
  console.log('node fix-edge-to-edge.js');
}

console.log('\n📋 Arquivos importantes criados:');
console.log('- react-native.config.js (desabilita edge-to-edge)');
console.log('- babel.config.js (configuração Babel)');
console.log('- CORRECOES_BUILD_EAS.md (documentação das correções)');
