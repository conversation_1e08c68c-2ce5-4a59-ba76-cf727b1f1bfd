<?php
/**
 * TESTE ESPECÍFICO PARA EAS BUILD
 * Execute: php test_eas_push.php
 */

if (php_sapi_name() !== 'cli') {
    die("Execute via CLI: php test_eas_push.php\n");
}

echo "🏗️ TESTE ESPECÍFICO PARA EAS BUILD\n";
echo "==================================\n\n";

function log_eas($message, $type = 'info') {
    $colors = [
        'info' => "\033[0;36m",
        'success' => "\033[0;32m",
        'error' => "\033[0;31m",
        'warning' => "\033[0;33m",
        'reset' => "\033[0m"
    ];
    
    $color = $colors[$type] ?? $colors['info'];
    $timestamp = date('H:i:s');
    echo $color . "[$timestamp] $message" . $colors['reset'] . "\n";
}

// Incluir classe de envio
include_once('/var/www/vhosts/tecbiz.com.br/httpdocs/tecbiz/logica/elementos/sendAndroidPushNotification.php');

try {
    log_eas("🎯 TESTE ESPECÍFICO PARA EAS BUILD", 'warning');
    log_eas("Este teste é otimizado para apps compilados com EAS", 'info');
    
    // 1. Verificar configurações
    log_eas("🔍 Verificando configurações...", 'info');
    
    // Verificar google-services.json
    if (file_exists('google-services.json')) {
        $googleServices = json_decode(file_get_contents('google-services.json'), true);
        $projectId = $googleServices['project_info']['project_id'] ?? 'N/A';
        $packageName = $googleServices['client'][0]['client_info']['android_client_info']['package_name'] ?? 'N/A';
        $fcmKey = $googleServices['client'][0]['api_key'][0]['current_key'] ?? 'N/A';
        
        log_eas("✅ google-services.json encontrado", 'success');
        log_eas("   Project ID: $projectId", 'info');
        log_eas("   Package: $packageName", 'info');
        log_eas("   FCM Key: " . substr($fcmKey, 0, 20) . "...", 'info');
    } else {
        log_eas("❌ google-services.json não encontrado", 'error');
    }
    
    // 2. Token de teste
    $tokenTeste = 'ExponentPushToken[eMZsHICR_9RNghuZC5vlWT]';
    log_eas("🎯 Token de teste: " . substr($tokenTeste, 0, 30) . "...", 'info');
    
    // 3. Preparar mensagem específica para EAS
    $titulo = "🏗️ EAS Test " . date('H:i:s');
    $mensagem = "TESTE EAS BUILD: Esta mensagem foi enviada para testar push notifications em app compilado com EAS em " . date('d/m/Y H:i:s') . ". Se você recebeu, a configuração FCM está funcionando!";
    
    $dados = [
        'test_type' => 'eas_build',
        'timestamp' => time(),
        'source' => 'test_eas_push',
        'screen' => 'Notifications',
        'priority' => 'high',
        'build_type' => 'eas',
        'package' => 'com.tecbiz.tecbizassociadospush'
    ];
    
    log_eas("📝 Preparando mensagem EAS:", 'info');
    log_eas("   Título: $titulo", 'info');
    log_eas("   Tipo: EAS Build Test", 'info');
    log_eas("   Package: com.tecbiz.tecbizassociadospush", 'info');
    
    // 4. Tentar múltiplos métodos de envio
    log_eas("🚀 TENTANDO MÚLTIPLOS MÉTODOS DE ENVIO...", 'warning');
    
    // Método 1: Expo Push API (padrão)
    log_eas("📤 Método 1: Expo Push API", 'info');
    $push1 = new sendAndroidPushNotification($titulo, $mensagem, [$tokenTeste], $dados);
    $sucesso1 = $push1->send();
    
    if ($sucesso1) {
        log_eas("✅ Método 1: SUCESSO via Expo Push API", 'success');
    } else {
        log_eas("❌ Método 1: FALHOU", 'error');
        $resultado1 = $push1->getResult();
        log_eas("   Erro: " . json_encode($resultado1['errors'] ?? []), 'error');
    }
    
    // Método 2: FCM Direto (se disponível)
    if (!$sucesso1 && isset($fcmKey) && $fcmKey !== 'N/A') {
        log_eas("📤 Método 2: FCM Direto", 'info');
        
        try {
            $fcmResult = sendDirectFCM($tokenTeste, $titulo, $mensagem, $dados, $fcmKey);
            
            if ($fcmResult['success']) {
                log_eas("✅ Método 2: SUCESSO via FCM Direto", 'success');
                $sucesso1 = true;
            } else {
                log_eas("❌ Método 2: FALHOU", 'error');
                log_eas("   Erro FCM: " . $fcmResult['error'], 'error');
            }
            
        } catch (Exception $e) {
            log_eas("❌ Método 2: ERRO - " . $e->getMessage(), 'error');
        }
    }
    
    // 5. Resultado final
    if ($sucesso1) {
        log_eas("🎉 PUSH ENVIADO COM SUCESSO!", 'success');
        
        echo "\n";
        log_eas("📱 VERIFICAÇÃO NO DISPOSITIVO:", 'warning');
        log_eas("1. Certifique-se de que o app foi compilado com EAS", 'info');
        log_eas("2. App deve estar instalado via APK do EAS", 'info');
        log_eas("3. Feche o app completamente", 'info');
        log_eas("4. Aguarde 5-15 segundos", 'info');
        log_eas("5. Notificação deve aparecer na tela", 'info');
        
        echo "\n";
        log_eas("🔍 SE NÃO RECEBER:", 'warning');
        log_eas("1. Verifique se FCM foi configurado no EAS", 'error');
        log_eas("2. Execute: node configurar_fcm_eas.js", 'error');
        log_eas("3. Faça novo build EAS", 'error');
        log_eas("4. Obtenha novo token após instalar", 'error');
        
    } else {
        log_eas("❌ TODOS OS MÉTODOS FALHARAM!", 'error');
        
        echo "\n";
        log_eas("🔧 SOLUÇÕES PARA EAS:", 'warning');
        log_eas("1. CONFIGURAR FCM NO EAS:", 'error');
        log_eas("   node configurar_fcm_eas.js", 'info');
        
        log_eas("2. FAZER NOVO BUILD:", 'error');
        log_eas("   eas build --platform android --profile preview --clear-cache", 'info');
        
        log_eas("3. VERIFICAR CONFIGURAÇÃO:", 'error');
        log_eas("   npx expo push:android:show", 'info');
        
        log_eas("4. OBTER NOVO TOKEN:", 'error');
        log_eas("   - Instalar novo APK", 'info');
        log_eas("   - Fazer login no app", 'info');
        log_eas("   - Ir para Debug Token", 'info');
        log_eas("   - Copiar novo token", 'info');
    }
    
} catch (Exception $e) {
    log_eas("💥 ERRO CRÍTICO: " . $e->getMessage(), 'error');
    exit(1);
}

// Função auxiliar para FCM direto
function sendDirectFCM($token, $title, $body, $data, $serverKey) {
    $url = 'https://fcm.googleapis.com/fcm/send';
    
    $notification = [
        'title' => $title,
        'body' => $body,
        'sound' => 'default',
        'badge' => 1
    ];
    
    $fields = [
        'to' => $token,
        'notification' => $notification,
        'data' => $data,
        'priority' => 'high'
    ];
    
    $headers = [
        'Authorization: key=' . $serverKey,
        'Content-Type: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $result = json_decode($response, true);
    
    return [
        'success' => $httpCode === 200 && isset($result['success']) && $result['success'] > 0,
        'response' => $result,
        'error' => $httpCode !== 200 ? "HTTP $httpCode" : ($result['error'] ?? 'Unknown error')
    ];
}

echo "\n" . str_repeat("=", 60) . "\n";
log_eas("🏗️ TESTE EAS BUILD CONCLUÍDO!", 'success');

echo "\n";
log_eas("📋 RESUMO PARA EAS:", 'info');
log_eas("✅ EAS Build precisa de configuração FCM específica", 'info');
log_eas("✅ Diferente do Expo Go (que usa credenciais do Expo)", 'info');
log_eas("✅ Cada build precisa das suas próprias credenciais", 'info');

echo "\n";
log_eas("🚀 PRÓXIMOS PASSOS:", 'warning');
log_eas("1. node configurar_fcm_eas.js", 'info');
log_eas("2. eas build --platform android --profile preview --clear-cache", 'info');
log_eas("3. Instalar novo APK", 'info');
log_eas("4. Obter novo token", 'info');
log_eas("5. php test_eas_push.php", 'info');

echo "\n🎉 TESTE CONCLUÍDO!\n";
?>
