const { withAndroidManifest, withMainActivity } = require('@expo/config-plugins');

/**
 * Plugin para resolver avisos do Google Play Console
 * - Remove APIs descontinuadas para edge-to-edge
 * - Melhora compatibilidade com dispositivos de tela grande
 * - Remove restrições de redimensionamento e orientação
 */
const withAndroidCompatibility = (config) => {
  // Configurar AndroidManifest.xml
  config = withAndroidManifest(config, (config) => {
    const androidManifest = config.modResults;
    
    // Encontrar a activity principal
    const application = androidManifest.manifest.application[0];
    const mainActivity = application.activity.find(
      activity => activity.$['android:name'] === '.MainActivity'
    );
    
    if (mainActivity) {
      // ✅ Remover restrições rígidas de orientação
      // Manter portrait como padrão, mas permitir flexibilidade
      mainActivity.$['android:screenOrientation'] = 'portrait';
      
      // ✅ Melhorar suporte para dispositivos de tela grande
      mainActivity.$['android:resizeableActivity'] = 'true';
      
      // ✅ Configurações para dispositivos foldable e tablets
      mainActivity.$['android:supportsPictureInPicture'] = 'false';
      
      // ✅ Configurações de aspect ratio para melhor compatibilidade
      if (!mainActivity.$['android:maxAspectRatio']) {
        mainActivity.$['android:maxAspectRatio'] = '2.4';
      }
      
      // ✅ Remover configurações que podem causar problemas com edge-to-edge
      delete mainActivity.$['android:windowSoftInputMode'];
      mainActivity.$['android:windowSoftInputMode'] = 'adjustResize';
      
      // ✅ Configurações para melhor compatibilidade com diferentes telas
      mainActivity.$['android:configChanges'] = 
        'keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|density';
    }
    
    // ✅ Configurações da aplicação para melhor compatibilidade
    if (application) {
      application.$['android:supportsRtl'] = 'true';
      application.$['android:largeHeap'] = 'false'; // Evitar problemas de memória
      
      // ✅ Remover configurações que podem causar problemas
      delete application.$['android:hardwareAccelerated'];
      application.$['android:hardwareAccelerated'] = 'true';
    }
    
    return config;
  });
  
  // Configurar MainActivity para melhor compatibilidade
  config = withMainActivity(config, (config) => {
    const mainActivity = config.modResults;
    
    // ✅ Adicionar imports necessários para compatibilidade
    if (!mainActivity.contents.includes('import android.content.res.Configuration;')) {
      mainActivity.contents = mainActivity.contents.replace(
        'import android.os.Bundle;',
        'import android.os.Bundle;\nimport android.content.res.Configuration;'
      );
    }
    
    // ✅ Adicionar método para lidar com mudanças de configuração
    if (!mainActivity.contents.includes('onConfigurationChanged')) {
      const onConfigurationChanged = `
  @Override
  public void onConfigurationChanged(Configuration newConfig) {
    super.onConfigurationChanged(newConfig);
    // Lidar com mudanças de orientação e tamanho de tela de forma mais robusta
  }`;
      
      mainActivity.contents = mainActivity.contents.replace(
        'public class MainActivity extends ReactActivity {',
        `public class MainActivity extends ReactActivity {${onConfigurationChanged}`
      );
    }
    
    return config;
  });
  
  return config;
};

module.exports = withAndroidCompatibility;
