// Serviço de Autenticação Biométrica para TecBiz
// Integração com expo-local-authentication

import * as LocalAuthentication from 'expo-local-authentication';
import { Alert } from 'react-native';
import { nativeStorage } from '../utils/nativeStorage';

// Interface para dados biométricos
interface BiometricData {
  cardOrEmail: string;
  password: string;
  enabled: boolean;
  lastUsed: number;
}

class BiometricService {
  private isInitialized = false;
  private biometricSupported = false;
  private biometricTypes: LocalAuthentication.AuthenticationType[] = [];

  /**
   * Inicializar serviço biométrico
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔐 Inicializando serviço biométrico...');

      // Verificar se dispositivo suporta biometria
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      if (!hasHardware) {
        console.log('❌ Dispositivo não suporta biometria');
        return false;
      }

      // Verificar se há biometrias cadastradas
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      if (!isEnrolled) {
        console.log('⚠️ Nenhuma biometria cadastrada no dispositivo');
        return false;
      }

      // Obter tipos de biometria disponíveis
      this.biometricTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      console.log('🔐 Tipos de biometria disponíveis:', this.getBiometricTypesString());

      this.biometricSupported = true;
      this.isInitialized = true;

      console.log('✅ Serviço biométrico inicializado com sucesso');
      return true;
    } catch (error) {
      console.error('❌ Erro ao inicializar biometria:', error);
      return false;
    }
  }

  /**
   * Verificar se biometria está disponível
   */
  isAvailable(): boolean {
    return this.isInitialized && this.biometricSupported;
  }

  /**
   * Obter tipos de biometria como string
   */
  getBiometricTypesString(): string {
    const types = this.biometricTypes.map(type => {
      switch (type) {
        case LocalAuthentication.AuthenticationType.FINGERPRINT:
          return 'Digital';
        case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
          return 'Reconhecimento Facial';
        case LocalAuthentication.AuthenticationType.IRIS:
          return 'Íris';
        default:
          return 'Biometria';
      }
    });
    return types.join(', ');
  }

  /**
   * Verificar se biometria está habilitada para login
   */
  async isBiometricLoginEnabled(): Promise<boolean> {
    try {
      const data = await this.getBiometricData();
      return data?.enabled || false;
    } catch (error) {
      console.error('❌ Erro ao verificar biometria habilitada:', error);
      return false;
    }
  }

  /**
   * Salvar dados para login biométrico
   */
  async saveBiometricData(cardOrEmail: string, password: string): Promise<boolean> {
    try {
      if (!this.isAvailable()) {
        console.log('❌ Biometria não disponível');
        return false;
      }

      // Confirmar com o usuário
      const confirmed = await this.showBiometricSetupDialog();
      if (!confirmed) {
        return false;
      }

      // Autenticar antes de salvar
      const authenticated = await this.authenticate('Confirme sua identidade para habilitar login biométrico');
      if (!authenticated) {
        return false;
      }

      // Salvar dados criptografados
      const biometricData: BiometricData = {
        cardOrEmail,
        password,
        enabled: true,
        lastUsed: Date.now()
      };

      await nativeStorage.setObject('biometric_data', biometricData);
      console.log('✅ Dados biométricos salvos com sucesso');

      // Não mostrar alert - configuração silenciosa
      console.log(`🔐 Login biométrico configurado silenciosamente - Tipo: ${this.getBiometricTypesString()}`);

      return true;
    } catch (error) {
      console.error('❌ Erro ao salvar dados biométricos:', error);
      Alert.alert('Erro', 'Falha ao configurar login biométrico');
      return false;
    }
  }

  /**
   * Obter dados biométricos salvos
   */
  private async getBiometricData(): Promise<BiometricData | null> {
    try {
      return nativeStorage.getObject<BiometricData>('biometric_data');
    } catch (error) {
      console.error('❌ Erro ao obter dados biométricos:', error);
      return null;
    }
  }

  /**
   * Fazer login com biometria
   */
  async authenticateLogin(): Promise<{ success: boolean; cardOrEmail?: string; password?: string }> {
    try {
      if (!this.isAvailable()) {
        return { success: false };
      }

      const biometricData = await this.getBiometricData();
      if (!biometricData || !biometricData.enabled) {
        console.log('❌ Login biométrico não configurado');
        return { success: false };
      }

      // Autenticar com biometria
      const authenticated = await this.authenticate('Use sua biometria para fazer login');
      if (!authenticated) {
        return { success: false };
      }

      // Atualizar último uso
      biometricData.lastUsed = Date.now();
      await nativeStorage.setObject('biometric_data', biometricData);

      console.log('✅ Login biométrico realizado com sucesso');
      return {
        success: true,
        cardOrEmail: biometricData.cardOrEmail,
        password: biometricData.password
      };
    } catch (error) {
      console.error('❌ Erro no login biométrico:', error);
      return { success: false };
    }
  }

  /**
   * Autenticar com biometria
   */
  private async authenticate(promptMessage: string): Promise<boolean> {
    try {
      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        cancelLabel: 'Cancelar',
        fallbackLabel: 'Usar senha do dispositivo',
        disableDeviceFallback: false,
      });

      if (result.success) {
        console.log('✅ Autenticação biométrica bem-sucedida');
        return true;
      } else {
        console.log('❌ Autenticação biométrica falhou:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Erro na autenticação biométrica:', error);
      return false;
    }
  }

  /**
   * Mostrar dialog de configuração
   */
  private showBiometricSetupDialog(): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        'Habilitar Login Biométrico',
        `Deseja habilitar o login com ${this.getBiometricTypesString()}?\n\nIsso permitirá fazer login de forma mais rápida e segura.`,
        [
          {
            text: 'Cancelar',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Habilitar',
            onPress: () => resolve(true)
          }
        ]
      );
    });
  }

  /**
   * Desabilitar login biométrico
   */
  async disableBiometricLogin(): Promise<boolean> {
    try {
      const authenticated = await this.authenticate('Confirme sua identidade para desabilitar login biométrico');
      if (!authenticated) {
        return false;
      }

      await nativeStorage.removeItem('biometric_data');
      console.log('✅ Login biométrico desabilitado');

      Alert.alert(
        'Biometria Desabilitada',
        'Login biométrico foi desabilitado com sucesso.',
        [{ text: 'OK' }]
      );

      return true;
    } catch (error) {
      console.error('❌ Erro ao desabilitar biometria:', error);
      Alert.alert('Erro', 'Falha ao desabilitar login biométrico');
      return false;
    }
  }

  /**
   * Obter informações do serviço
   */
  getServiceInfo(): string {
    if (!this.isInitialized) {
      return 'Serviço não inicializado';
    }

    if (!this.biometricSupported) {
      return 'Biometria não suportada neste dispositivo';
    }

    return `Biometria Disponível\nTipos: ${this.getBiometricTypesString()}\nStatus: ${this.isAvailable() ? 'Ativo' : 'Inativo'}`;
  }

  /**
   * Verificar se há dados biométricos salvos
   */
  async hasSavedBiometricData(): Promise<boolean> {
    try {
      const data = await this.getBiometricData();
      return data?.enabled || false;
    } catch (error) {
      return false;
    }
  }
}

// Instância singleton
const biometricService = new BiometricService();
export default biometricService;

// Funções helper para uso mais fácil
export const initializeBiometric = () => biometricService.initialize();
export const isBiometricAvailable = () => biometricService.isAvailable();
export const authenticateWithBiometric = () => biometricService.authenticateLogin();
export const saveBiometricLogin = (cardOrEmail: string, password: string) => 
  biometricService.saveBiometricData(cardOrEmail, password);
export const disableBiometricLogin = () => biometricService.disableBiometricLogin();
