import { robustStorage } from '../utils/robustStorage';

interface DatabaseNotification {
  id: number;
  title: string;
  body: string;
  data?: any;
  timestamp: number;
  token: string;
  lida: boolean;
  created_at: string;
  codass?: number;
  codent?: number;
  cartao?: string;
}

interface NotificationResponse {
  success: boolean;
  count: number;
  notifications: DatabaseNotification[];
  error?: string;
}

class DatabaseNotificationService {
  private apiUrl = 'https://www2.tecbiz.com.br/tecbiz/get_pending_notifications.php';
  private markReadUrl = 'https://www2.tecbiz.com.br/tecbiz/mark_notifications_read.php';

  /**
   * Buscar notificações do banco de dados
   */
  async fetchNotifications(codass?: number, codent?: number): Promise<DatabaseNotification[]> {
    try {
      console.log('🗄️ Buscando notificações do banco de dados...');
      
      // Obter token atual
      const token = await this.getCurrentToken();
      if (!token) {
        console.log('⚠️ Token não encontrado');
        return [];
      }

      // Construir URL com parâmetros
      const params = new URLSearchParams({
        token: token,
        since: (Date.now() - (7 * 24 * 60 * 60 * 1000)).toString() // 7 dias atrás
      });

      if (codass) params.append('codass', codass.toString());
      if (codent) params.append('codent', codent.toString());

      const url = `${this.apiUrl}?${params.toString()}`;
      console.log('🌐 URL da requisição:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: NotificationResponse = await response.json();
      console.log('📥 Resposta da API:', data);

      if (data.success && data.notifications) {
        console.log(`✅ ${data.count} notificações encontradas no banco`);
        return data.notifications;
      } else {
        console.log('⚠️ Nenhuma notificação encontrada ou erro na API');
        return [];
      }

    } catch (error) {
      console.error('❌ Erro ao buscar notificações do banco:', error);
      return [];
    }
  }

  /**
   * Marcar notificações como lidas
   */
  async markNotificationsAsRead(notificationIds: number[]): Promise<boolean> {
    try {
      console.log('📖 Marcando notificações como lidas:', notificationIds);

      const response = await fetch(this.markReadUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ids: notificationIds
        }),
        timeout: 5000,
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (data.success) {
        console.log(`✅ ${notificationIds.length} notificações marcadas como lidas`);
        return true;
      } else {
        console.log('❌ Erro ao marcar como lidas:', data.error);
        return false;
      }

    } catch (error) {
      console.error('❌ Erro ao marcar notificações como lidas:', error);
      return false;
    }
  }

  /**
   * Buscar notificações para um usuário específico
   */
  async fetchUserNotifications(codass: number, codent: number): Promise<DatabaseNotification[]> {
    console.log(`👤 Buscando notificações para usuário: codass=${codass}, codent=${codent}`);
    return await this.fetchNotifications(codass, codent);
  }

  /**
   * Contar notificações não lidas
   */
  async getUnreadCount(codass?: number, codent?: number): Promise<number> {
    try {
      const notifications = await this.fetchNotifications(codass, codent);
      const unreadCount = notifications.filter(n => !n.lida).length;
      console.log(`📊 Notificações não lidas: ${unreadCount}`);
      return unreadCount;
    } catch (error) {
      console.error('❌ Erro ao contar não lidas:', error);
      return 0;
    }
  }

  /**
   * Sincronização automática (substitui o antigo sistema)
   */
  async syncNotifications(codass?: number, codent?: number): Promise<DatabaseNotification[]> {
    console.log('🔄 Iniciando sincronização de notificações do banco...');
    
    try {
      const notifications = await this.fetchNotifications(codass, codent);
      
      if (notifications.length > 0) {
        console.log(`📨 ${notifications.length} notificações sincronizadas do banco`);
        
        // Salvar timestamp da última sincronização
        await robustStorage.setItem('last_notification_sync', Date.now().toString());
      }
      
      return notifications;
      
    } catch (error) {
      console.error('❌ Erro na sincronização:', error);
      return [];
    }
  }

  /**
   * Obter token atual
   */
  private async getCurrentToken(): Promise<string | null> {
    try {
      const token = await robustStorage.getItem('push_token');
      return token;
    } catch (error) {
      console.error('❌ Erro ao obter token:', error);
      return null;
    }
  }

  /**
   * Limpar notificações antigas (opcional)
   */
  async clearOldNotifications(): Promise<void> {
    console.log('🧹 Limpeza de notificações antigas não implementada (feita no servidor)');
  }
}

export const databaseNotificationService = new DatabaseNotificationService();
export default databaseNotificationService;
