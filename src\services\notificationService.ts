import { robustStorage } from '../utils/robustStorage';

export interface PushNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: number;
  read: boolean;
  type?: string;
}

class NotificationService {
  private storageKey = 'push_notifications';
  private badgeKey = 'notification_badge_count';

  /**
   * Adicionar nova notificação recebida (com deduplicação)
   */
  async addNotification(notification: Omit<PushNotification, 'id' | 'timestamp' | 'read'>): Promise<void> {
    try {
      const notifications = await this.getNotifications();

      // Verificar duplicação mais rigorosa (mesmo título e corpo nos últimos 2 minutos)
      const twoMinutesAgo = Date.now() - (2 * 60 * 1000);
      const isDuplicate = notifications.some(existing =>
        existing.title === notification.title &&
        existing.body === notification.body &&
        existing.timestamp > twoMinutesAgo
      );

      if (isDuplicate) {
        console.log('⚠️ Notificação duplicada ignorada (app aberto):', notification.title);
        console.log('   - Títu<PERSON>:', notification.title);
        console.log('   - Corpo:', notification.body);
        console.log('   - <PERSON><PERSON> de deduplicação: 2 minutos');
        return;
      }

      const newNotification: PushNotification = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: Date.now(),
        read: false,
        ...notification
      };

      notifications.unshift(newNotification); // Adicionar no início

      // Manter apenas as últimas 50 notificações
      if (notifications.length > 50) {
        notifications.splice(50);
      }

      await robustStorage.setItem(this.storageKey, JSON.stringify(notifications));
      await this.updateBadgeCount();

      console.log('📱 Nova notificação adicionada:', newNotification.title);
    } catch (error) {
      console.error('❌ Erro ao adicionar notificação:', error);
    }
  }

  /**
   * Obter todas as notificações
   */
  async getNotifications(): Promise<PushNotification[]> {
    try {
      const stored = await robustStorage.getItem(this.storageKey);
      if (stored) {
        return JSON.parse(stored);
      }
      return [];
    } catch (error) {
      console.error('❌ Erro ao obter notificações:', error);
      return [];
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      const notification = notifications.find(n => n.id === notificationId);
      
      if (notification && !notification.read) {
        notification.read = true;
        await robustStorage.setItem(this.storageKey, JSON.stringify(notifications));
        await this.updateBadgeCount();
        console.log('✅ Notificação marcada como lida:', notificationId);
      }
    } catch (error) {
      console.error('❌ Erro ao marcar como lida:', error);
    }
  }

  /**
   * Marcar todas as notificações como lidas
   */
  async markAllAsRead(): Promise<void> {
    try {
      const notifications = await this.getNotifications();
      let hasUnread = false;
      
      notifications.forEach(notification => {
        if (!notification.read) {
          notification.read = true;
          hasUnread = true;
        }
      });

      if (hasUnread) {
        await robustStorage.setItem(this.storageKey, JSON.stringify(notifications));
        await this.updateBadgeCount();
        console.log('✅ Todas as notificações marcadas como lidas');
      }
    } catch (error) {
      console.error('❌ Erro ao marcar todas como lidas:', error);
    }
  }

  /**
   * Obter contagem de notificações não lidas
   */
  async getUnreadCount(): Promise<number> {
    try {
      const notifications = await this.getNotifications();
      return notifications.filter(n => !n.read).length;
    } catch (error) {
      console.error('❌ Erro ao obter contagem não lidas:', error);
      return 0;
    }
  }

  /**
   * Atualizar badge count no storage
   */
  private async updateBadgeCount(): Promise<void> {
    try {
      const count = await this.getUnreadCount();
      await robustStorage.setItem(this.badgeKey, count.toString());
      console.log('🔢 Badge count atualizado:', count);
    } catch (error) {
      console.error('❌ Erro ao atualizar badge count:', error);
    }
  }

  /**
   * Obter badge count do storage
   */
  async getBadgeCount(): Promise<number> {
    try {
      const stored = await robustStorage.getItem(this.badgeKey);
      return stored ? parseInt(stored, 10) : 0;
    } catch (error) {
      console.error('❌ Erro ao obter badge count:', error);
      return 0;
    }
  }

  /**
   * Limpar todas as notificações
   */
  async clearAll(): Promise<void> {
    try {
      await robustStorage.removeItem(this.storageKey);
      await robustStorage.removeItem(this.badgeKey);
      console.log('🗑️ Todas as notificações removidas');
    } catch (error) {
      console.error('❌ Erro ao limpar notificações:', error);
    }
  }

  /**
   * Processar notificação recebida (quando app está aberto)
   */
  async handleReceivedNotification(notification: any): Promise<void> {
    try {
      console.log('📨 Processando notificação recebida:', notification);
      
      await this.addNotification({
        title: notification.request?.content?.title || 'Nova Notificação',
        body: notification.request?.content?.body || '',
        data: notification.request?.content?.data || {},
        type: 'received'
      });
    } catch (error) {
      console.error('❌ Erro ao processar notificação recebida:', error);
    }
  }

  /**
   * Processar notificação clicada (quando usuário toca na notificação)
   */
  async handleNotificationResponse(response: any): Promise<void> {
    try {
      console.log('👆 Processando clique na notificação:', response);
      
      const notification = response.notification;
      await this.addNotification({
        title: notification.request?.content?.title || 'Notificação Clicada',
        body: notification.request?.content?.body || '',
        data: notification.request?.content?.data || {},
        type: 'clicked'
      });
    } catch (error) {
      console.error('❌ Erro ao processar clique na notificação:', error);
    }
  }

  /**
   * Adicionar notificação de teste
   */
  async addTestNotification(): Promise<void> {
    await this.addNotification({
      title: '🧪 Notificação de Teste',
      body: 'Esta é uma notificação de teste do sistema',
      data: { test: true, timestamp: Date.now() },
      type: 'test'
    });
  }
}

export const notificationService = new NotificationService();
export default notificationService;
