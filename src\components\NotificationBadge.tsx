import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Cores from '../constants/Cores';
import pushNotificationService from '../services/pushNotificationService';

interface NotificationBadgeProps {
  onPress?: () => void;
  style?: any;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ onPress, style }) => {
  const [unreadCount, setUnreadCount] = useState(0);

  // ✨ NOVO: Usar apenas pushNotificationService como fonte única
  useEffect(() => {
    const updateCount = () => {
      try {
        if (pushNotificationService.isServiceInitialized()) {
          const count = pushNotificationService.getUnreadCount();
          setUnreadCount(count);
        } else {
          setUnreadCount(0);
        }
      } catch (error) {
        setUnreadCount(0);
      }
    };

    updateCount();
    const interval = setInterval(updateCount, 5000); // A cada 5 segundos

    return () => clearInterval(interval);
  }, []);

  const hasFinalUnreadNotifications = unreadCount > 0;

  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, style]}
      onPress={handlePress}
      disabled={!onPress}
    >
      <Text style={styles.icon}>🔔</Text>
      {hasFinalUnreadNotifications && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>
            {unreadCount > 99 ? '99+' : unreadCount.toString()}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 8,
  },
  icon: {
    fontSize: 24,
    color: Cores.textoEscuro,
  },
  badge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: Cores.erro,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default NotificationBadge;
