<?php

include_once('/var/www/vhosts/tecbiz.com.br/httpdocs/tecbiz/logica/elementos/sendAndroidPushNotification.php');
/**
 * TESTE DO sendAndroidPushNotification.php
 * Execute: php test_send_notification.php
 */

if (php_sapi_name() !== 'cli') {
    die("Execute via CLI: php test_send_notification.php\n");
}

echo "🧪 TESTE DO sendAndroidPushNotification.php\n";
echo "==========================================\n\n";

function log_test($message, $type = 'info') {
    $colors = [
        'info' => "\033[0;36m",
        'success' => "\033[0;32m",
        'error' => "\033[0;31m",
        'warning' => "\033[0;33m",
        'reset' => "\033[0m"
    ];
    
    $color = $colors[$type] ?? $colors['info'];
    $timestamp = date('H:i:s');
    echo $color . "[$timestamp] $message" . $colors['reset'] . "\n";
}

try {
    // 1. Verificar se o arquivo existe
    log_test("📁 Verificando arquivo sendAndroidPushNotification.php...", 'info');
    
    if (!file_exists('/var/www/vhosts/tecbiz.com.br/httpdocs/tecbiz/logica/elementos/sendAndroidPushNotification.php')) {
        log_test("❌ Arquivo sendAndroidPushNotification.php não encontrado!", 'error');
        log_test("💡 Certifique-se de que o arquivo está no mesmo diretório", 'warning');
        exit(1);
    }
    
    log_test("✅ Arquivo encontrado", 'success');
    
    // 2. Incluir a classe
    log_test("📦 Carregando classe...", 'info');
    include_once('sendAndroidPushNotification.php');
    log_test("✅ Classe carregada com sucesso", 'success');
    
    // 3. Definir token de teste (SUBSTITUA PELO SEU TOKEN REAL)
    $tokenTeste = 'ExponentPushToken[eMZsHICR_9RNghuZC5vlWT]'; // SEU TOKEN REAL AQUI
    
    log_test("🎯 Usando token: " . substr($tokenTeste, 0, 30) . "...", 'info');
    
    // Verificar se é um token Expo válido
    if (strpos($tokenTeste, 'ExponentPushToken[') !== 0) {
        log_test("⚠️ ATENÇÃO: Token não parece ser um ExponentPushToken válido", 'warning');
        log_test("💡 Certifique-se de usar um token real obtido do app", 'warning');
    }
    
    // 4. Preparar dados da mensagem
    $titulo = "🧪 Teste " . date('H:i:s');
    $mensagem = "Esta é uma mensagem de teste enviada em " . date('d/m/Y H:i:s') . ". Se você recebeu esta notificação, o sistema está funcionando perfeitamente!";
    $dados = [
        'test' => true,
        'timestamp' => time(),
        'source' => 'test_script',
        'screen' => 'Notifications',
        'priority' => 'high'
    ];
    
    log_test("📝 Dados da mensagem:", 'info');
    log_test("   Título: $titulo", 'info');
    log_test("   Mensagem: " . substr($mensagem, 0, 80) . "...", 'info');
    log_test("   Dados extras: " . json_encode($dados), 'info');
    
    // 5. Criar instância e enviar
    log_test("🚀 Criando instância e enviando push...", 'warning');
    
    $push = new sendAndroidPushNotification($titulo, $mensagem, [$tokenTeste], $dados);
    
    $startTime = microtime(true);
    $sucesso = $push->send();
    $endTime = microtime(true);
    
    $duracao = round(($endTime - $startTime) * 1000, 2);
    log_test("⏱️ Tempo de envio: {$duracao}ms", 'info');
    
    // 6. Verificar resultado
    $resultado = $push->getResult();
    $tokensInvalidos = $push->getInvalidTokens();
    
    log_test("📊 Resultado detalhado:", 'info');
    log_test("   Sucesso: " . ($sucesso ? 'SIM' : 'NÃO'), $sucesso ? 'success' : 'error');
    log_test("   Tokens inválidos: " . count($tokensInvalidos), count($tokensInvalidos) > 0 ? 'warning' : 'success');
    log_test("   Resultado: " . json_encode($resultado), 'info');
    
    if ($sucesso) {
        log_test("🎉 PUSH ENVIADO COM SUCESSO!", 'success');
        
        if (count($tokensInvalidos) > 0) {
            log_test("⚠️ Alguns tokens eram inválidos:", 'warning');
            foreach ($tokensInvalidos as $tokenInvalido) {
                log_test("   - " . substr($tokenInvalido, 0, 50) . "...", 'warning');
            }
        }
        
    } else {
        log_test("❌ FALHA NO ENVIO!", 'error');
        log_test("Detalhes do erro: " . json_encode($resultado), 'error');
    }
    
} catch (Exception $e) {
    log_test("💥 ERRO CRÍTICO: " . $e->getMessage(), 'error');
    log_test("Arquivo: " . $e->getFile(), 'error');
    log_test("Linha: " . $e->getLine(), 'error');
    exit(1);
}

echo "\n" . str_repeat("=", 60) . "\n";

if ($sucesso) {
    log_test("🎯 TESTE CONCLUÍDO COM SUCESSO!", 'success');
    
    echo "\n";
    log_test("📱 COMO VERIFICAR SE FUNCIONOU:", 'warning');
    log_test("1. Mantenha o app FECHADO ou em BACKGROUND", 'info');
    log_test("2. Aguarde alguns segundos", 'info');
    log_test("3. Você deve receber uma notificação na tela do dispositivo", 'info');
    log_test("4. Abra o app e vá para a tela de notificações", 'info');
    log_test("5. A mensagem deve aparecer na lista", 'info');
    
    echo "\n";
    log_test("🔍 LOGS DO APP (para debug):", 'warning');
    log_test("adb logcat | grep 'PUSH-SERVICE'", 'info');
    log_test("ou: npx expo logs --platform android", 'info');
    
    echo "\n";
    log_test("✅ SEU sendAndroidPushNotification.php ESTÁ FUNCIONANDO!", 'success');
    
} else {
    log_test("❌ TESTE FALHOU!", 'error');
    
    echo "\n";
    log_test("🔧 POSSÍVEIS PROBLEMAS:", 'warning');
    log_test("1. Token não é válido (não é ExponentPushToken real)", 'info');
    log_test("2. App não está instalado no dispositivo", 'info');
    log_test("3. Dispositivo sem internet", 'info');
    log_test("4. Notificações desabilitadas no app", 'info');
    log_test("5. Problema na API do Expo", 'info');
    
    echo "\n";
    log_test("💡 COMO OBTER TOKEN VÁLIDO:", 'warning');
    log_test("1. Instale o app no dispositivo físico", 'info');
    log_test("2. Faça login no app", 'info');
    log_test("3. Vá para Home → Debug Token", 'info');
    log_test("4. Copie o token que aparece", 'info');
    log_test("5. Substitua na linha 31 deste arquivo", 'info');
}

echo "\n";
log_test("📋 PRÓXIMOS PASSOS:", 'info');
log_test("1. Se funcionou: Integre no seu backend usando exemplo_uso_backend.php", 'info');
log_test("2. Se não funcionou: Verifique o token e tente novamente", 'info');
log_test("3. Use este script sempre que quiser testar o envio", 'info');

echo "\n🎉 TESTE CONCLUÍDO!\n";
?>
