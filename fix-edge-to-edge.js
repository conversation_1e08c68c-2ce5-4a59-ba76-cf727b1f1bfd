#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Corrigindo problema do react-native-edge-to-edge...');

// Remover arquivos de build que podem estar causando conflito
const buildPaths = [
  'android/app/build',
  'android/app/.cxx',
  'android/build',
  'node_modules/react-native-edge-to-edge/android/build'
];

buildPaths.forEach(buildPath => {
  if (fs.existsSync(buildPath)) {
    console.log(`🗑️ Removendo: ${buildPath}`);
    fs.rmSync(buildPath, { recursive: true, force: true });
  }
});

// Criar arquivo react-native.config.js para desabilitar autolinking
const configContent = `module.exports = {
  dependencies: {
    'react-native-edge-to-edge': {
      platforms: {
        android: null, // disable Android platform auto linking
        ios: null, // disable iOS platform auto linking
      },
    },
  },
};`;

try {
  fs.writeFileSync('react-native.config.js', configContent);
  console.log('✅ Arquivo react-native.config.js criado');
} catch (error) {
  console.log('⚠️ Erro ao criar react-native.config.js:', error.message);
}

// Corrigir styles.xml para remover referência ao Theme.EdgeToEdge
const stylesPath = 'android/app/src/main/res/values/styles.xml';
if (fs.existsSync(stylesPath)) {
  try {
    let stylesContent = fs.readFileSync(stylesPath, 'utf8');
    if (stylesContent.includes('Theme.EdgeToEdge')) {
      stylesContent = stylesContent.replace(
        'parent="Theme.EdgeToEdge"',
        'parent="Theme.AppCompat.Light.NoActionBar"'
      );
      fs.writeFileSync(stylesPath, stylesContent);
      console.log('✅ Corrigido styles.xml - removida referência ao Theme.EdgeToEdge');
    }
  } catch (error) {
    console.log('⚠️ Erro ao corrigir styles.xml:', error.message);
  }
}

console.log('✅ Correção concluída! Execute o build novamente.');
