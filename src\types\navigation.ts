// Tipos de navegação centralizados
export type ScreenName = 
  | 'Splash' 
  | 'Login' 
  | 'Home' 
  | 'Saldo' 
  | 'Extrato' 
  | 'EsqueceuSenha' 
  | 'AutorizacoesPendentes' 
  | 'AlterarSenha' 
  | 'RedeConveniada' 
  | 'RedeConveniadaResultados' 
  | 'CartaoVirtual' 
  | 'BloquearCartao'
  | 'Notifications';

export interface NavigationProp {
  navigate: (screen: ScreenName, params?: any) => void;
  goBack?: () => void;
}

export interface ScreenProps {
  navigation: NavigationProp;
  route?: {
    params?: any;
  };
}
