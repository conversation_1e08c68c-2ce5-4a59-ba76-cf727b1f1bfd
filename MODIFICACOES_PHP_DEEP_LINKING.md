# Modificações nos Arquivos PHP para Deep Linking

## Resumo das Alterações

Os arquivos PHP existentes foram modificados para suportar deep linking com o aplicativo TecBiz, aproveitando a estrutura já existente que detecta `origem=APP`.

## Arquivos Modificados

### 1. `EnviaEmailLinkConfirmaEmail.php`

**Modificações na função `enviaEmail()`:**

- Adicionada detecção da origem para personalizar a mensagem do email
- Garantido que sempre tenha origem definida (padrão: 'WEB')
- Mensagem personalizada para aplicativo vs navegador
- Adicionada informação de debug "Sempre vai vir origem=APP" quando aplicável

**Principais mudanças:**
```php
// Ajustar mensagem baseada na origem
if ($origem == 'APP') {
    $msg .= "Clique no link abaixo para confirmar seu e-mail no aplicativo.<br />";
} else {
    $msg .= "Clique no link abaixo ou copie e cole no seu navegador.<br />";
}

// Garantir que sempre tenha origem definida
if (empty($origem)) {
    $origem = 'WEB'; // Padrão para web se não especificado
}
```

### 2. `pg_ass_login.php`

**Modificações em duas partes:**

#### A. Detecção automática de origem (linha ~143)
- Adicionada lógica para detectar automaticamente se a requisição veio do aplicativo
- Detecção baseada em `tokenid` ou User-Agent `TecBizApp`
- Passa a origem correta para `enviaEmail()`

```php
// Detectar se a requisição veio do aplicativo
$origem = 'WEB'; // Padrão
if (isset($_REQUEST['tokenid']) || isset($_SERVER['HTTP_USER_AGENT']) && strpos($_SERVER['HTTP_USER_AGENT'], 'TecBizApp') !== false) {
    $origem = 'APP';
}

$EnviaEmailLinkConfirmaEmail->enviaEmail($origem);
```

#### B. Processamento de confirmação de email (linha ~416)
- Mantida a lógica existente para detectar `origem=APP`
- Adicionado redirecionamento inteligente para o aplicativo
- HTML responsivo com fallback para navegador
- Deep link: `tecbizapp://email_confirmation`

**Principais mudanças:**
```php
if (@$_REQUEST['origem'] == 'APP') {
    // Construir URL do deep link para o app
    $appUrl = "tecbizapp://email_confirmation?" . http_build_query([
        'modo' => $_REQUEST['modo'],
        'dados' => $_REQUEST['dados'],
        'origem' => $_REQUEST['origem'],
        'status' => 'success',
        'message' => 'E-mail validado com sucesso!'
    ]);
    
    // HTML para redirecionamento inteligente
    // [HTML responsivo com JavaScript para redirecionamento]
}
```

## Fluxo Completo

### 1. Envio do Email
1. Usuário tenta fazer login no app
2. Sistema detecta que email não está validado
3. `pg_ass_login.php` detecta origem=APP automaticamente
4. `EnviaEmailLinkConfirmaEmail.php` envia email com `origem=APP`
5. Link no email: `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=...&dados=...&origem=APP`

### 2. Confirmação do Email
1. Usuário clica no link do email
2. `pg_ass_login.php` detecta `origem=APP`
3. Marca email como confirmado no banco
4. Retorna HTML com redirecionamento para `tecbizapp://email_confirmation`
5. Aplicativo abre automaticamente na tela de alterar senha

### 3. Fallbacks
- Se app não estiver instalado: redireciona para navegador
- Se redirecionamento falhar: botões manuais disponíveis
- Timeout de 5 segundos para fallback automático

## Configurações Necessárias

### No Aplicativo
- Deep link configurado: `tecbizapp://`
- Intent filters no AndroidManifest.xml
- User-Agent: `TecBizApp/1.0`

### No Servidor
- Arquivos PHP atualizados conforme descrito
- Suporte a HTTPS para deep links
- Headers corretos para detecção de origem

## Compatibilidade

- ✅ Mantém compatibilidade total com navegador web
- ✅ Funciona com aplicativo instalado
- ✅ Fallback automático se app não estiver disponível
- ✅ Não quebra funcionalidade existente

## Teste

Para testar:
1. Fazer login no app com email não validado
2. Verificar se email chega com `origem=APP`
3. Clicar no link do email
4. Verificar se abre o aplicativo
5. Confirmar se vai para tela de alterar senha
