#!/bin/bash

# Hook para corrigir package name antes do build EAS
echo "🔧 Executando correções pré-build..."

# Criar diretório correto se não existir
mkdir -p android/app/src/main/java/com/tecbiz/tecbizassociadospush

# Copiar e corrigir arquivos Java se necessário
if [ -f "android/app/src/main/java/com/mauriciocdz07/tecbizexpoapp/MainApplication.kt" ]; then
    echo "📝 Corrigindo MainApplication.kt..."
    sed 's/package com.mauriciocdz07.tecbizexpoapp/package com.tecbiz.tecbizassociadospush/g' \
        android/app/src/main/java/com/mauriciocdz07/tecbizexpoapp/MainApplication.kt > \
        android/app/src/main/java/com/tecbiz/tecbizassociadospush/MainApplication.kt
fi

if [ -f "android/app/src/main/java/com/mauriciocdz07/tecbizexpoapp/MainActivity.kt" ]; then
    echo "📝 Corrigindo MainActivity.kt..."
    sed 's/package com.mauriciocdz07.tecbizexpoapp/package com.tecbiz.tecbizassociadospush/g' \
        android/app/src/main/java/com/mauriciocdz07/tecbizexpoapp/MainActivity.kt > \
        android/app/src/main/java/com/tecbiz/tecbizassociadospush/MainActivity.kt
fi

echo "✅ Correções pré-build concluídas!"
