// Storage persistente super simples que funciona sem dependências
// Usa apenas globalStorage melhorado

class PersistentStorage {
  private initialized = false;

  // Inicializar storage
  async init() {
    if (this.initialized) return;

    try {
      // Garantir que globalStorage existe
      if (!(globalThis as any).globalStorage) {
        (globalThis as any).globalStorage = {};
      }

      this.initialized = true;
      console.log('💾 Storage persistente inicializado');
    } catch (error) {
      console.log('⚠️ Erro ao inicializar storage persistente:', error);
      this.initialized = true;
    }
  }

  // Salvar item
  async setItem(key: string, value: string) {
    await this.init();

    if (!(globalThis as any).globalStorage) {
      (globalThis as any).globalStorage = {};
    }
    (globalThis as any).globalStorage[key] = value;

    console.log(`💾 Item salvo: ${key}`);
  }

  // Obter item
  async getItem(key: string): Promise<string | null> {
    await this.init();

    const value = (globalThis as any).globalStorage?.[key];
    return value || null;
  }

  // Remover item
  async removeItem(key: string) {
    await this.init();

    if ((globalThis as any).globalStorage) {
      delete (globalThis as any).globalStorage[key];
    }

    console.log(`🗑️ Item removido: ${key}`);
  }

  // Limpar tudo
  async clear() {
    await this.init();

    if ((globalThis as any).globalStorage) {
      Object.keys((globalThis as any).globalStorage).forEach(key => {
        if (key.startsWith('saved_') || key.startsWith('cartao_') || key.startsWith('senha_')) {
          delete (globalThis as any).globalStorage[key];
        }
      });
    }

    console.log('🧹 Storage limpo');
  }
}

// Instância global
export const persistentStorage = new PersistentStorage();

// Funções de conveniência
export const saveLoginData = async (cardOrEmail: string, password: string, rememberData: boolean) => {
  try {
    if (rememberData) {
      await persistentStorage.setItem('saved_cardOrEmail', cardOrEmail);
      await persistentStorage.setItem('saved_password', password);
      await persistentStorage.setItem('saved_rememberData', 'true');
      console.log('💾 Dados de login salvos persistentemente');
    } else {
      // Se não marcou lembrar, limpar dados salvos
      await persistentStorage.removeItem('saved_cardOrEmail');
      await persistentStorage.removeItem('saved_password');
      await persistentStorage.removeItem('saved_rememberData');
      console.log('🗑️ Dados de login removidos');
    }

    // SEMPRE salvar para recarregamento automático (independente de lembrar dados)
    await persistentStorage.setItem('cartao_login', cardOrEmail);
    await persistentStorage.setItem('senha_login', password);
    
    // Também manter no globalStorage para compatibilidade
    if (!(globalThis as any).globalStorage) {
      (globalThis as any).globalStorage = {};
    }
    (globalThis as any).globalStorage['cartao_login'] = cardOrEmail;
    (globalThis as any).globalStorage['senha_login'] = password;
    
  } catch (error) {
    console.log('❌ Erro ao salvar dados de login:', error);
  }
};

export const loadLoginData = async () => {
  try {
    const cardOrEmail = await persistentStorage.getItem('saved_cardOrEmail');
    const password = await persistentStorage.getItem('saved_password');
    const rememberData = await persistentStorage.getItem('saved_rememberData');

    return {
      savedCardOrEmail: cardOrEmail,
      savedPassword: password,
      savedRememberData: rememberData === 'true'
    };
  } catch (error) {
    console.log('❌ Erro ao carregar dados de login:', error);
    return {
      savedCardOrEmail: null,
      savedPassword: null,
      savedRememberData: false
    };
  }
};

export const clearLoginData = async () => {
  try {
    await persistentStorage.removeItem('saved_cardOrEmail');
    await persistentStorage.removeItem('saved_password');
    await persistentStorage.removeItem('saved_rememberData');
    await persistentStorage.removeItem('cartao_login');
    await persistentStorage.removeItem('senha_login');
    
    // Limpar globalStorage também
    if ((globalThis as any).globalStorage) {
      delete (globalThis as any).globalStorage['cartao_login'];
      delete (globalThis as any).globalStorage['senha_login'];
    }
    
    console.log('🧹 Todos os dados de login limpos');
  } catch (error) {
    console.log('❌ Erro ao limpar dados de login:', error);
  }
};

export const getReloadCredentials = async () => {
  try {
    // Tentar primeiro do storage persistente
    let cartao = await persistentStorage.getItem('cartao_login');
    let senha = await persistentStorage.getItem('senha_login');
    
    // Se não encontrar, tentar do globalStorage
    if (!cartao || !senha) {
      cartao = (globalThis as any).globalStorage?.['cartao_login'];
      senha = (globalThis as any).globalStorage?.['senha_login'];
    }
    
    return { cartao, senha };
  } catch (error) {
    console.log('❌ Erro ao obter credenciais para reload:', error);
    return { cartao: null, senha: null };
  }
};
