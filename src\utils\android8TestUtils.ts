// Utilitários para testar compatibilidade com Android 8.0
import { Platform } from 'react-native';
import { compatibleFetch, isAndroid8OrLower } from './networkConfig';
import { createCompatibleAbortController } from './polyfills';

/**
 * Testar se AbortController funciona corretamente no Android 8.0
 */
export const testAbortController = async (): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('🧪 Testando AbortController no Android 8.0...');
    
    const controller = createCompatibleAbortController();
    
    // Testar se o controller foi criado corretamente
    if (!controller || !controller.signal) {
      return {
        success: false,
        message: 'AbortController não foi criado corretamente'
      };
    }
    
    // Testar se o signal tem as propriedades necessárias
    if (typeof controller.signal.addEventListener !== 'function') {
      return {
        success: false,
        message: 'AbortSignal não tem addEventListener'
      };
    }
    
    // Testar abort
    let abortCalled = false;
    controller.signal.addEventListener('abort', () => {
      abortCalled = true;
    });
    
    controller.abort();
    
    // Aguardar um pouco para o evento ser processado
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (!abortCalled) {
      return {
        success: false,
        message: 'Evento abort não foi disparado'
      };
    }
    
    console.log('✅ AbortController funcionando corretamente');
    return {
      success: true,
      message: 'AbortController funcionando corretamente'
    };
  } catch (error) {
    console.error('❌ Erro no teste AbortController:', error);
    return {
      success: false,
      message: `Erro: ${error}`
    };
  }
};

/**
 * Testar fetch com timeout no Android 8.0
 */
export const testFetchWithTimeout = async (): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('🧪 Testando fetch com timeout no Android 8.0...');
    
    // Testar com URL que deve responder rapidamente
    const testUrl = 'https://www.google.com';
    
    const startTime = Date.now();
    const response = await compatibleFetch(testUrl, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    const endTime = Date.now();
    
    console.log(`✅ Fetch completado em ${endTime - startTime}ms`);
    
    return {
      success: true,
      message: `Fetch funcionando - tempo: ${endTime - startTime}ms`
    };
  } catch (error) {
    console.error('❌ Erro no teste fetch:', error);
    return {
      success: false,
      message: `Erro no fetch: ${error}`
    };
  }
};

/**
 * Testar conexão HTTPS com o servidor da TecBiz
 */
export const testTecBizConnection = async (): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('🧪 Testando conexão HTTPS com TecBiz...');
    
    const testUrl = 'https://www2.tecbiz.com.br';
    
    const startTime = Date.now();
    const response = await compatibleFetch(testUrl, {
      method: 'HEAD'
    });
    const endTime = Date.now();
    
    console.log(`✅ Conexão TecBiz OK - Status: ${response.status}, Tempo: ${endTime - startTime}ms`);
    
    return {
      success: true,
      message: `Conexão OK - Status: ${response.status}, Tempo: ${endTime - startTime}ms`
    };
  } catch (error) {
    console.error('❌ Erro na conexão TecBiz:', error);
    return {
      success: false,
      message: `Erro na conexão: ${error}`
    };
  }
};

/**
 * Executar todos os testes de compatibilidade Android 8.0
 */
export const runAndroid8CompatibilityTests = async (): Promise<{
  success: boolean;
  results: Array<{ test: string; success: boolean; message: string }>;
}> => {
  console.log('🧪 Executando testes de compatibilidade Android 8.0...');
  
  const results = [];
  let allSuccess = true;
  
  // Teste 1: AbortController
  const abortTest = await testAbortController();
  results.push({ test: 'AbortController', ...abortTest });
  if (!abortTest.success) allSuccess = false;
  
  // Teste 2: Fetch com timeout
  const fetchTest = await testFetchWithTimeout();
  results.push({ test: 'Fetch com Timeout', ...fetchTest });
  if (!fetchTest.success) allSuccess = false;
  
  // Teste 3: Conexão TecBiz
  const tecbizTest = await testTecBizConnection();
  results.push({ test: 'Conexão TecBiz', ...tecbizTest });
  if (!tecbizTest.success) allSuccess = false;
  
  console.log('🧪 Resultados dos testes:');
  results.forEach(result => {
    console.log(`   ${result.success ? '✅' : '❌'} ${result.test}: ${result.message}`);
  });
  
  return {
    success: allSuccess,
    results
  };
};

/**
 * Obter informações do dispositivo para debug
 */
export const getDeviceInfo = () => {
  return {
    platform: Platform.OS,
    version: Platform.Version,
    isAndroid8OrLower: isAndroid8OrLower(),
    userAgent: navigator.userAgent || 'N/A',
    hasAbortController: typeof AbortController !== 'undefined',
    hasAbortSignalTimeout: typeof AbortSignal !== 'undefined' && typeof (AbortSignal as any).timeout === 'function',
    hasFetch: typeof fetch !== 'undefined'
  };
};

/**
 * Log detalhado das informações do dispositivo
 */
export const logDeviceInfo = () => {
  const info = getDeviceInfo();
  
  console.log('📱 Informações do dispositivo:');
  console.log('   - Plataforma:', info.platform);
  console.log('   - Versão:', info.version);
  console.log('   - É Android 8.0 ou inferior:', info.isAndroid8OrLower);
  console.log('   - User Agent:', info.userAgent);
  console.log('   - Tem AbortController:', info.hasAbortController);
  console.log('   - Tem AbortSignal.timeout:', info.hasAbortSignalTimeout);
  console.log('   - Tem fetch:', info.hasFetch);
  
  return info;
};

export default {
  testAbortController,
  testFetchWithTimeout,
  testTecBizConnection,
  runAndroid8CompatibilityTests,
  getDeviceInfo,
  logDeviceInfo
};
