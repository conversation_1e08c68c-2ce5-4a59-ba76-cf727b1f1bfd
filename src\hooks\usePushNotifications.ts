import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import pushNotificationService from '../services/firebasePushService';

// Interface para o estado do hook
interface PushNotificationState {
  token: string | null;
  isInitialized: boolean;
  hasPermission: boolean;
  isLoading: boolean;
  error: string | null;
}

// Interface para configuração do hook
interface UsePushNotificationsConfig {
  autoInitialize?: boolean;
  onTokenReceived?: (token: string) => void;
  onMessageReceived?: (message: any) => void;
  onNotificationOpened?: (message: any) => void;
}

/**
 * Hook personalizado para gerenciar push notifications
 */
export const usePushNotifications = (config: UsePushNotificationsConfig = {}) => {
  const {
    autoInitialize = true,
    onTokenReceived,
    onMessageReceived,
    onNotificationOpened
  } = config;

  const [state, setState] = useState<PushNotificationState>({
    token: null,
    isInitialized: false,
    hasPermission: false,
    isLoading: false,
    error: null
  });

  // Função para inicializar o serviço
  const initialize = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      console.log('🔔 Inicializando push notifications via hook...');
      
      // Inicializar o serviço
      await pushNotificationService.initialize();
      
      // Verificar permissões
      const hasPermission = await pushNotificationService.hasPermission();
      
      // Obter token
      const token = await pushNotificationService.getFCMToken();
      
      setState(prev => ({
        ...prev,
        token,
        isInitialized: true,
        hasPermission,
        isLoading: false
      }));

      // Callback de token recebido
      if (token && onTokenReceived) {
        onTokenReceived(token);
      }

      console.log('✅ Push notifications inicializadas via hook');
      
    } catch (error) {
      console.error('❌ Erro ao inicializar push notifications:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }));
    }
  };

  // Função para solicitar permissões manualmente
  const requestPermissions = async (): Promise<boolean> => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));
      
      const granted = await pushNotificationService.requestPermissions();
      
      setState(prev => ({
        ...prev,
        hasPermission: granted,
        isLoading: false
      }));

      if (!granted) {
        Alert.alert(
          'Permissões Necessárias',
          'Para receber notificações importantes, é necessário conceder permissão nas configurações do dispositivo.',
          [{ text: 'OK' }]
        );
      }

      return granted;
    } catch (error) {
      console.error('❌ Erro ao solicitar permissões:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao solicitar permissões'
      }));
      return false;
    }
  };

  // Função para obter o token atual
  const getToken = async (): Promise<string | null> => {
    try {
      const token = await pushNotificationService.getFCMToken();
      setState(prev => ({ ...prev, token }));
      return token;
    } catch (error) {
      console.error('❌ Erro ao obter token:', error);
      return null;
    }
  };

  // Função para verificar se tem permissão
  const checkPermission = async (): Promise<boolean> => {
    try {
      const hasPermission = await pushNotificationService.hasPermission();
      setState(prev => ({ ...prev, hasPermission }));
      return hasPermission;
    } catch (error) {
      console.error('❌ Erro ao verificar permissões:', error);
      return false;
    }
  };

  // Função para enviar token para o servidor
  const sendTokenToServer = async (token: string, userId?: string): Promise<boolean> => {
    try {
      console.log('📤 Enviando token para servidor...', {
        token: token.substring(0, 20) + '...',
        userId
      });

      // Aqui você implementaria a chamada para sua API
      // Exemplo:
      // const response = await fetch('/api/register-token', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ token, userId })
      // });
      // return response.ok;

      // Por enquanto, apenas log
      console.log('✅ Token enviado para servidor (simulado)');
      return true;
      
    } catch (error) {
      console.error('❌ Erro ao enviar token para servidor:', error);
      return false;
    }
  };

  // Efeito para inicialização automática
  useEffect(() => {
    if (autoInitialize) {
      initialize();
    }

    // Cleanup quando o componente for desmontado
    return () => {
      // O serviço é singleton, então não fazemos cleanup aqui
      // para permitir que outros componentes continuem usando
    };
  }, [autoInitialize]);

  // Efeito para configurar callbacks personalizados
  useEffect(() => {
    // Aqui você poderia configurar listeners adicionais se necessário
    // Por exemplo, listeners específicos para este hook
  }, [onMessageReceived, onNotificationOpened]);

  return {
    // Estado
    ...state,
    
    // Funções
    initialize,
    requestPermissions,
    getToken,
    checkPermission,
    sendTokenToServer,
    
    // Utilitários
    refresh: initialize,
    isReady: state.isInitialized && state.hasPermission && !!state.token
  };
};

export default usePushNotifications;
