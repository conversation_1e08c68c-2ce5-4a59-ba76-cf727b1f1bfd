// Storage híbrido - AsyncStorage com API similar ao MMKV
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage em memória como fallback
let memoryStorage: { [key: string]: any } = {};
let useMemoryFallback = false;

// Interface compatível com MMKV
interface HybridStorageInterface {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
  getAllKeys(): string[];
  getBoolean(key: string): boolean;
  setBoolean(key: string, value: boolean): void;
  getNumber(key: string): number;
  setNumber(key: string, value: number): void;
  getObject<T>(key: string): T | null;
  setObject(key: string, value: any): void;
}

// Implementação híbrida
class HybridStorage implements HybridStorageInterface {
  
  // Métodos síncronos (como MMKV) mas usando AsyncStorage internamente
  getItem(key: string): string | null {
    try {
      if (useMemoryFallback) {
        return memoryStorage[key] || null;
      }
      
      // Para manter compatibilidade síncrona, usar cache em memória
      // O valor real será carregado assincronamente
      return memoryStorage[key] || null;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter item ${key}:`, error);
      return memoryStorage[key] || null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      // Salvar imediatamente em memória
      memoryStorage[key] = value;
      
      if (!useMemoryFallback) {
        // Salvar assincronamente no AsyncStorage
        AsyncStorage.setItem(key, value).catch((error) => {
          console.warn(`⚠️ AsyncStorage falhou para ${key}, usando apenas memória:`, error);
          useMemoryFallback = true;
        });
      }
      
      console.log(`💾 Item salvo: ${key}`);
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar item ${key}:`, error);
      useMemoryFallback = true;
    }
  }

  removeItem(key: string): void {
    try {
      // Remover da memória
      delete memoryStorage[key];
      
      if (!useMemoryFallback) {
        // Remover assincronamente do AsyncStorage
        AsyncStorage.removeItem(key).catch((error) => {
          console.warn(`⚠️ AsyncStorage falhou para remover ${key}:`, error);
          useMemoryFallback = true;
        });
      }
      
      console.log(`🗑️ Item removido: ${key}`);
    } catch (error) {
      console.warn(`⚠️ Erro ao remover item ${key}:`, error);
    }
  }

  clear(): void {
    try {
      // Limpar memória
      memoryStorage = {};
      
      if (!useMemoryFallback) {
        // Limpar assincronamente o AsyncStorage
        AsyncStorage.clear().catch((error) => {
          console.warn('⚠️ AsyncStorage falhou para clear():', error);
          useMemoryFallback = true;
        });
      }
      
      console.log('🧹 Storage limpo');
    } catch (error) {
      console.warn('⚠️ Erro ao limpar storage:', error);
    }
  }

  getAllKeys(): string[] {
    try {
      return Object.keys(memoryStorage);
    } catch (error) {
      console.warn('⚠️ Erro ao obter chaves:', error);
      return [];
    }
  }

  // Métodos específicos para booleanos
  getBoolean(key: string): boolean {
    try {
      const value = this.getItem(key);
      return value === 'true';
    } catch (error) {
      console.warn(`⚠️ Erro ao obter boolean ${key}:`, error);
      return false;
    }
  }

  setBoolean(key: string, value: boolean): void {
    try {
      this.setItem(key, value.toString());
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar boolean ${key}:`, error);
    }
  }

  // Métodos específicos para números
  getNumber(key: string): number {
    try {
      const value = this.getItem(key);
      return value ? parseFloat(value) : 0;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter number ${key}:`, error);
      return 0;
    }
  }

  setNumber(key: string, value: number): void {
    try {
      this.setItem(key, value.toString());
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar number ${key}:`, error);
    }
  }

  // Métodos específicos para objetos JSON
  getObject<T>(key: string): T | null {
    try {
      const value = this.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter objeto ${key}:`, error);
      return null;
    }
  }

  setObject(key: string, value: any): void {
    try {
      this.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar objeto ${key}:`, error);
    }
  }

  // Método para carregar dados do AsyncStorage para memória
  async loadFromAsyncStorage(): Promise<void> {
    try {
      if (useMemoryFallback) return;
      
      const keys = await AsyncStorage.getAllKeys();
      const items = await AsyncStorage.multiGet(keys);
      
      items.forEach(([key, value]) => {
        if (value !== null) {
          memoryStorage[key] = value;
        }
      });
      
      console.log(`📱 ${keys.length} itens carregados do AsyncStorage para memória`);
    } catch (error) {
      console.warn('⚠️ Erro ao carregar do AsyncStorage, usando apenas memória:', error);
      useMemoryFallback = true;
    }
  }

  // Método para verificar se está usando fallback
  isUsingMemoryFallback(): boolean {
    return useMemoryFallback;
  }

  // Método para obter tipo de storage em uso
  getStorageType(): string {
    return useMemoryFallback ? 'Memory (AsyncStorage failed)' : 'AsyncStorage + Memory Cache';
  }
}

// Instância singleton
export const hybridStorage = new HybridStorage();

// Carregar dados do AsyncStorage na inicialização
hybridStorage.loadFromAsyncStorage();

// Funções específicas para login (mesma API do MMKV)
export const saveLoginData = (cardOrEmail: string, password: string, rememberData: boolean) => {
  try {
    if (rememberData) {
      hybridStorage.setItem('saved_cardOrEmail', cardOrEmail);
      hybridStorage.setItem('saved_password', password);
      hybridStorage.setBoolean('saved_rememberData', true);
      console.log('💾 Dados de login salvos');
    } else {
      // Se não marcou lembrar, limpar dados salvos
      hybridStorage.removeItem('saved_cardOrEmail');
      hybridStorage.removeItem('saved_password');
      hybridStorage.removeItem('saved_rememberData');
      console.log('🗑️ Dados de login removidos');
    }

    // SEMPRE salvar para recarregamento automático (independente de lembrar dados)
    hybridStorage.setItem('cartao_login', cardOrEmail);
    hybridStorage.setItem('senha_login', password);
    
  } catch (error) {
    console.error('❌ Erro ao salvar dados de login:', error);
  }
};

export const loadLoginData = () => {
  try {
    const rememberData = hybridStorage.getBoolean('saved_rememberData');
    
    if (rememberData) {
      const cardOrEmail = hybridStorage.getItem('saved_cardOrEmail');
      const password = hybridStorage.getItem('saved_password');
      
      return {
        cardOrEmail: cardOrEmail || '',
        password: password || '',
        rememberData: true
      };
    }
    
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  } catch (error) {
    console.error('❌ Erro ao carregar dados de login:', error);
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  }
};

export const clearLoginData = () => {
  try {
    hybridStorage.removeItem('saved_cardOrEmail');
    hybridStorage.removeItem('saved_password');
    hybridStorage.removeItem('saved_rememberData');
    hybridStorage.removeItem('cartao_login');
    hybridStorage.removeItem('senha_login');
    
    console.log('🧹 Todos os dados de login limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados de login:', error);
  }
};

// Export padrão
export default hybridStorage;

console.log('💾 Hybrid Storage inicializado (AsyncStorage + Memory Cache)');
