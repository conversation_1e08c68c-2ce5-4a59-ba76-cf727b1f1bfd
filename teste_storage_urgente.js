// TESTE URGENTE - VERIFICAR SE STORAGE FUNCIONA
// Execute no console do app para testar

console.log('🧪 TESTE URGENTE DO STORAGE');
console.log('===========================');

// Teste 1: robustStorage
import { robustStorage } from './src/utils/robustStorage';

async function testeRobustStorage() {
  console.log('📱 Testando robustStorage...');
  
  try {
    // Salvar
    await robustStorage.setItem('teste_login', 'cartao123');
    console.log('✅ Salvou no robustStorage');
    
    // Ler
    const valor = await robustStorage.getItem('teste_login');
    console.log('📖 Leu do robustStorage:', valor);
    
    if (valor === 'cartao123') {
      console.log('✅ robustStorage FUNCIONANDO!');
    } else {
      console.log('❌ robustStorage FALHOU!');
    }
  } catch (error) {
    console.log('❌ Erro no robustStorage:', error);
  }
}

// Teste 2: nativeStorage
import { nativeStorage } from './src/utils/nativeStorage';

async function testeNativeStorage() {
  console.log('📱 Testando nativeStorage...');
  
  try {
    // Salvar
    await nativeStorage.setItem('teste_token', 'ExponentPushToken[TESTE123]');
    console.log('✅ Salvou no nativeStorage');
    
    // Ler
    const valor = await nativeStorage.getItem('teste_token');
    console.log('📖 Leu do nativeStorage:', valor);
    
    if (valor === 'ExponentPushToken[TESTE123]') {
      console.log('✅ nativeStorage FUNCIONANDO!');
    } else {
      console.log('❌ nativeStorage FALHOU!');
    }
  } catch (error) {
    console.log('❌ Erro no nativeStorage:', error);
  }
}

// Executar testes
async function executarTestes() {
  console.log('🚀 INICIANDO TESTES URGENTES...');
  
  await testeRobustStorage();
  console.log('');
  await testeNativeStorage();
  
  console.log('');
  console.log('🎯 RESULTADO:');
  console.log('Se ambos mostraram "FUNCIONANDO", o storage está OK');
  console.log('Se algum falhou, ainda há problema');
}

// Executar
executarTestes();

// INSTRUÇÕES:
console.log('');
console.log('📋 INSTRUÇÕES:');
console.log('1. Copie este código');
console.log('2. Cole no console do app (DevTools)');
console.log('3. Execute');
console.log('4. Veja se ambos os storages funcionam');
console.log('5. Se funcionarem, teste login e token');
