// Storage robusto usando SecureStore + fallback em memória
import * as SecureStore from 'expo-secure-store';

// Storage em memória como fallback
let memoryStorage: { [key: string]: string } = {};

// Interface do Storage
interface StorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
  getBoolean(key: string): Promise<boolean>;
  setBoolean(key: string, value: boolean): Promise<void>;
  getObject<T>(key: string): Promise<T | null>;
  setObject(key: string, value: any): Promise<void>;
}

// Implementação robusta do Storage (SecureStore + fallback)
class RobustStorage implements StorageInterface {
  private useMemoryFallback = false; // Tentar SecureStore primeiro

  async getItem(key: string): Promise<string | null> {
    try {
      if (this.useMemoryFallback) {
        return memoryStorage[key] || null;
      }

      const value = await SecureStore.getItemAsync(key);
      
      // Se AsyncStorage retornar null, verificar memória como backup
      if (value === null && memoryStorage[key]) {
        return memoryStorage[key];
      }
      
      return value;
    } catch (error) {
      console.warn(`⚠️ AsyncStorage falhou para getItem(${key}), usando memória:`, error);
      this.useMemoryFallback = true;
      return memoryStorage[key] || null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      // SEMPRE salvar em memória primeiro
      memoryStorage[key] = value;

      if (!this.useMemoryFallback) {
        await SecureStore.setItemAsync(key, value);
        console.log(`💾 Item salvo no SecureStore: ${key}`);
      } else {
        console.log(`💾 Item salvo apenas em memória: ${key}`);
      }
    } catch (error) {
      console.warn(`⚠️ SecureStore falhou para setItem(${key}), usando apenas memória:`, error);
      this.useMemoryFallback = true;
      // Valor já foi salvo em memória acima
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      // Remover da memória
      delete memoryStorage[key];

      if (!this.useMemoryFallback) {
        await SecureStore.deleteItemAsync(key);
        console.log(`🗑️ Item removido do AsyncStorage: ${key}`);
      } else {
        console.log(`🗑️ Item removido apenas da memória: ${key}`);
      }
    } catch (error) {
      console.warn(`⚠️ AsyncStorage falhou para removeItem(${key}):`, error);
      this.useMemoryFallback = true;
      // Item já foi removido da memória acima
    }
  }

  async clear(): Promise<void> {
    try {
      // Limpar memória
      memoryStorage = {};

      if (!this.useMemoryFallback) {
        await AsyncStorage.clear();
        console.log('🧹 AsyncStorage limpo');
      } else {
        console.log('🧹 Memória limpa');
      }
    } catch (error) {
      console.warn('⚠️ AsyncStorage falhou para clear():', error);
      this.useMemoryFallback = true;
      // Memória já foi limpa acima
    }
  }

  // Métodos específicos para booleanos
  async getBoolean(key: string): Promise<boolean> {
    try {
      const value = await this.getItem(key);
      return value === 'true';
    } catch (error) {
      console.warn(`⚠️ Erro ao obter boolean ${key}:`, error);
      return false;
    }
  }

  async setBoolean(key: string, value: boolean): Promise<void> {
    try {
      await this.setItem(key, value.toString());
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar boolean ${key}:`, error);
    }
  }

  // Métodos específicos para objetos JSON
  async getObject<T>(key: string): Promise<T | null> {
    try {
      const value = await this.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter objeto ${key}:`, error);
      return null;
    }
  }

  async setObject(key: string, value: any): Promise<void> {
    try {
      await this.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar objeto ${key}:`, error);
    }
  }

  // Método para verificar se está usando fallback
  isUsingMemoryFallback(): boolean {
    return this.useMemoryFallback;
  }

  // Método para obter tipo de storage em uso
  getStorageType(): string {
    return this.useMemoryFallback ? 'Memory (SecureStore failed)' : 'SecureStore';
  }

  // Método para tentar reativar SecureStore
  async tryReactivateSecureStore(): Promise<boolean> {
    try {
      await SecureStore.setItemAsync('test_key', 'test_value');
      const value = await SecureStore.getItemAsync('test_key');
      await SecureStore.deleteItemAsync('test_key');

      if (value === 'test_value') {
        this.useMemoryFallback = false;
        console.log('✅ SecureStore reativado com sucesso');
        return true;
      }
    } catch (error) {
      console.warn('⚠️ SecureStore ainda não funciona:', error);
    }
    return false;
  }
}

// Instância singleton
export const robustStorage = new RobustStorage();

// Funções específicas para login
export const saveLoginData = async (cardOrEmail: string, password: string, rememberData: boolean) => {
  try {
    if (rememberData) {
      await robustStorage.setItem('saved_cardOrEmail', cardOrEmail);
      await robustStorage.setItem('saved_password', password);
      await robustStorage.setBoolean('saved_rememberData', true);
      console.log('💾 Dados de login salvos');
    } else {
      // Se não marcou lembrar, limpar dados salvos
      await robustStorage.removeItem('saved_cardOrEmail');
      await robustStorage.removeItem('saved_password');
      await robustStorage.removeItem('saved_rememberData');
      console.log('🗑️ Dados de login removidos');
    }

    // SEMPRE salvar para recarregamento automático (independente de lembrar dados)
    await robustStorage.setItem('cartao_login', cardOrEmail);
    await robustStorage.setItem('senha_login', password);
    
  } catch (error) {
    console.error('❌ Erro ao salvar dados de login:', error);
  }
};

export const loadLoginData = async () => {
  try {
    const rememberData = await robustStorage.getBoolean('saved_rememberData');
    
    if (rememberData) {
      const cardOrEmail = await robustStorage.getItem('saved_cardOrEmail');
      const password = await robustStorage.getItem('saved_password');
      
      return {
        cardOrEmail: cardOrEmail || '',
        password: password || '',
        rememberData: true
      };
    }
    
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  } catch (error) {
    console.error('❌ Erro ao carregar dados de login:', error);
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  }
};

export const clearLoginData = async () => {
  try {
    await robustStorage.removeItem('saved_cardOrEmail');
    await robustStorage.removeItem('saved_password');
    await robustStorage.removeItem('saved_rememberData');
    await robustStorage.removeItem('cartao_login');
    await robustStorage.removeItem('senha_login');
    
    console.log('🧹 Todos os dados de login limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados de login:', error);
  }
};

// Export padrão
export default robustStorage;

console.log('💾 Robust Storage inicializado com SecureStore + fallback em memória');
