import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  Animated,
  Dimensions,
} from 'react-native';
import Constants from 'expo-constants'; // Importe Constants de expo-constants 
import Cores from '../constants/Cores';

interface SplashScreenProps {
  navigation: {
    navigate: (screen: 'Login') => void;
  };
}

const { width, height } = Dimensions.get('window');

const SplashScreen = ({ navigation }: SplashScreenProps) => {
  // Animações
  const logoScale = useRef(new Animated.Value(0.3)).current;
  const logoOpacity = useRef(new Animated.Value(0)).current;
  const textOpacity = useRef(new Animated.Value(0)).current;
  const backgroundOpacity = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    startAnimations();
  }, []);

  const startAnimations = () => {
    // Sequência de animações
    Animated.sequence([
      // 1. Fade in do background
      Animated.timing(backgroundOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),

      // 2. Entrada do logo com escala (sem rotação)
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),

      // 3. Fade in do texto
      Animated.timing(textOpacity, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),

      // 4. Animação de pulso
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ]),
        { iterations: 2 }
      ),
    ]).start(() => {
      // Após as animações, navegar para Login
      setTimeout(() => {
        navigation.navigate('Login');
      }, 400);
    });
  };

    // Pega a versão do app.config.js
  const appVersion =
    Constants?.expoConfig?.version ||
    Constants?.manifest?.version ||
    '1.0.0';

  // Removido logoRotationInterpolate - não usamos mais rotação

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Cores.primaria} barStyle="light-content" />
      
      {/* Background animado */}
      <Animated.View 
        style={[
          styles.backgroundGradient,
          { opacity: backgroundOpacity }
        ]}
      />
      
      {/* Círculos decorativos */}
      <View style={styles.circleContainer}>
        <Animated.View 
          style={[
            styles.circle1,
            { 
              opacity: backgroundOpacity,
              transform: [{ scale: pulseAnimation }]
            }
          ]} 
        />
        <Animated.View 
          style={[
            styles.circle2,
            { 
              opacity: backgroundOpacity,
              transform: [{ scale: pulseAnimation }]
            }
          ]} 
        />
      </View>

      {/* Logo principal */}
      <View style={styles.logoContainer}>
        <Animated.View
          style={[
            styles.logoWrapper,
            {
              opacity: logoOpacity,
              transform: [
                { scale: logoScale },
                { scale: pulseAnimation }
              ],
            },
          ]}
        >
          {/* Logo TecBiz - Usando texto estilizado como logo */}
          <View style={styles.logoTextContainer}>
            <Text style={styles.logoTextTec}>Tec</Text>
            <Text style={styles.logoTextBiz}>Biz</Text>
          </View>
          <View style={styles.logoSubContainer}>
            <Text style={styles.logoSubText}>ASSOCIADOS</Text>
          </View>
        </Animated.View>
      </View>

      {/* Texto de carregamento */}
      <Animated.View 
        style={[
          styles.textContainer,
          { opacity: textOpacity }
        ]}
      >
        <Text style={styles.loadingText}>Carregando...</Text>
        <View style={styles.dotsContainer}>
          <Animated.View style={[styles.dot, { opacity: pulseAnimation }]} />
          <Animated.View style={[styles.dot, { opacity: pulseAnimation }]} />
          <Animated.View style={[styles.dot, { opacity: pulseAnimation }]} />
        </View>
      </Animated.View>

      {/* Versão */}
      <Animated.View 
        style={[
          styles.versionContainer,
          { opacity: textOpacity }
        ]}
      >
        <Text style={styles.versionText}>v{appVersion}</Text>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.primaria,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Cores.primaria,
  },
  circleContainer: {
    position: 'absolute',
    width: width,
    height: height,
  },
  circle1: {
    position: 'absolute',
    width: width * 0.8,
    height: width * 0.8,
    borderRadius: width * 0.4,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    top: -width * 0.3,
    right: -width * 0.3,
  },
  circle2: {
    position: 'absolute',
    width: width * 0.6,
    height: width * 0.6,
    borderRadius: width * 0.3,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    bottom: -width * 0.2,
    left: -width * 0.2,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  logoTextContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 5,
  },
  logoTextTec: {
    fontSize: 48,
    fontWeight: 'bold',
    color: Cores.textoBranco,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoTextBiz: {
    fontSize: 48,
    fontWeight: '300',
    color: Cores.textoBranco,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  logoSubContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 15,
  },
  logoSubText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Cores.textoBranco,
    letterSpacing: 2,
  },
  textContainer: {
    position: 'absolute',
    bottom: 120,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: Cores.textoBranco,
    marginBottom: 15,
    fontWeight: '500',
  },
  dotsContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Cores.textoBranco,
  },
  versionContainer: {
    position: 'absolute',
    bottom: 50,
  },
  versionText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
});

export default SplashScreen;
