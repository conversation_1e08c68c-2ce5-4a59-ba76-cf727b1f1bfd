{"name": "tecbizappass", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android --port=8082", "ios": "react-native run-android --port=8082", "start": "react-native run-android --port=8082", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:release": "cd android && gradlew.bat clean && gradlew.bat assembleRelease", "start:metro": "react-native start --port=8082", "start:app": "react-native run-android --port=8082"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-firebase/app": "^20.4.0", "@react-native-firebase/messaging": "^20.4.0", "react": "^18.2.0", "react-dom": "18.2.0", "react-native": "^0.74.5", "react-native-reanimated": "^3.18.0", "react-native-toast-message": "^2.3.3", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "^0.74.87", "@types/jest": "^29.5.5", "@types/node": "^24.0.14", "@types/react": "^18.3.23", "@types/react-native": "^0.73.0", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "typescript": "^5.0.4"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}