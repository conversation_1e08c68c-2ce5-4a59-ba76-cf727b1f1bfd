import React, { useState, useRef } from 'react';
import {
  ScrollView,
  View,
  StyleSheet,
  ViewStyle,
  ScrollViewProps,
  LayoutChangeEvent,
} from 'react-native';
import { screenInfo, needsHorizontalScroll } from '../utils/responsive';

interface ResponsiveScrollViewProps extends ScrollViewProps {
  children: React.ReactNode;
  containerStyle?: ViewStyle;
  enableAutoHorizontalScroll?: boolean;
  minContentWidth?: number;
}

const ResponsiveScrollView: React.FC<ResponsiveScrollViewProps> = ({
  children,
  containerStyle,
  enableAutoHorizontalScroll = true,
  minContentWidth,
  style,
  ...scrollViewProps
}) => {
  const [contentWidth, setContentWidth] = useState(0);
  const [needsHorizontal, setNeedsHorizontal] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  const handleContentLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setContentWidth(width);
    
    // Verificar se precisa de scroll horizontal
    const shouldScroll = enableAutoHorizontalScroll && (
      needsHorizontalScroll(width) || 
      (minContentWidth && width > minContentWidth)
    );
    
    setNeedsHorizontal(shouldScroll);
  };

  const containerStyles = [
    styles.container,
    containerStyle,
    needsHorizontal && styles.horizontalContainer,
  ];

  const scrollViewStyles = [
    styles.scrollView,
    style,
  ];

  return (
    <ScrollView
      ref={scrollViewRef}
      style={scrollViewStyles}
      horizontal={needsHorizontal}
      showsHorizontalScrollIndicator={needsHorizontal}
      showsVerticalScrollIndicator={!needsHorizontal}
      contentContainerStyle={needsHorizontal ? styles.horizontalContent : undefined}
      {...scrollViewProps}
    >
      <View
        style={containerStyles}
        onLayout={handleContentLayout}
      >
        {children}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  horizontalContainer: {
    minWidth: screenInfo.width,
  },
  scrollView: {
    flex: 1,
  },
  horizontalContent: {
    flexGrow: 1,
  },
});

export default ResponsiveScrollView;
