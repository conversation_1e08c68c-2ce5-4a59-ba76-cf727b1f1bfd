import { Linking } from 'react-native';
import { compatibleFetch, getTecBizNetworkConfig, isAndroid8OrLower } from '../utils/networkConfig';

// Função auxiliar para criar timeout compatível
const createTimeoutSignal = (timeoutMs: number): AbortSignal | undefined => {
  try {
    // Tentar usar AbortSignal.timeout se disponível (versões mais recentes)
    if (typeof AbortSignal !== 'undefined' && typeof (AbortSignal as any).timeout === 'function') {
      return (AbortSignal as any).timeout(timeoutMs);
    }
  } catch (error) {
    console.warn('⚠️ AbortSignal.timeout não disponível, usando fallback');
  }

  // Fallback para Android 8.0 - não usar timeout (será gerenciado pelo compatibleFetch)
  return undefined;
};

// Tipos para Deep Linking
export interface DeepLinkData {
  action: string;
  params: { [key: string]: string };
}

// Função para extrair dados do link de confirmação de email
export const parseEmailConfirmationLink = (url: string): DeepLinkData | null => {
  try {
    console.log('🔗 Analisando link de confirmação:', url);
    
    const urlObj = new URL(url);
    const searchParams = urlObj.searchParams;
    
    // Verificar se é um link de confirmação de email
    const action = searchParams.get('a');
    const modo = searchParams.get('modo');
    const dados = searchParams.get('dados');
    const origem = searchParams.get('origem');
    
    if (action === '657df2' && modo && dados) {
      console.log('✅ Link de confirmação de email válido detectado');

      return {
        action: 'email_confirmation',
        params: {
          modo,
          dados,
          origem: origem || 'WEB', // Aceitar tanto APP quanto WEB
          fullUrl: url
        }
      };
    }
    
    console.log('❌ Link não reconhecido como confirmação de email');
    return null;
    
  } catch (error) {
    console.error('❌ Erro ao analisar link:', error);
    return null;
  }
};

// Função para processar deep links
export const handleDeepLink = (url: string): DeepLinkData | null => {
  try {
    console.log('🔗 Processando deep link:', url);
    
    // Verificar se é link de confirmação de email (HTTP ou HTTPS)
    if (url.includes('www2.tecbiz.com.br') && url.includes('a=657df2')) {
      console.log('🔗 Link de confirmação de email detectado:', url);
      return parseEmailConfirmationLink(url);
    }
    
    // Verificar se é link customizado do app (tecbizapp://)
    if (url.startsWith('tecbizapp://')) {
      const urlObj = new URL(url);
      const action = urlObj.hostname;
      const params: { [key: string]: string } = {};

      urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
      });

      console.log('🔗 Deep link customizado detectado:', { action, params });

      // Se é confirmação de email, extrair dados do usuário dos parâmetros
      if (action === 'email_confirmation' && params.codass && params.codent) {
        console.log('✅ Dados do usuário encontrados no deep link:', {
          codass: params.codass,
          codent: params.codent,
          cartao: params.cartao ? params.cartao.substring(0, 4) + '****' : 'N/A',
          emailCadastro: params.emailCadastro,
          portador: params.portador
        });
      }

      return {
        action,
        params
      };
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Erro ao processar deep link:', error);
    return null;
  }
};

// Função para configurar listener de deep links
export const setupDeepLinkListener = (
  onDeepLink: (data: DeepLinkData) => void
): (() => void) => {
  console.log('🔗 Configurando listener de deep links');
  
  // Listener para links recebidos quando o app está aberto
  const handleUrl = (event: { url: string }) => {
    console.log('🔗 Deep link recebido (app aberto):', event.url);
    const data = handleDeepLink(event.url);
    if (data) {
      onDeepLink(data);
    }
  };
  
  // Adicionar listener
  const subscription = Linking.addEventListener('url', handleUrl);
  
  // Verificar se o app foi aberto por um deep link
  Linking.getInitialURL().then((url) => {
    if (url) {
      console.log('🔗 Deep link inicial (app fechado):', url);
      const data = handleDeepLink(url);
      if (data) {
        // Aguardar mais tempo para o app inicializar completamente (splash screen + navegação)
        setTimeout(() => onDeepLink(data), 3000);
      }
    }
  }).catch((error) => {
    console.error('❌ Erro ao obter URL inicial:', error);
  });
  
  // Retornar função para remover listener
  return () => {
    subscription?.remove();
  };
};

// Função para confirmar email via API
export const confirmarEmail = async (
  modo: string,
  dados: string
): Promise<{ success: boolean; message: string; data?: any }> => {
  try {
    console.log('📧 Confirmando email via API:', { modo, dados });

    // NOTA: Os dados do usuário agora vêm diretamente do PHP via deep link
    // Não precisamos mais decodificar ou buscar dados aqui

    // Estratégia de URLs para máxima compatibilidade
    const isAndroid8 = isAndroid8OrLower();
    console.log('📱 Detectado Android 8.0 ou inferior:', isAndroid8);

    // Para Android 8.0: usar HTTP primeiro (melhor compatibilidade)
    // Para versões atuais: tentar HTTPS primeiro, HTTP como fallback
    const urlsToTry = isAndroid8
      ? [
          `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`,
          `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`
        ]
      : [
          `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`,
          `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${encodeURIComponent(modo)}&dados=${encodeURIComponent(dados)}&origem=APP`
        ];

    console.log('🔗 URLs para tentar:', urlsToTry.map(url => url.startsWith('https') ? 'HTTPS' : 'HTTP'));

    let response: Response | null = null;
    let lastError: Error | null = null;

    // Tentar cada URL até uma funcionar
    for (let i = 0; i < urlsToTry.length; i++) {
      const currentUrl = urlsToTry[i];
      const protocol = currentUrl.startsWith('https') ? 'HTTPS' : 'HTTP';

      try {
        console.log(`🔄 Tentativa ${i + 1}/${urlsToTry.length} - ${protocol}:`, currentUrl);

        // Configurar fetch baseado na versão do Android
        const fetchFunction = isAndroid8 ? compatibleFetch : fetch;
        const requestConfig = isAndroid8 ? getTecBizNetworkConfig() : {};

        console.log('📱 Usando fetch compatível:', isAndroid8 ? 'SIM (Android 8.0)' : 'NÃO (Versão atual)');

        response = await fetchFunction(currentUrl, {
          method: 'GET',
          headers: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'User-Agent': 'TecBizApp/1.0',
            'Cache-Control': 'no-cache',
            ...(isAndroid8 ? {} : { 'Accept-Encoding': 'gzip, deflate, br' })
          },
          ...requestConfig,
          // Timeout específico por tentativa (compatível com Android 8.0)
          signal: isAndroid8 ? undefined : createTimeoutSignal(30000)
        });

        console.log(`✅ Sucesso ${protocol} - Status:`, response.status);
        break; // Sucesso, sair do loop

      } catch (error: any) {
        const errorInfo = {
          message: error.message,
          name: error.name,
          code: error.code,
          stack: error.stack?.substring(0, 200) + '...'
        };

        console.error(`❌ Falha ${protocol}:`, errorInfo);

        // Identificar tipos específicos de erro para Android 8.0
        if (isAndroid8) {
          if (error.message?.includes('SSL') || error.message?.includes('certificate')) {
            console.log('🔒 Erro SSL detectado no Android 8.0 - esperado para HTTPS');
          } else if (error.message?.includes('Network') || error.message?.includes('timeout')) {
            console.log('🌐 Erro de rede detectado no Android 8.0');
          }
        }

        lastError = error;

        // Se não é a última tentativa, continuar
        if (i < urlsToTry.length - 1) {
          console.log(`🔄 Tentando próximo protocolo... (${i + 2}/${urlsToTry.length})`);
          continue;
        }
      }
    }

    // Verificar se conseguiu uma resposta válida
    if (!response) {
      console.error('❌ Todas as tentativas falharam');

      // Criar mensagem de erro específica para diferentes plataformas
      let errorMessage = 'Erro de conexão. Verifique sua internet e tente novamente.';

      if (lastError) {
        if (lastError.message?.includes('SSL') || lastError.message?.includes('certificate')) {
          errorMessage = 'Erro de certificado SSL. Tente novamente ou verifique sua conexão.';
        } else if (lastError.message?.includes('timeout') || lastError.message?.includes('TIMEOUT')) {
          errorMessage = 'Timeout de conexão. Verifique sua internet e tente novamente.';
        } else if (lastError.message?.includes('Network') || lastError.message?.includes('network')) {
          errorMessage = 'Erro de rede. Verifique sua conexão com a internet.';
        } else if (lastError.message?.includes('Failed to fetch') || lastError.message?.includes('fetch')) {
          errorMessage = 'Falha na conexão com o servidor. Verifique sua internet.';
        }
      }

      const finalError = new Error(errorMessage);
      (finalError as any).originalError = lastError;
      throw finalError;
    }

    console.log('📡 Status final confirmação email:', response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📄 Resposta confirmação:', responseText.substring(0, 200) + '...');

    // Verificar se a resposta contém indicação de sucesso
    // O servidor agora retorna HTML com redirecionamento para o app
    if (responseText.includes('E-mail validado com sucesso') ||
        responseText.includes('validado com sucesso') ||
        responseText.includes('tecbizapp://email_confirmation') ||
        response.status === 200) {

      console.log('✅ Email confirmado com sucesso');

      // O PHP já processou tudo e enviará os dados via deep link
      // Não precisamos mais processar dados aqui
      console.log('✅ Email confirmado com sucesso - dados virão via deep link');

      return {
        success: true,
        message: 'E-mail confirmado com sucesso!',
        data: {
          confirmed: true,
          timestamp: Date.now()
        }
      };
    } else if (responseText.includes('Link expirado')) {
      console.log('⏰ Link de confirmação expirado');
      return {
        success: false,
        message: 'Link de confirmação expirado. Solicite um novo link.'
      };
    } else {
      console.log('❌ Falha na confirmação do email');
      return {
        success: false,
        message: 'Falha ao confirmar email. Tente novamente.'
      };
    }

  } catch (error) {
    console.error('❌ Erro ao confirmar email:', error);
    return {
      success: false,
      message: 'Erro de conexão. Verifique sua internet e tente novamente.'
    };
  }
};
