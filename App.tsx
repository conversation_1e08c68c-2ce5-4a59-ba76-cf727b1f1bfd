import React, { useEffect } from 'react';
import AppNavigator from './src/navigation/AppNavigator';
import { AppProvider } from './src/context/AppContext';
import { StatusBar } from 'react-native';
import * as Notifications from 'expo-notifications';
import { initializeNetworkConfig } from './src/utils/networkConfig';
import { applyPolyfills } from './src/utils/polyfills';
// import { SafeAreaProvider } from 'react-native-safe-area-context';

// ⚠️ REGISTRA O HANDLER GLOBAL DE NOTIFICAÇÕES ANTES DE TUDO
// Isso garante que notificações sejam processadas corretamente em produção
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export default function App() {
  useEffect(() => {
    // Aplicar polyfills para compatibilidade com Android 8.0
    applyPolyfills();

    // Inicializar configurações de rede para compatibilidade Android 8.0
    initializeNetworkConfig();

    // StatusBar será configurada individualmente por cada tela
    // Removido configuração global para permitir controle por tela
  }, []);

  return (
    <AppProvider>
      <AppNavigator />
    </AppProvider>
  );
}
