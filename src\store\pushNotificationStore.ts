import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Interface para dados de notificação
interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: { [key: string]: any };
  timestamp: number;
  read: boolean;
  type?: 'info' | 'warning' | 'error' | 'success';
}

// Interface para configurações de push
interface PushSettings {
  enabled: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  showInForeground: boolean;
}

// Interface para o estado da store
interface PushNotificationState {
  // Token FCM
  fcmToken: string | null;
  
  // Configurações
  settings: PushSettings;
  
  // Notificações
  notifications: NotificationData[];
  unreadCount: number;
  
  // Estado do serviço
  isInitialized: boolean;
  hasPermission: boolean;
  
  // Actions
  setFcmToken: (token: string | null) => void;
  updateSettings: (settings: Partial<PushSettings>) => void;
  addNotification: (notification: Omit<NotificationData, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  setInitialized: (initialized: boolean) => void;
  setPermission: (hasPermission: boolean) => void;
  
  // Getters
  getUnreadNotifications: () => NotificationData[];
  getNotificationById: (id: string) => NotificationData | undefined;
}

// Storage personalizado usando uma implementação simples
const customStorage = {
  getItem: (name: string): string | null => {
    try {
      // Usar uma variável global para simular persistência
      const globalStorage = (globalThis as any).pushNotificationStorage || {};
      return globalStorage[name] || null;
    } catch (error) {
      console.error('Erro ao ler do storage:', error);
      return null;
    }
  },
  setItem: (name: string, value: string): void => {
    try {
      // Usar uma variável global para simular persistência
      if (!(globalThis as any).pushNotificationStorage) {
        (globalThis as any).pushNotificationStorage = {};
      }
      (globalThis as any).pushNotificationStorage[name] = value;
      console.log('💾 Dados salvos no storage:', name);
    } catch (error) {
      console.error('Erro ao salvar no storage:', error);
    }
  },
  removeItem: (name: string): void => {
    try {
      const globalStorage = (globalThis as any).pushNotificationStorage || {};
      delete globalStorage[name];
      console.log('🗑️ Dados removidos do storage:', name);
    } catch (error) {
      console.error('Erro ao remover do storage:', error);
    }
  },
};

// Store principal
export const usePushNotificationStore = create<PushNotificationState>()(
  persist(
    (set, get) => ({
      // Estado inicial
      fcmToken: null,
      settings: {
        enabled: true,
        soundEnabled: true,
        vibrationEnabled: true,
        showInForeground: true,
      },
      notifications: [],
      unreadCount: 0,
      isInitialized: false,
      hasPermission: false,

      // Actions
      setFcmToken: (token) => {
        console.log('🎯 Atualizando FCM token na store:', token ? token.substring(0, 20) + '...' : 'null');
        set({ fcmToken: token });
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings }
        }));
        console.log('⚙️ Configurações de push atualizadas:', newSettings);
      },

      addNotification: (notificationData) => {
        const notification: NotificationData = {
          ...notificationData,
          id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
          read: false,
        };

        set((state) => {
          const newNotifications = [notification, ...state.notifications];
          const newUnreadCount = newNotifications.filter(n => !n.read).length;
          
          console.log('📨 Nova notificação adicionada:', notification.title);
          
          return {
            notifications: newNotifications,
            unreadCount: newUnreadCount,
          };
        });
      },

      markAsRead: (notificationId) => {
        set((state) => {
          const updatedNotifications = state.notifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, read: true }
              : notification
          );
          const newUnreadCount = updatedNotifications.filter(n => !n.read).length;
          
          console.log('✅ Notificação marcada como lida:', notificationId);
          
          return {
            notifications: updatedNotifications,
            unreadCount: newUnreadCount,
          };
        });
      },

      markAllAsRead: () => {
        set((state) => {
          const updatedNotifications = state.notifications.map(notification => ({
            ...notification,
            read: true,
          }));
          
          console.log('✅ Todas as notificações marcadas como lidas');
          
          return {
            notifications: updatedNotifications,
            unreadCount: 0,
          };
        });
      },

      removeNotification: (notificationId) => {
        set((state) => {
          const filteredNotifications = state.notifications.filter(
            notification => notification.id !== notificationId
          );
          const newUnreadCount = filteredNotifications.filter(n => !n.read).length;
          
          console.log('🗑️ Notificação removida:', notificationId);
          
          return {
            notifications: filteredNotifications,
            unreadCount: newUnreadCount,
          };
        });
      },

      clearAllNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
        });
        console.log('🧹 Todas as notificações removidas');
      },

      setInitialized: (initialized) => {
        set({ isInitialized: initialized });
        console.log('🔄 Estado de inicialização atualizado:', initialized);
      },

      setPermission: (hasPermission) => {
        set({ hasPermission });
        console.log('🔐 Estado de permissão atualizado:', hasPermission);
      },

      // Getters
      getUnreadNotifications: () => {
        return get().notifications.filter(notification => !notification.read);
      },

      getNotificationById: (id) => {
        return get().notifications.find(notification => notification.id === id);
      },
    }),
    {
      name: 'push-notification-storage',
      storage: createJSONStorage(() => customStorage),
      partialize: (state) => ({
        fcmToken: state.fcmToken,
        settings: state.settings,
        notifications: state.notifications.slice(0, 50), // Manter apenas as 50 mais recentes
        unreadCount: state.unreadCount,
      }),
    }
  )
);

// Hook para facilitar o uso
export const usePushNotifications = () => {
  const store = usePushNotificationStore();
  
  return {
    // Estado
    fcmToken: store.fcmToken,
    settings: store.settings,
    notifications: store.notifications,
    unreadCount: store.unreadCount,
    isInitialized: store.isInitialized,
    hasPermission: store.hasPermission,
    
    // Actions
    setFcmToken: store.setFcmToken,
    updateSettings: store.updateSettings,
    addNotification: store.addNotification,
    markAsRead: store.markAsRead,
    markAllAsRead: store.markAllAsRead,
    removeNotification: store.removeNotification,
    clearAllNotifications: store.clearAllNotifications,
    setInitialized: store.setInitialized,
    setPermission: store.setPermission,
    
    // Getters
    getUnreadNotifications: store.getUnreadNotifications,
    getNotificationById: store.getNotificationById,
    
    // Computed
    hasUnreadNotifications: store.unreadCount > 0,
    isReady: store.isInitialized && store.hasPermission && !!store.fcmToken,
  };
};

export default usePushNotificationStore;
