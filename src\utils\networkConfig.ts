// Configuração de rede para compatibilidade com Android 8.0
import { Platform } from 'react-native';
import { createCompatibleAbortController } from './polyfills';

// Configurações específicas para Android 8.0
export const ANDROID_8_CONFIG = {
  // Timeout mais longo para conexões em Android antigo
  timeout: 120000,
  // Headers adicionais para compatibilidade
  headers: {
    'User-Agent': 'TecBizApp/1.0.0 (Android)',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Cache-Control': 'no-cache',
  },
  // Configurações de retry
  retryConfig: {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error: any) => {
      // Retry em erros de rede ou timeout
      return error.code === 'NETWORK_ERROR' ||
             error.code === 'TIMEOUT' ||
             error.message?.includes('SSL') ||
             error.message?.includes('certificate');
    }
  }
};

// Configurações específicas para Android 8.1
export const ANDROID_81_CONFIG = {
  timeout: 150000, // Timeout ainda maior para 8.1
  headers: {
    'User-Agent': 'TecBizApp/1.0.0 (Android 8.1)',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'identity', // Desabilitar compressão que pode causar problemas
    'Connection': 'close', // Forçar conexão fechada para evitar problemas de keep-alive
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
  },
  retryConfig: {
    retries: 5, // Mais tentativas para 8.1
    retryDelay: 2000, // Delay maior entre tentativas
    retryCondition: (error: any) => {
      return error.code === 'NETWORK_ERROR' ||
             error.code === 'TIMEOUT' ||
             error.message?.includes('SSL') ||
             error.message?.includes('certificate') ||
             error.message?.includes('ECONNRESET') ||
             error.message?.includes('ENOTFOUND');
    }
  }
};

// Configurações específicas para Android 9.0
export const ANDROID_9_CONFIG = {
  timeout: 180000, // Timeout muito maior para 9.0
  headers: {
    'User-Agent': 'TecBizApp/1.0.0 (Android 9.0)',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'identity', // Sem compressão
    'Connection': 'close', // Conexão fechada
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    // Headers específicos para contornar problemas de segurança do Android 9
    'X-Requested-With': 'XMLHttpRequest',
    'Origin': 'https://www2.tecbiz.com.br',
  },
  retryConfig: {
    retries: 7, // Ainda mais tentativas para 9.0
    retryDelay: 3000, // Delay ainda maior
    retryCondition: (error: any) => {
      return error.code === 'NETWORK_ERROR' ||
             error.code === 'TIMEOUT' ||
             error.message?.includes('SSL') ||
             error.message?.includes('certificate') ||
             error.message?.includes('ECONNRESET') ||
             error.message?.includes('ENOTFOUND') ||
             error.message?.includes('CLEARTEXT') ||
             error.message?.includes('SECURITY');
    }
  }
};

/**
 * Verificar se é Android 8.0 ou inferior
 */
export const isAndroid8OrLower = (): boolean => {
  if (Platform.OS !== 'android') return false;

  try {
    const version = Platform.Version;
    return typeof version === 'number' && version <= 26; // API 26 = Android 8.0
  } catch (error) {
    console.warn('Erro ao verificar versão do Android:', error);
    return false;
  }
};

/**
 * Verificar se é Android 8.1 (API 27)
 */
export const isAndroid81 = (): boolean => {
  if (Platform.OS !== 'android') return false;

  try {
    const version = Platform.Version;
    return typeof version === 'number' && version === 27; // API 27 = Android 8.1
  } catch (error) {
    console.warn('Erro ao verificar versão do Android:', error);
    return false;
  }
};

/**
 * Verificar se é Android 9.0 (API 28)
 */
export const isAndroid9 = (): boolean => {
  if (Platform.OS !== 'android') return false;

  try {
    const version = Platform.Version;
    return typeof version === 'number' && version === 28; // API 28 = Android 9.0
  } catch (error) {
    console.warn('Erro ao verificar versão do Android:', error);
    return false;
  }
};

/**
 * Verificar se é uma versão problemática do Android (8.1 ou 9.0)
 */
export const isProblematicAndroidVersion = (): boolean => {
  return isAndroid81() || isAndroid9();
};

/**
 * Obter informações detalhadas sobre a versão do Android para debug
 */
export const getAndroidVersionInfo = () => {
  if (Platform.OS !== 'android') {
    return { platform: 'not-android', version: null, isProblematic: false };
  }

  const version = Platform.Version;
  const versionNumber = typeof version === 'number' ? version : parseInt(version?.toString() || '0');

  let versionName = 'Unknown';
  if (versionNumber <= 26) versionName = '8.0 or lower';
  else if (versionNumber === 27) versionName = '8.1';
  else if (versionNumber === 28) versionName = '9.0';
  else if (versionNumber === 29) versionName = '10.0';
  else if (versionNumber >= 30) versionName = '11.0+';

  return {
    platform: 'android',
    apiLevel: versionNumber,
    versionName,
    version: version,
    isProblematic: isProblematicAndroidVersion(),
    is8OrLower: isAndroid8OrLower(),
    is81: isAndroid81(),
    is9: isAndroid9(),
  };
};

/**
 * Logar informações de debug sobre a versão do Android
 */
export const logAndroidVersionDebug = () => {
  const info = getAndroidVersionInfo();
  console.log('📱 Informações da versão do Android:', info);

  if (info.isProblematic) {
    console.log('⚠️ Versão problemática detectada - usando configurações otimizadas');
  }

  return info;
};

/**
 * Obter configurações de rede baseadas na versão do Android
 */
export const getNetworkConfig = () => {
  const baseConfig = {
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }
  };

  // Configurações específicas para Android 9.0 (mais problemático)
  if (isAndroid9()) {
    console.log('📱 Aplicando configurações otimizadas para Android 9.0');
    return {
      ...baseConfig,
      ...ANDROID_9_CONFIG,
      headers: {
        ...baseConfig.headers,
        ...ANDROID_9_CONFIG.headers,
      }
    };
  }

  // Configurações específicas para Android 8.1
  if (isAndroid81()) {
    console.log('📱 Aplicando configurações otimizadas para Android 8.1');
    return {
      ...baseConfig,
      ...ANDROID_81_CONFIG,
      headers: {
        ...baseConfig.headers,
        ...ANDROID_81_CONFIG.headers,
      }
    };
  }

  // Configurações para Android 8.0 ou inferior
  if (isAndroid8OrLower()) {
    console.log('📱 Aplicando configurações para Android 8.0 ou inferior');
    return {
      ...baseConfig,
      ...ANDROID_8_CONFIG,
      headers: {
        ...baseConfig.headers,
        ...ANDROID_8_CONFIG.headers,
      }
    };
  }

  return baseConfig;
};

/**
 * Fetch otimizado para versões problemáticas do Android (8.1 e 9.0)
 */
export const problematicAndroidFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const config = getNetworkConfig();
  const controller = createCompatibleAbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, config.timeout);

  // Estratégias específicas para versões problemáticas
  const strategies = [];

  if (isAndroid9()) {
    // Android 9.0: Tentar múltiplas abordagens
    strategies.push(
      // 1. HTTP primeiro (mais compatível)
      { url: url.replace('https://', 'http://'), description: 'HTTP direto (Android 9.0)' },
      // 2. HTTPS com headers específicos
      { url: url, description: 'HTTPS com headers otimizados (Android 9.0)' },
      // 3. HTTP com porta específica
      { url: url.replace('https://', 'http://').replace(':443', ':80'), description: 'HTTP porta 80 (Android 9.0)' }
    );
  } else if (isAndroid81()) {
    // Android 8.1: Abordagem mais conservadora
    strategies.push(
      // 1. HTTP primeiro
      { url: url.replace('https://', 'http://'), description: 'HTTP direto (Android 8.1)' },
      // 2. HTTPS simples
      { url: url, description: 'HTTPS simples (Android 8.1)' }
    );
  } else {
    // Fallback para outras versões
    strategies.push({ url: url, description: 'URL original' });
  }

  let lastError: Error | null = null;

  for (const strategy of strategies) {
    try {
      console.log(`🔄 Tentando: ${strategy.description} - ${strategy.url}`);

      const response = await fetch(strategy.url, {
        ...options,
        headers: {
          ...config.headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        console.log(`✅ Sucesso com: ${strategy.description}`);
        return response;
      } else {
        console.log(`⚠️ Resposta não OK (${response.status}) com: ${strategy.description}`);
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ Erro com ${strategy.description}:`, error);
      lastError = error as Error;

      // Pequeno delay entre tentativas para versões problemáticas
      if (isProblematicAndroidVersion()) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  clearTimeout(timeoutId);
  throw lastError || new Error('Todas as estratégias de conexão falharam');
};

/**
 * Wrapper para fetch com configurações específicas do Android 8.0
 * Versão simplificada para evitar loops infinitos
 */
export const compatibleFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  // Para versões problemáticas do Android (8.1 e 9.0), usar fetch otimizado
  if (isProblematicAndroidVersion()) {
    console.log('🔧 Usando fetch otimizado para versão problemática do Android');
    return problematicAndroidFetch(url, options);
  }

  // Para Android 8.0, usar AbortController manual ao invés de AbortSignal.timeout
  if (isAndroid8OrLower()) {
    const controller = createCompatibleAbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 90000); // 90 segundos para Android 8.0

    // Para Android 8.0, ir direto para HTTP se a URL for HTTPS
    const urlsToTry = [];

    if (url.startsWith('https://')) {
      // Android 8.0 tem problemas com HTTPS - ir direto para HTTP
      console.log('🔄 Android 8.0 detectado - usando HTTP diretamente para melhor compatibilidade');
      urlsToTry.push(url.replace('https://', 'http://')); // HTTP primeiro
      urlsToTry.push(url); // HTTPS como fallback (caso o servidor não aceite HTTP)
    } else {
      urlsToTry.push(url); // Manter URL original
    }

    for (let urlIndex = 0; urlIndex < urlsToTry.length; urlIndex++) {
      const currentUrl = urlsToTry[urlIndex];
      const isHttpFirst = urlIndex === 0 && currentUrl.startsWith('http://') && url.startsWith('https://');
      const isHttpsFallback = urlIndex > 0 && currentUrl.startsWith('https://');

      if (isHttpFirst) {
        console.log('🚀 Android 8.0 - Usando HTTP diretamente (otimizado)');
      } else if (isHttpsFallback) {
        console.log('🔄 HTTP falhou, tentando HTTPS como fallback...');
      }

      // Configurações para tentar
      const configurations = [
        // Configuração 1: Headers básicos
        {
          ...options,
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Cache-Control': 'no-cache',
            ...options.headers,
          },
          signal: controller.signal,
        },
        // Configuração 2: Sem headers extras
        {
          ...options,
          signal: controller.signal,
        }
      ];

      for (let i = 0; i < configurations.length; i++) {
        const config = configurations[i];

        try {
          const protocol = currentUrl.startsWith('https://') ? 'HTTPS' : 'HTTP';
          const priority = (isHttpFirst && protocol === 'HTTP') ? '(OTIMIZADO)' :
                          (isHttpsFallback && protocol === 'HTTPS') ? '(FALLBACK)' : '';

          console.log(`🔗 Tentativa ${protocol} ${priority} ${i + 1}/${configurations.length} para:`, currentUrl);
          console.log('🔧 Headers enviados:', JSON.stringify(config.headers, null, 2));

          const response = await fetch(currentUrl, config);
          clearTimeout(timeoutId);

          console.log(`✅ Sucesso ${protocol} ${priority} - Status:`, response.status);
          return response;
        } catch (error: any) {
          const protocol = currentUrl.startsWith('https://') ? 'HTTPS' : 'HTTP';
          const priority = (isHttpFirst && protocol === 'HTTP') ? '(OTIMIZADO)' :
                          (isHttpsFallback && protocol === 'HTTPS') ? '(FALLBACK)' : '';

          console.error(`❌ Tentativa ${protocol} ${priority} ${i + 1} falhou:`, {
            message: error.message,
            name: error.name,
          });

          // Se não é a última configuração desta URL, continuar
          if (i < configurations.length - 1) {
            console.log(`🔄 Tentando próxima configuração...`);
            continue;
          }

          // Se não é a última URL, tentar próxima URL
          if (urlIndex < urlsToTry.length - 1) {
            console.log(`🔄 Tentando próximo protocolo...`);
            break; // Sair do loop de configurações para tentar próxima URL
          }

          // Última tentativa de tudo falhou
          clearTimeout(timeoutId);

          if (error.name === 'AbortError') {
            throw new Error('TIMEOUT');
          }
          throw error;
        }
      }
    }
  }

  // Para versões mais recentes, usar comportamento padrão
  return fetch(url, options);
};

/**
 * Configurar interceptadores globais para melhor compatibilidade
 * DESABILITADO - estava causando loop infinito com Metro bundler
 */
export const setupNetworkInterceptors = () => {
  if (isAndroid8OrLower()) {
    console.log('🔧 Interceptadores de rede desabilitados para evitar loop infinito');
    // Interceptador global removido para evitar conflito com Metro bundler
    // As correções de compatibilidade são aplicadas diretamente no compatibleFetch
  }
};

/**
 * Verificar conectividade de rede específica para Android 8.0
 */
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    await compatibleFetch('https://www.google.com', {
      method: 'HEAD',
      mode: 'no-cors',
    });

    return true;
  } catch (error) {
    console.warn('⚠️ Problema de conectividade detectado:', error);
    return false;
  }
};

/**
 * Configurações específicas para o domínio da TecBiz
 */
export const getTecBizNetworkConfig = () => {
  const baseConfig = getNetworkConfig();

  // Configurações específicas para versões problemáticas (8.1 e 9.0)
  if (isProblematicAndroidVersion()) {
    return {
      ...baseConfig,
      headers: {
        ...baseConfig.headers,
        // Headers otimizados para versões problemáticas
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'pt-BR,pt;q=0.9',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
      // Sem credentials para versões problemáticas
      mode: 'cors' as RequestMode,
    };
  }

  // Configurações específicas para Android 8.0
  if (isAndroid8OrLower()) {
    return {
      ...baseConfig,
      headers: {
        ...baseConfig.headers,
        // Headers simplificados para Android 8.0
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'pt-BR,pt;q=0.9',
        'Cache-Control': 'no-cache',
      },
      // Remover credentials para Android 8.0 (pode causar problemas)
      mode: 'cors' as RequestMode,
    };
  }

  // Configurações para versões mais recentes
  return {
    ...baseConfig,
    headers: {
      ...baseConfig.headers,
      // Headers específicos para a API da TecBiz
      'X-Requested-With': 'XMLHttpRequest',
      'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
    },
    // Configurações específicas para HTTPS da TecBiz
    credentials: 'include' as RequestCredentials,
  };
};

/**
 * Inicializar configurações de rede
 */
export const initializeNetworkConfig = () => {
  console.log('🌐 Inicializando configurações de rede...');

  // Logar informações detalhadas sobre a versão do Android
  const androidInfo = logAndroidVersionDebug();

  if (androidInfo.isProblematic) {
    console.log('🔧 Aplicando configurações otimizadas para versão problemática');
  } else if (isAndroid8OrLower()) {
    console.log('📱 Detectado Android 8.0 ou inferior - aplicando configurações especiais');
    setupNetworkInterceptors();
  }

  console.log('✅ Configurações de rede inicializadas');
};

export default {
  isAndroid8OrLower,
  getNetworkConfig,
  compatibleFetch,
  setupNetworkInterceptors,
  checkNetworkConnectivity,
  getTecBizNetworkConfig,
  initializeNetworkConfig,
  ANDROID_8_CONFIG,
};
