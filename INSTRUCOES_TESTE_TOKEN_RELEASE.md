# 🔍 Instruções para Testar Token Push no APK Release

## 📱 **PROBLEMA IDENTIFICADO**
O token estava sendo simulado porque:
1. **API Cloud Messaging (legada) foi DESCONTINUADA** pelo Firebase
2. Precisa migrar para **Firebase Cloud Messaging API v1**
3. O token não estava sendo enviado como parâmetro `tokenid` no login
4. Faltava debug adequado para APK release

## ✅ **CORREÇÕES IMPLEMENTADAS**

### 1. **Login com Token Push (Integrado)**
- ✅ Token agora é enviado como parâmetro `tokenid` no login
- ✅ Token é processado diretamente pelo `login.php` existente
- ✅ Integração com tabela `tbz_token_app_ass` já implementada
- ✅ Logs detalhados para debug

### 2. **Componente de Debug**
- ✅ Botão "🔍 Debug Token" na tela de login (apenas em desenvolvimento)
- ✅ Modal com informações completas do token
- ✅ Teste de registro no backend
- ✅ Envio de debug info para servidor

### 3. **Endpoint de Debug**
- ✅ `debug_token_release.php` para debug remoto
- ✅ Verificação de tokens no banco
- ✅ Estatísticas de tokens reais vs simulados

## 🧪 **COMO TESTAR NO APK RELEASE**

### **Passo 1: Gerar APK Release**
```bash
# No diretório do projeto
npx expo build:android --type=apk
# ou
eas build --platform android --profile production
```

### **Passo 2: Instalar e Testar**
1. **Instale o APK** no dispositivo físico
2. **Abra o app** e faça login
3. **Verifique os logs** (se possível via ADB):
   ```bash
   adb logcat | grep -i "token\|firebase\|expo"
   ```

### **Passo 3: Testar Login com Token**
1. **Teste primeiro**: `https://www2.tecbiz.com.br/test_login_with_token.php?a=2547a0&carass=SEU_CARTAO&senass=SUA_SENHA&tokenid=TESTE`
2. **Verifique** se o parâmetro `tokenid` está sendo recebido
3. **Faça login real** no app e verifique logs

### **Passo 4: Verificar Token no Banco**
1. **Consulte** a tabela `tbz_token_app_ass` no banco
2. **Procure** por tokens que começam com `ExponentPushToken[`
3. **Verifique** se o token foi associado ao usuário correto

### **Passo 5: Testar Push Notification**
1. **Use o token** obtido no `test_push_notification.php`
2. **Configure** para usar **FCM API v1** (não legada)
3. **Teste** envio via endpoint atualizado

## 🔧 **CONFIGURAÇÕES FIREBASE NECESSÁRIAS**

### **No Firebase Console:**
1. **Vá para**: Configurações do projeto > Cloud Messaging
2. **ATIVE** a API Cloud Messaging (legada) - **IMPORTANTE!**
3. **Verifique** se a API Firebase Cloud Messaging (V1) está ativa
4. **Baixe** o `google-services.json` atualizado se necessário

### **Configurações Verificadas:**
- ✅ Project ID: `tecbizappass`
- ✅ Project Number: `608372388905`
- ✅ Package Name: `com.tecbiz.tecbizassociadospush`
- ✅ API Key: `AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk`

## 📊 **LOGS ESPERADOS NO APK RELEASE**

### **Token Real (Sucesso):**
```
🎯 Token Expo REAL obtido: ExponentPushToken[xxxxxx]...
✅ Token válido para produção!
📤 Enviando token push para o backend...
✅ Token push enviado com sucesso!
```

### **Token Simulado (Problema):**
```
⚠️ Token obtido não é um token real do Expo
🎯 Token recebido: tecbiz_dev_xxxxx
💡 Possível causa: Executando no Expo Go ou emulador
```

## 🚨 **AÇÕES NECESSÁRIAS NO FIREBASE**

### **IMPORTANTE - Migração para FCM v1:**
1. **A API legada foi DESCONTINUADA** pelo Firebase
2. **Use apenas**: Firebase Cloud Messaging API (v1) ✅ Ativado
3. **NÃO tente** ativar a API legada (não funciona mais)
4. **Configure** Service Account para autenticação OAuth 2.0

### **Configurar Service Account:**
1. **Acesse**: [Firebase Console](https://console.firebase.google.com/project/tecbizappass/settings/serviceaccounts/adminsdk)
2. **Gere** nova chave privada JSON
3. **Configure** no servidor para usar OAuth 2.0
4. **Endpoint**: `https://fcm.googleapis.com/v1/projects/tecbizappass/messages:send`

## 📱 **TESTE MANUAL NO DISPOSITIVO**

### **Se tiver acesso ao dispositivo:**
1. **Instale** o APK
2. **Faça login** com suas credenciais
3. **Observe** se aparece notificação de token obtido
4. **Use** o botão "🔍 Debug Token" (se em modo dev)
5. **Copie** o token completo dos logs

### **Se não tiver acesso aos logs:**
1. **Faça login** no app
2. **Acesse**: `https://www2.tecbiz.com.br/debug_token_release.php`
3. **Verifique** se seu token aparece na lista
4. **Procure** por tokens recentes com seu user_id

## 🔄 **PRÓXIMOS PASSOS**

### **Se o token ainda for simulado:**
1. **Gere APK release** (não use Expo Go)
2. **Teste** em dispositivo físico (não emulador)
3. **Verifique** se o `google-services.json` está atualizado
4. **Configure** Service Account para FCM v1

### **Se o token for real:**
1. **Use** `send_push_fcm_v1.php` para envio
2. **Configure** Service Account JSON no servidor
3. **Teste** envio via FCM API v1
4. **Verifique** se as notificações chegam no dispositivo

## 📞 **SUPORTE**

Se ainda houver problemas:
1. **Envie** os logs do `debug_token_release.php`
2. **Compartilhe** o status das APIs no Firebase Console
3. **Informe** se o token aparece como real ou simulado
4. **Teste** com diferentes usuários/dispositivos

---

**🎯 OBJETIVO**: Obter token real `ExponentPushToken[...]` e registrá-lo no backend para envio de push notifications.
