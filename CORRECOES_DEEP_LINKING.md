# Correções do Deep Linking - Alteração de Senha

## Problemas Identificados

### 1. **Dados do usuário não encontrados**
- **Problema**: Quando o usuário vem do link do email, a tela `AlterarSenhaScreen` não recebe os dados necessários (`codass`, `codent`)
- **Causa**: Os dados do usuário não estavam sendo passados do PHP para o app via deep link
- **Sintoma**: <PERSON>rro "Dados do usuário não encontrados. Faça login novamente"

### 2. **Compatibilidade Android 8**
- **Problema**: Erro de conexão em Android 8.0 e versões anteriores
- **Causa**: Problemas com HTTPS e fetch nativo em versões antigas do Android
- **Sintoma**: "Erro de conexão. Verifique sua internet e tente novamente"

## Soluções Implementadas

### 1. **Modificação no PHP (`pg_ass_login.php`)**

#### A. Inclusão de dados do usuário no deep link
```php
// Construir URL do deep link para o app com dados do usuário
$appUrl = "tecbizapp://email_confirmation?" . http_build_query([
    'modo' => $_REQUEST['modo'],
    'dados' => $_REQUEST['dados'],
    'origem' => $_REQUEST['origem'],
    'status' => 'success',
    'message' => 'E-mail validado com sucesso!',
    // ADICIONAR DADOS DO USUÁRIO PARA O APP
    'codass' => $Cartao->associado->codigo,
    'codent' => $Cartao->associado->entidade->codigo,
    'cartao' => $numeroCartao,
    'emailCadastro' => $Cartao->associado->asslogema,
    'portador' => $Cartao->associado->assnomass
]);
```

**Vantagens desta abordagem:**
- ✅ Segura: usa dados já validados pelo PHP
- ✅ Simples: não precisa "adivinhar" senhas
- ✅ Direta: dados vêm prontos para uso
- ✅ Confiável: PHP já tem toda a lógica de validação

### 2. **Melhorias no `deepLinking.ts`**

#### A. Captura de dados do deep link
```typescript
// Se é confirmação de email, extrair dados do usuário dos parâmetros
if (action === 'email_confirmation' && params.codass && params.codent) {
  console.log('✅ Dados do usuário encontrados no deep link:', {
    codass: params.codass,
    codent: params.codent,
    cartao: params.cartao ? params.cartao.substring(0, 4) + '****' : 'N/A',
    emailCadastro: params.emailCadastro,
    portador: params.portador
  });
}
```

#### B. Uso de fetch compatível com Android 8
```typescript
// Usar HTTP ao invés de HTTPS para melhor compatibilidade
const url = `http://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=${modo}&dados=${dados}&origem=APP`;

// Usar fetch compatível
const { compatibleFetch, getTecBizNetworkConfig, isAndroid8OrLower } = await import('../utils/networkConfig');
const fetchFunction = isAndroid8OrLower() ? compatibleFetch : fetch;
```

### 3. **Melhorias no `AppNavigator.tsx`**

#### A. Uso direto dos dados do deep link
```typescript
const handleDeepLink = async (data: DeepLinkData) => {
  // Verificar se temos os dados essenciais do usuário vindos do PHP
  if (data.params.codass && data.params.codent) {
    console.log('✅ Dados do usuário encontrados no deep link');

    // Confirmar email via API (apenas para marcar como processado)
    const result = await confirmarEmail(data.params.modo, data.params.dados);

    if (result.success) {
      setAlterarSenhaParams({
        fromEmail: true,
        emailConfirmed: true,
        userData: {
          codass: data.params.codass,
          codent: data.params.codent,
          cartao: data.params.cartao,
          emailCadastro: data.params.emailCadastro,
          portador: data.params.portador
        },
        hideMenu: true,
        senhaInicial: true
      });
      navigate('AlterarSenha');
    }
  }
}
```

### 4. **Simplificação do `AlterarSenhaScreen.tsx`**

#### A. Lógica simplificada para obter dados do usuário
```typescript
// Verificar se veio do esqueceu senha ou login (tem dados do usuário nos parâmetros)
if ((fromEmail || fromLogin) && route?.params?.userData) {
  // Usar dados do esqueceu senha, login ou deep link (todos vêm via userData)
  codass = route.params.userData.codass.toString();
  codent = route.params.userData.codent.toString();
  console.log(`📧 Usando dados do ${fromLogin ? 'login' : 'deep link/esqueceu senha'}:`, { codass, codent });
} else {
  // Obter dados do usuário do contexto (login normal)
  const usuarioData = getUsuarioData();
  if (!usuarioData) {
    Alert.alert('Erro', 'Dados do usuário não encontrados. Faça login novamente.');
    navigation.navigate('Login');
    return;
  }
  codass = usuarioData.usuario.codass.toString();
  codent = usuarioData.usuario.codent.toString();
}
```

## Fluxo Corrigido

### 1. **Esqueceu Senha → Email**
1. `EsqueceuSenhaScreen` → envia email com `origem=APP`
2. Email contém link: `https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=657df2&modo=...&dados=...&origem=APP`

### 2. **Link do Email → Deep Link**
1. PHP processa confirmação e extrai dados do usuário
2. PHP redireciona: `tecbizapp://email_confirmation?modo=...&dados=...&codass=123&codent=456&cartao=...`
3. App recebe deep link via `setupDeepLinkListener()`

### 3. **Deep Link → Confirmação**
1. `handleDeepLink()` extrai dados diretamente dos parâmetros do deep link
2. `confirmarEmail()` apenas confirma o processamento (dados já estão disponíveis)
3. Dados do usuário (`codass`, `codent`, etc.) vêm prontos do PHP

### 4. **Confirmação → Alterar Senha**
1. `AppNavigator` navega para `AlterarSenha` com `userData` completo
2. `AlterarSenhaScreen` usa dados diretamente (sem busca adicional)
3. Chama API de alteração de senha com `codass`/`codent` corretos

## Compatibilidade Android 8

### Problemas Resolvidos:
- ✅ Uso de HTTP ao invés de HTTPS
- ✅ Fetch compatível com versões antigas
- ✅ Timeout aumentado para 90 segundos
- ✅ Headers apropriados para Android antigo

### Configurações de Rede:
```typescript
const REQUEST_TIMEOUT = isAndroid8OrLower() ? 90000 : 60000;
const fetchFunction = isAndroid8OrLower() ? compatibleFetch : fetch;
const networkConfig = isAndroid8OrLower() ? getTecBizNetworkConfig() : {};
```

## Testes Necessários

### 1. **Fluxo Completo**
- [ ] Esqueceu senha → receber email
- [ ] Clicar no link do email → abrir app
- [ ] Definir nova senha → sucesso
- [ ] Login com nova senha → funcionar

### 2. **Compatibilidade**
- [ ] Testar em Android 8.0
- [ ] Testar em Android 9+
- [ ] Testar conectividade HTTP vs HTTPS

### 3. **Casos Edge**
- [ ] Link expirado
- [ ] Dados inválidos
- [ ] Sem conexão de internet
- [ ] App não instalado (fallback navegador)

## Arquivos Modificados

1. **`pg_ass_login.php`** ⭐ **PRINCIPAL**
   - Inclusão de dados do usuário no deep link
   - `codass`, `codent`, `cartao`, `emailCadastro`, `portador`

2. **`src/services/deepLinking.ts`**
   - Captura de dados do deep link
   - Fetch compatível com Android 8
   - Simplificação da lógica (dados vêm do PHP)

3. **`src/navigation/AppNavigator.tsx`**
   - Uso direto dos dados do deep link
   - Passagem correta de `userData` para `AlterarSenha`

4. **`src/screens/AlterarSenhaScreen.tsx`**
   - Simplificação da lógica de obtenção de dados
   - Remoção de código complexo desnecessário
