/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';

global.setTimeout = global.setTimeout || (() => {});
global.clearTimeout = global.clearTimeout || (() => {});
global.setImmediate = global.setImmediate || (() => {});
//global.XMLHttpRequest = global.XMLHttpRequest || function () {};
//global.fetch = global.fetch || function () {};

AppRegistry.registerComponent(appName, () => App);
