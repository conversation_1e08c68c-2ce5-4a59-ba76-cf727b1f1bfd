// Função para bloquear rotação (apenas retrato)
// Implementação simplificada sem dependências do React Native
export const lockToPortrait = () => {
  // No React Native, a orientação é controlada principalmente pelo Android/iOS nativo
  // Esta função serve como placeholder para futuras implementações
  console.log('📱 Orientação bloqueada para modo retrato');

  // Em produção, seria necessário usar bibliotecas nativas como:
  // react-native-orientation-locker para controle completo
};

// Hook para monitorar mudanças de orientação
export const useOrientationLock = () => {
  // Implementação básica sem dependências
  // Em produção seria necessário usar bibliotecas nativas

  return {
    orientation: 'portrait' as const,
    isPortrait: true
  };
};
