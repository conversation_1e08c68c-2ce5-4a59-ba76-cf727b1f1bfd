import React from 'react';
import { Text, TextProps, StyleSheet } from 'react-native';
import {
  getResponsiveTextSize,
  getResponsiveTitleSize,
  isSmallScreen,
  isLargeScreen,
} from '../utils/responsive';

interface ResponsiveTextProps extends TextProps {
  variant?: 'title' | 'subtitle' | 'body' | 'caption' | 'button';
  size?: 'small' | 'medium' | 'large';
  adjustsFontSizeToFit?: boolean;
  minimumFontScale?: number;
}

const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  variant = 'body',
  size = 'medium',
  adjustsFontSizeToFit = true,
  minimumFontScale = 0.8,
  style,
  children,
  ...textProps
}) => {
  const getBaseSize = () => {
    const sizeMap = {
      small: { title: 20, subtitle: 16, body: 14, caption: 12, button: 14 },
      medium: { title: 24, subtitle: 18, body: 16, caption: 14, button: 16 },
      large: { title: 28, subtitle: 20, body: 18, caption: 16, button: 18 },
    };
    
    return sizeMap[size][variant];
  };

  const getResponsiveSize = () => {
    const baseSize = getBaseSize();
    
    if (variant === 'title' || variant === 'subtitle') {
      return getResponsiveTitleSize(baseSize);
    }
    
    return getResponsiveTextSize(baseSize);
  };

  const getLineHeight = () => {
    const fontSize = getResponsiveSize();
    return fontSize * 1.4;
  };

  const responsiveStyles = StyleSheet.create({
    text: {
      fontSize: getResponsiveSize(),
      lineHeight: getLineHeight(),
      // Ajustes específicos para telas pequenas
      ...(isSmallScreen() && {
        letterSpacing: variant === 'title' ? -0.5 : 0,
      }),
      // Ajustes específicos para telas grandes
      ...(isLargeScreen() && {
        letterSpacing: variant === 'title' ? 0.5 : 0.2,
      }),
    },
  });

  const combinedStyles = [
    responsiveStyles.text,
    getVariantStyles(variant),
    style,
  ];

  return (
    <Text
      style={combinedStyles}
      adjustsFontSizeToFit={adjustsFontSizeToFit}
      minimumFontScale={minimumFontScale}
      numberOfLines={variant === 'title' ? 2 : undefined}
      {...textProps}
    >
      {children}
    </Text>
  );
};

const getVariantStyles = (variant: string) => {
  switch (variant) {
    case 'title':
      return styles.title;
    case 'subtitle':
      return styles.subtitle;
    case 'body':
      return styles.body;
    case 'caption':
      return styles.caption;
    case 'button':
      return styles.button;
    default:
      return styles.body;
  }
};

const styles = StyleSheet.create({
  title: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  subtitle: {
    fontWeight: '600',
  },
  body: {
    fontWeight: 'normal',
  },
  caption: {
    fontWeight: 'normal',
    opacity: 0.7,
  },
  button: {
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default ResponsiveText;
