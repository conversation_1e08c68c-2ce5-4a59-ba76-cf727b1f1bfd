-- Script SQL para criar tabelas OTIMIZADAS do sistema de notificações push
-- Execute este script no PostgreSQL do seu servidor

-- =====================================================
-- TABELA 1: tbz_push_tokens (Tokens dos usuários)
-- =====================================================

CREATE TABLE IF NOT EXISTS tbz_push_tokens (
    id SERIAL PRIMARY KEY,
    token VARCHAR(500) UNIQUE NOT NULL,
    codass VARCHAR(50),
    codent VARCHAR(50),
    cartao VARCHAR(50),
    dispositivo VARCHAR(100),
    ativo BOOLEAN DEFAULT true,
    data_criacao TIMESTAMP DEFAULT NOW(),
    data_atualizacao TIMESTAMP DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_tbz_push_tokens_ativo ON tbz_push_tokens(ativo);
CREATE INDEX IF NOT EXISTS idx_tbz_push_tokens_codass ON tbz_push_tokens(codass);
CREATE INDEX IF NOT EXISTS idx_tbz_push_tokens_codent ON tbz_push_tokens(codent);
CREATE INDEX IF NOT EXISTS idx_tbz_push_tokens_cartao ON tbz_push_tokens(cartao);
CREATE INDEX IF NOT EXISTS idx_tbz_push_tokens_data_atualizacao ON tbz_push_tokens(data_atualizacao);

-- =====================================================
-- TABELA 2: tbz_notificacoes (Notificações enviadas)
-- =====================================================

CREATE TABLE IF NOT EXISTS tbz_notificacoes (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(255) NOT NULL,
    mensagem TEXT NOT NULL,
    codass VARCHAR(50),
    codent VARCHAR(50),
    cartao VARCHAR(50),
    dados_json TEXT,
    data_envio TIMESTAMP DEFAULT NOW(),
    -- Arrays para controle eficiente por token
    tokens_enviados TEXT[], -- Tokens que receberam a notificação
    tokens_entregues TEXT[], -- Tokens que confirmaram recebimento
    tokens_lidos TEXT[], -- Tokens que marcaram como lido
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_data_envio ON tbz_notificacoes(data_envio);
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_ativo ON tbz_notificacoes(ativo);
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_codass ON tbz_notificacoes(codass);
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_codent ON tbz_notificacoes(codent);

-- Índices GIN para arrays (muito importante para performance)
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_tokens_enviados ON tbz_notificacoes USING GIN(tokens_enviados);
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_tokens_entregues ON tbz_notificacoes USING GIN(tokens_entregues);
CREATE INDEX IF NOT EXISTS idx_tbz_notificacoes_tokens_lidos ON tbz_notificacoes USING GIN(tokens_lidos);

-- =====================================================
-- COMENTÁRIOS E EXPLICAÇÕES
-- =====================================================

COMMENT ON TABLE tbz_push_tokens IS 'Tabela otimizada para armazenar tokens de push notification dos usuários';
COMMENT ON COLUMN tbz_push_tokens.token IS 'Token único do dispositivo (ExponentPushToken[...] ou FCM)';
COMMENT ON COLUMN tbz_push_tokens.ativo IS 'Se false, token será ignorado nos envios';

COMMENT ON TABLE tbz_notificacoes IS 'Tabela otimizada para notificações - UMA notificação para TODOS os tokens';
COMMENT ON COLUMN tbz_notificacoes.tokens_enviados IS 'Array de tokens que receberam esta notificação';
COMMENT ON COLUMN tbz_notificacoes.tokens_entregues IS 'Array de tokens que confirmaram recebimento';
COMMENT ON COLUMN tbz_notificacoes.tokens_lidos IS 'Array de tokens que marcaram como lido';

-- =====================================================
-- EXEMPLOS DE USO
-- =====================================================

-- Inserir token de usuário
-- INSERT INTO tbz_push_tokens (token, codass, codent, cartao, dispositivo) 
-- VALUES ('ExponentPushToken[xxxxx]', '001', '001', '12345', 'Android');

-- Inserir notificação (feito pelo PHP automaticamente)
-- INSERT INTO tbz_notificacoes (titulo, mensagem, tokens_enviados) 
-- VALUES ('Teste', 'Mensagem teste', ARRAY['token1', 'token2']);

-- Buscar notificações de um token específico
-- SELECT * FROM tbz_notificacoes 
-- WHERE 'ExponentPushToken[xxxxx]' = ANY(tokens_enviados) 
-- AND data_envio >= NOW() - INTERVAL '7 days';

-- Marcar como lido para um token específico
-- UPDATE tbz_notificacoes 
-- SET tokens_lidos = array_append(tokens_lidos, 'ExponentPushToken[xxxxx]')
-- WHERE id = 123 AND NOT ('ExponentPushToken[xxxxx]' = ANY(tokens_lidos));

-- =====================================================
-- MIGRAÇÃO DE DADOS (se necessário)
-- =====================================================

-- Se você tem dados na tabela antiga tbz_notificacao_enviada, pode migrar assim:
-- 
-- INSERT INTO tbz_notificacoes (titulo, mensagem, codass, codent, cartao, dados_json, data_envio, tokens_enviados)
-- SELECT 
--     titulo,
--     mensagem, 
--     codass,
--     codent,
--     cartao,
--     dados_json,
--     data_criacao,
--     ARRAY[token] -- Converter token individual em array
-- FROM tbz_notificacao_enviada 
-- WHERE data_criacao >= NOW() - INTERVAL '30 days' -- Apenas últimos 30 dias
-- GROUP BY titulo, mensagem, codass, codent, cartao, dados_json, data_criacao;

-- =====================================================
-- LIMPEZA AUTOMÁTICA (OPCIONAL)
-- =====================================================

-- Função para limpar notificações antigas (executar mensalmente)
-- DELETE FROM tbz_notificacoes WHERE data_envio < NOW() - INTERVAL '90 days';

-- Função para limpar tokens inativos (executar semanalmente)  
-- DELETE FROM tbz_push_tokens WHERE ativo = false AND data_atualizacao < NOW() - INTERVAL '30 days';

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se as tabelas foram criadas
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE tablename IN ('tbz_push_tokens', 'tbz_notificacoes');

-- Verificar índices criados
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('tbz_push_tokens', 'tbz_notificacoes')
ORDER BY tablename, indexname;

-- Mostrar estrutura das tabelas
\d tbz_push_tokens;
\d tbz_notificacoes;

-- =====================================================
-- RESULTADO ESPERADO
-- =====================================================

-- ✅ 2 tabelas criadas (vs 3+ anteriores)
-- ✅ Índices otimizados para performance
-- ✅ Arrays PostgreSQL para controle eficiente
-- ✅ Uma notificação = um registro (não um por token)
-- ✅ Sistema escalável e performático

PRINT 'Tabelas otimizadas criadas com sucesso!';
PRINT 'Sistema pronto para receber notificações push eficientemente!';
