// Configuração global para sistema biométrico
import { robustStorage } from './robustStorage';

// Chaves para storage
const BIOMETRIC_CONFIG_KEY = 'biometric_global_config';
const BIOMETRIC_USER_CHOICE_KEY = 'biometric_user_choice';
const BIOMETRIC_FIRST_SETUP_KEY = 'biometric_first_setup_done';

// Interface para configuração biométrica
interface BiometricConfig {
  isEnabled: boolean;
  userChoice: boolean;
  firstSetupDone: boolean;
  autoLoginEnabled: boolean;
  requirePasswordFirst: boolean; // Nova propriedade: exigir senha na primeira vez
  lastConfigured: number;
}

// Configuração padrão
const defaultConfig: BiometricConfig = {
  isEnabled: false,
  userChoice: false,
  firstSetupDone: false,
  autoLoginEnabled: false,
  requirePasswordFirst: true, // Por padrão, exigir senha na primeira vez
  lastConfigured: 0,
};

class BiometricConfigManager {
  private config: BiometricConfig = { ...defaultConfig };
  private isInitialized = false;

  /**
   * Inicializar configuração biométrica
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔐 Inicializando configuração biométrica...');
      
      // Carregar configuração salva
      const savedConfig = await robustStorage.getItem(BIOMETRIC_CONFIG_KEY);
      
      if (savedConfig) {
        this.config = { ...defaultConfig, ...JSON.parse(savedConfig) };
        console.log('📱 Configuração biométrica carregada:', this.config);
      } else {
        console.log('📱 Usando configuração biométrica padrão');
        await this.saveConfig();
      }
      
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Erro ao inicializar configuração biométrica:', error);
      this.config = { ...defaultConfig };
      this.isInitialized = true;
    }
  }

  /**
   * Salvar configuração
   */
  private async saveConfig(): Promise<void> {
    try {
      await robustStorage.setItem(BIOMETRIC_CONFIG_KEY, JSON.stringify(this.config));
      console.log('💾 Configuração biométrica salva');
    } catch (error) {
      console.error('❌ Erro ao salvar configuração biométrica:', error);
    }
  }

  /**
   * Verificar se usuário já fez a primeira configuração
   */
  async hasUserMadeFirstChoice(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    return this.config.firstSetupDone;
  }

  /**
   * Obter escolha do usuário sobre biometria
   */
  async getUserChoice(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    return this.config.userChoice;
  }

  /**
   * Verificar se biometria está habilitada
   */
  async isBiometricEnabled(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    return this.config.isEnabled;
  }

  /**
   * Verificar se auto-login está habilitado
   */
  async isAutoLoginEnabled(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    return this.config.autoLoginEnabled && this.config.isEnabled && this.config.userChoice;
  }

  /**
   * Definir escolha do usuário
   */
  async setUserChoice(choice: boolean, silent: boolean = false): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    this.config.userChoice = choice;
    this.config.firstSetupDone = true;
    this.config.lastConfigured = Date.now();
    
    // Se usuário desabilitou, desabilitar tudo
    if (!choice) {
      this.config.isEnabled = false;
      this.config.autoLoginEnabled = false;
    }
    
    await this.saveConfig();
    
    if (!silent) {
      console.log('🔐 Escolha do usuário atualizada:', choice);
    }
  }

  /**
   * Habilitar biometria (após configuração bem-sucedida)
   */
  async enableBiometric(enableAutoLogin: boolean = true): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    this.config.isEnabled = true;
    this.config.autoLoginEnabled = enableAutoLogin;
    this.config.lastConfigured = Date.now();
    
    await this.saveConfig();
    console.log('✅ Biometria habilitada com auto-login:', enableAutoLogin);
  }

  /**
   * Desabilitar biometria
   */
  async disableBiometric(): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    this.config.isEnabled = false;
    this.config.autoLoginEnabled = false;
    this.config.requirePasswordFirst = true; // Voltar a exigir senha
    this.config.lastConfigured = Date.now();

    await this.saveConfig();
    console.log('🔐 Biometria desabilitada');
  }

  /**
   * Verificar se deve exigir senha antes da biometria
   */
  async shouldRequirePasswordFirst(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    return this.config.requirePasswordFirst;
  }

  /**
   * Marcar que usuário já fez login com senha (liberar biometria)
   */
  async markPasswordLoginDone(): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    this.config.requirePasswordFirst = false;
    await this.saveConfig();
    console.log('✅ Login com senha realizado - biometria liberada para próximas vezes');
  }

  /**
   * Resetar exigência de senha (forçar login com senha na próxima vez)
   */
  async resetPasswordRequirement(): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    this.config.requirePasswordFirst = true;
    await this.saveConfig();
    console.log('🔐 Exigência de senha resetada');
  }

  /**
   * Resetar configuração (para testes ou reset completo)
   */
  async resetConfig(): Promise<void> {
    this.config = { ...defaultConfig };
    await this.saveConfig();
    console.log('🔄 Configuração biométrica resetada');
  }

  /**
   * Obter configuração completa
   */
  async getConfig(): Promise<BiometricConfig> {
    if (!this.isInitialized) await this.initialize();
    return { ...this.config };
  }

  /**
   * Verificar se deve mostrar opção de biometria
   */
  async shouldShowBiometricOption(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    
    // Sempre mostrar se usuário já fez escolha ou se é primeira vez
    return true;
  }

  /**
   * Verificar se deve fazer auto-login na inicialização
   */
  async shouldAutoLogin(): Promise<boolean> {
    if (!this.isInitialized) await this.initialize();
    
    return this.config.firstSetupDone && 
           this.config.userChoice && 
           this.config.isEnabled && 
           this.config.autoLoginEnabled;
  }

  /**
   * Marcar que primeira configuração foi feita
   */
  async markFirstSetupDone(): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    this.config.firstSetupDone = true;
    await this.saveConfig();
  }
}

// Instância singleton
const biometricConfig = new BiometricConfigManager();

export default biometricConfig;

// Funções helper para uso mais fácil
export const initializeBiometricConfig = () => biometricConfig.initialize();
export const getBiometricUserChoice = () => biometricConfig.getUserChoice();
export const setBiometricUserChoice = (choice: boolean, silent?: boolean) =>
  biometricConfig.setUserChoice(choice, silent);
export const isBiometricConfigEnabled = () => biometricConfig.isBiometricEnabled();
export const shouldAutoLoginWithBiometric = () => biometricConfig.shouldAutoLogin();
export const enableBiometricConfig = (autoLogin?: boolean) =>
  biometricConfig.enableBiometric(autoLogin);
export const disableBiometricConfig = () => biometricConfig.disableBiometric();
export const hasUserMadeFirstBiometricChoice = () => biometricConfig.hasUserMadeFirstChoice();
export const getBiometricConfig = () => biometricConfig.getConfig();

// Novas funções para controle de senha obrigatória
export const shouldRequirePasswordFirst = () => biometricConfig.shouldRequirePasswordFirst();
export const markPasswordLoginDone = () => biometricConfig.markPasswordLoginDone();
export const resetPasswordRequirement = () => biometricConfig.resetPasswordRequirement();
