// Teste rápido para verificar obtenção de token Expo
// Execute: node test_expo_token.js

console.log('🧪 Teste de Token Expo');
console.log('=====================');

// Simular diferentes cenários de token
const scenarios = [
  {
    name: 'Token Real Expo',
    token: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx]',
    isReal: true
  },
  {
    name: 'Token Simulado',
    token: 'tecbiz_dev_1753100298433_asujgp72g',
    isReal: false
  },
  {
    name: 'Token Vazio',
    token: '',
    isReal: false
  }
];

scenarios.forEach((scenario, index) => {
  console.log(`\n${index + 1}. ${scenario.name}:`);
  console.log(`   Token: ${scenario.token}`);
  console.log(`   Length: ${scenario.token.length}`);
  console.log(`   Is Real: ${scenario.token.startsWith('ExponentPushToken[') && scenario.token.length > 50}`);
  console.log(`   Is Simulated: ${scenario.token.startsWith('tecbiz_dev_')}`);
});

console.log('\n🔍 Análise do Erro:');
console.log('- Erro: "projectId": Invalid uuid');
console.log('- Causa: Firebase projectId não é UUID válido para Expo');
console.log('- Solução: Remover projectId ou usar EAS projectId');
console.log('- EAS Project ID: 78c39496-1095-4079-8e21-a92c90832c74');

console.log('\n✅ Correção Aplicada:');
console.log('- Removido projectId do getExpoPushTokenAsync()');
console.log('- Expo usará configuração padrão do app.json');
console.log('- Token deve ser obtido sem erro de UUID');

console.log('\n📱 Próximos Passos:');
console.log('1. Teste no Expo Go (token simulado esperado)');
console.log('2. Gere APK release para token real');
console.log('3. Verifique logs após correção');
console.log('4. Token real deve começar com "ExponentPushToken["');
