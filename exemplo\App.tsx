import React, { useEffect } from 'react';
import AppNavigator from './src/navigation/AppNavigator';
import { AppProvider } from './src/context/AppContext';
// Usando Firebase React Native OFICIAL para push notifications REAIS
import firebaseRealService from './src/services/firebaseRealService';

export default function App() {
  useEffect(() => {
    // Inicializar Firebase React Native OFICIAL para push notifications REAIS
    const initializePushNotifications = async () => {
      try {
        console.log('🔥 Inicializando Firebase React Native OFICIAL...');
        const token = await firebaseRealService.initialize();
        if (token) {
          console.log('✅ Firebase React Native inicializado com SUCESSO!');
          console.log('🎯 Token FCM REAL:', token.substring(0, 30) + '...');
          console.log('📊 Info do serviço:', firebaseRealService.getServiceInfo());
          console.log('🚀 PUSH NOTIFICATIONS REAIS FUNCIONANDO!');
        } else {
          console.log('⚠️ Firebase React Native não pôde obter token');
        }
      } catch (error) {
        console.error('❌ Erro ao inicializar Firebase React Native:', error);
      }
    };

    initializePushNotifications();
  }, []);

  return (
    <AppProvider>
      <AppNavigator />
    </AppProvider>
  );
}
