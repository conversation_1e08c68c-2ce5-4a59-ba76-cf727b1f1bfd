// Configurações específicas para produção
// Garantir que tokens FCM reais sejam gerados no build de produção

import Constants from 'expo-constants';
import * as Device from 'expo-device';

export const PRODUCTION_CONFIG = {
  // Detectar se está em produção
  isProduction: Constants.executionEnvironment === 'standalone' || Constants.executionEnvironment === 'bare',
  
  // Detectar se é dispositivo físico
  isPhysicalDevice: Device.isDevice,
  
  // Configurações específicas para FCM
  fcm: {
    // Forçar uso de tokens reais em produção
    forceRealTokens: true,
    
    // Número máximo de tentativas para obter token
    maxRetries: 15,
    
    // Delay entre tentativas (em ms)
    retryDelay: 2000,
    
    // Timeout para cada tentativa (em ms)
    requestTimeout: 10000,
  },
  
  // Configurações de debug
  debug: {
    // Mostrar logs detalhados apenas em desenvolvimento
    enableDetailedLogs: !Constants.executionEnvironment || Constants.executionEnvironment === 'development',
    
    // Mostrar alertas de token apenas em desenvolvimento
    showTokenAlerts: !Constants.executionEnvironment || Constants.executionEnvironment === 'development',
  },
  
  // Configurações de fallback
  fallback: {
    // Permitir fallback apenas em desenvolvimento
    allowFallback: !Constants.executionEnvironment || Constants.executionEnvironment === 'development',
    
    // Prefixo para tokens de fallback
    tokenPrefix: 'tecbiz_prod_fallback',
  }
};

/**
 * Verificar se deve usar configurações de produção
 */
export const shouldUseProductionConfig = (): boolean => {
  return PRODUCTION_CONFIG.isProduction && PRODUCTION_CONFIG.isPhysicalDevice;
};

/**
 * Obter configurações baseadas no ambiente
 */
export const getEnvironmentConfig = () => {
  const isProduction = shouldUseProductionConfig();
  
  return {
    environment: isProduction ? 'production' : 'development',
    executionEnv: Constants.executionEnvironment,
    isDevice: Device.isDevice,
    shouldForceRealTokens: isProduction,
    allowFallback: !isProduction,
    enableLogs: !isProduction || __DEV__,
    maxRetries: isProduction ? 15 : 10,
    retryDelay: isProduction ? 2000 : 1000,
  };
};

/**
 * Log condicional baseado no ambiente
 */
export const productionLog = (message: string, ...args: any[]) => {
  if (PRODUCTION_CONFIG.debug.enableDetailedLogs || __DEV__) {
    console.log(message, ...args);
  }
};

/**
 * Alert condicional baseado no ambiente
 */
export const productionAlert = (title: string, message: string) => {
  if (PRODUCTION_CONFIG.debug.showTokenAlerts || __DEV__) {
    // Importar Alert dinamicamente para evitar problemas
    import('react-native').then(({ Alert }) => {
      Alert.alert(title, message);
    });
  }
};

/**
 * Validar se token é real
 */
export const isRealToken = (token: string | null): boolean => {
  if (!token) return false;

  // Token real do Expo sempre começa com ExponentPushToken[
  // OU pode ser um token FCM válido (mais de 140 caracteres)
  return (token.startsWith('ExponentPushToken[') && token.length > 50) ||
         (token.length > 140 && !token.includes('simulator') && !token.includes('development'));
};

/**
 * Validar se deve aceitar token baseado no ambiente
 */
export const shouldAcceptToken = (token: string | null): boolean => {
  if (!token) return false;
  
  const config = getEnvironmentConfig();
  
  // Em produção, só aceitar tokens reais
  if (config.shouldForceRealTokens) {
    return isRealToken(token);
  }
  
  // Em desenvolvimento, aceitar qualquer token válido
  return token.length > 20;
};

export default PRODUCTION_CONFIG;
