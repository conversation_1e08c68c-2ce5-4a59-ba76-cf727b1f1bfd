<?php

// ⚠️ ATUALIZADO PARA EXPO PUSH NOTIFICATIONS
// FCM Legacy foi descontinuado - agora usa Expo Push API
define('_HOST_EXPO_PUSH_', 'https://exp.host/--/api/v2/push/send');
define('_API_ACCESS_KEY_', 'AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk'); // Mantido para compatibilidade


class sendAndroidPushNotification {
    private $registrationIds = array();
    private $titulo   = null;
    private $mensagem = null;
    private $dados    = array(); // Dados adicionais
    public $result    = null;
    public $success   = false;
    public $errors    = array();

    public function __construct($titulo, $mensagem, $registrationIds, $dados = array()) {
        $this->titulo   = $this->normalizaTexto($titulo);
        $this->mensagem = $this->normalizaTexto($mensagem);
        $this->registrationIds = is_array($registrationIds) ? $registrationIds : array($registrationIds);
        $this->dados = $dados;
        
        // Log para debug
        error_log("Push Notification - Título: " . $this->titulo);
        error_log("Push Notification - Mensagem: " . $this->mensagem);
        error_log("Push Notification - Tokens: " . count($this->registrationIds));
    }

    public function send() {
        try {
            // Validar tokens
            if (empty($this->registrationIds)) {
                throw new Exception("Nenhum token de registro fornecido");
            }

            // Verificar se são tokens Expo válidos
            $expoTokens = array();
            $fcmTokens = array();

            foreach ($this->registrationIds as $token) {
                if (strpos($token, 'ExponentPushToken[') === 0) {
                    $expoTokens[] = $token;
                } else {
                    $fcmTokens[] = $token;
                }
            }

            // Se tem tokens Expo, usar API do Expo
            if (!empty($expoTokens)) {
                return $this->sendExpoNotifications($expoTokens);
            }

            // Se tem tokens FCM, usar API FCM (legacy - pode não funcionar)
            if (!empty($fcmTokens)) {
                return $this->sendFCMNotifications($fcmTokens);
            }

            throw new Exception("Nenhum token válido encontrado");

        } catch (Exception $e) {
            $this->success = false;
            $this->errors[] = $e->getMessage();
            error_log("Erro ao enviar push notification: " . $e->getMessage());
        }

        // Se enviou com sucesso, salvar no banco para sincronização
        if ($this->success) {
            $this->saveNotificationToDatabase();
        }

        return $this->success;
    }

    /**
     * Salvar notificação OTIMIZADA no banco PostgreSQL
     * UMA notificação para TODOS os tokens (não uma por token)
     */
    private function saveNotificationToDatabase() {
        try {
            // Conectar ao banco PostgreSQL
            $pdo = new PDO("pgsql:host=localhost;dbname=tecbiz", "postgres", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Inserir UMA ÚNICA notificação com array de tokens
            $insertSql = "INSERT INTO tbz_notificacoes
                         (titulo, mensagem, codass, codent, cartao, dados_json, tokens_enviados, data_envio)
                         VALUES (:titulo, :mensagem, :codass, :codent, :cartao, :dados_json, :tokens_enviados, NOW())";
            $stmt = $pdo->prepare($insertSql);

            $dataJson = json_encode($this->dados);

            // Extrair codass e codent dos dados se disponível
            $codass = isset($this->dados['codass']) ? $this->dados['codass'] : null;
            $codent = isset($this->dados['codent']) ? $this->dados['codent'] : null;
            $cartao = isset($this->dados['cartao']) ? $this->dados['cartao'] : null;

            // Converter tokens para array PostgreSQL
            $tokensArray = '{' . implode(',', array_map(function($token) {
                return '"' . str_replace('"', '""', $token) . '"';
            }, $this->registrationIds)) . '}';

            $stmt->execute([
                ':titulo' => $this->titulo,
                ':mensagem' => $this->mensagem,
                ':codass' => $codass,
                ':codent' => $codent,
                ':cartao' => $cartao,
                ':dados_json' => $dataJson,
                ':tokens_enviados' => $tokensArray
            ]);

            error_log("💾 Notificação OTIMIZADA salva no banco para " . count($this->registrationIds) . " tokens");

        } catch (Exception $e) {
            error_log("❌ Erro ao salvar notificação no banco: " . $e->getMessage());
        }
    }

    private function sendExpoNotifications($tokens) {
        try {
            // Preparar mensagens para Expo Push API
            $messages = array();

            foreach ($tokens as $token) {
                $messages[] = array(
                    "to" => $token,
                    "title" => $this->titulo,
                    "body" => $this->mensagem,
                    "data" => array_merge(array(
                        "timestamp" => time(),
                        "app" => "tecbiz"
                    ), $this->dados),
                    "sound" => "default",
                    "badge" => 1
                );
            }

            // Log das mensagens para debug
            error_log("Expo Push Messages: " . json_encode($messages, JSON_PRETTY_PRINT));

            // Configurar cURL para Expo Push API
            $headers = array(
                'Accept: application/json',
                'Accept-encoding: gzip, deflate',
                'Content-Type: application/json'
            );

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, _HOST_EXPO_PUSH_);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messages));
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            // Executar requisição
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_error($ch)) {
                throw new Exception("Erro cURL: " . curl_error($ch));
            }
            
            curl_close($ch);

            // Processar resposta do Expo
            $this->result = json_decode($response, true);

            // Log da resposta
            error_log("Expo Push Response: " . $response);
            error_log("HTTP Code: " . $httpCode);

            // Verificar sucesso (Expo retorna {"data": [{"status": "ok"}]})
            if ($httpCode == 200 && isset($this->result['data']) && is_array($this->result['data'])) {
                $successCount = 0;
                $errorCount = 0;

                foreach ($this->result['data'] as $index => $result) {
                    if (isset($result['status']) && $result['status'] === 'ok') {
                        $successCount++;
                        error_log("✅ Token " . $index . " enviado com sucesso. ID: " . ($result['id'] ?? 'N/A'));
                    } else {
                        $errorCount++;
                        if (isset($result['message'])) {
                            $this->errors[] = "Token " . $index . ": " . $result['message'];
                            error_log("❌ Erro no token " . $index . ": " . $result['message']);
                        }
                    }
                }

                if ($successCount > 0) {
                    $this->success = true;
                    error_log("🎉 Expo Push enviada com sucesso para " . $successCount . " dispositivos");
                } else {
                    $this->success = false;
                    throw new Exception("Nenhuma notificação foi enviada com sucesso");
                }
            } else {
                $this->success = false;
                $errorMsg = "Falha no envio da notificação Expo";

                if (isset($this->result['error'])) {
                    $this->errors[] = $this->result['error'];
                    error_log("Erro geral Expo: " . $this->result['error']);
                }

                throw new Exception($errorMsg);
            }

            return true;
        } catch (Exception $e) {
            $this->success = false;
            $this->errors[] = $e->getMessage();
            error_log("Erro ao enviar Expo push: " . $e->getMessage());
            return false;
        }
    }

    // Método para FCM Legacy (mantido para compatibilidade, mas pode não funcionar)
    private function sendFCMNotifications($tokens) {
        error_log("⚠️ AVISO: Usando FCM Legacy - pode não funcionar (API descontinuada)");

        // Implementação FCM legacy simplificada
        $this->success = false;
        $this->errors[] = "FCM Legacy não suportado - use tokens Expo";
        return false;
    }

    // Função melhorada para normalizar texto
    private function normalizaTexto($str) {
        if (empty($str)) return "";
        
        // Converter para UTF-8 se necessário
        if (!mb_check_encoding($str, 'UTF-8')) {
            $str = utf8_encode($str);
        }
        
        // Normalizar quebras de linha
        $str = str_replace(array("\r\n", "\r"), "\n", $str);
        $str = str_replace("\n", "\\n", $str);
        
        // Escapar aspas
        $str = str_replace('"', '\\"', $str);
        
        // Limitar tamanho (FCM tem limites)
        if (strlen($str) > 4000) {
            $str = substr($str, 0, 3997) . "...";
        }
        
        return $str;
    }

    // Função para obter resultado detalhado
    public function getResult() {
        return array(
            'success' => $this->success,
            'errors' => $this->errors,
            'raw_result' => $this->result
        );
    }

    // Função para verificar tokens inválidos
    public function getInvalidTokens() {
        $invalidTokens = array();
        
        if (isset($this->result['results'])) {
            foreach ($this->result['results'] as $index => $result) {
                if (isset($result['error']) && 
                    in_array($result['error'], ['NotRegistered', 'InvalidRegistration'])) {
                    $invalidTokens[] = $this->registrationIds[$index];
                }
            }
        }
        
        return $invalidTokens;
    }
}

// Função helper para uso mais fácil
function enviarPushNotification($titulo, $mensagem, $tokens, $dados = array()) {
    $push = new sendAndroidPushNotification($titulo, $mensagem, $tokens, $dados);
    $sucesso = $push->send();
    
    $resultado = $push->getResult();
    $tokensInvalidos = $push->getInvalidTokens();
    
    // Log do resultado final
    error_log("Push Notification Final - Sucesso: " . ($sucesso ? 'SIM' : 'NÃO'));
    if (!empty($tokensInvalidos)) {
        error_log("Tokens inválidos encontrados: " . implode(', ', $tokensInvalidos));
    }
    
    return array(
        'success' => $sucesso,
        'result' => $resultado,
        'invalid_tokens' => $tokensInvalidos
    );
}

// Exemplo de uso:
/*
$tokens = array('token1', 'token2', 'token3');
$dados = array(
    'screen' => 'Home',
    'action' => 'open_screen',
    'user_id' => '123'
);

$resultado = enviarPushNotification(
    'Título da Notificação',
    'Mensagem da notificação',
    $tokens,
    $dados
);

if ($resultado['success']) {
    echo "Notificação enviada com sucesso!";
} else {
    echo "Erro ao enviar notificação: " . implode(', ', $resultado['result']['errors']);
}
*/

?>
