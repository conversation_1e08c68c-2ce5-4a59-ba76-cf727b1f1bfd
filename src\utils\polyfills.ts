// Polyfills para compatibilidade com Android 8.0
import { Platform } from 'react-native';

/**
 * Verificar se é Android 8.0 ou inferior
 */
const isAndroid8OrLower = (): boolean => {
  if (Platform.OS !== 'android') return false;
  
  try {
    const version = Platform.Version;
    return typeof version === 'number' && version <= 26; // API 26 = Android 8.0
  } catch (error) {
    console.warn('Erro ao verificar versão do Android:', error);
    return false;
  }
};

/**
 * Polyfill para AbortController em Android 8.0
 * O AbortController pode não estar disponível ou ter comportamento inconsistente
 */
class PolyfillAbortController {
  public signal: AbortSignal;
  private _aborted = false;
  private _listeners: Array<() => void> = [];

  constructor() {
    // Criar um AbortSignal customizado
    this.signal = {
      aborted: false,
      addEventListener: (type: string, listener: () => void) => {
        if (type === 'abort') {
          this._listeners.push(listener);
        }
      },
      removeEventListener: (type: string, listener: () => void) => {
        if (type === 'abort') {
          const index = this._listeners.indexOf(listener);
          if (index > -1) {
            this._listeners.splice(index, 1);
          }
        }
      },
      dispatchEvent: () => true,
      onabort: null,
      reason: undefined,
      throwIfAborted: () => {
        if (this._aborted) {
          throw new Error('AbortError');
        }
      }
    } as AbortSignal;
  }

  abort() {
    if (this._aborted) return;
    
    this._aborted = true;
    (this.signal as any).aborted = true;
    
    // Notificar todos os listeners
    this._listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.warn('Erro ao executar listener de abort:', error);
      }
    });
    
    // Executar onabort se definido
    if (this.signal.onabort) {
      try {
        this.signal.onabort(new Event('abort'));
      } catch (error) {
        console.warn('Erro ao executar onabort:', error);
      }
    }
  }
}

/**
 * Polyfill para AbortSignal.timeout em Android 8.0
 */
const createAbortSignalTimeout = (timeout: number): AbortSignal => {
  const controller = new PolyfillAbortController();
  
  setTimeout(() => {
    controller.abort();
  }, timeout);
  
  return controller.signal;
};

/**
 * Aplicar polyfills necessários para Android 8.0
 * Versão simplificada para evitar conflitos
 */
export const applyPolyfills = () => {
  if (!isAndroid8OrLower()) {
    return; // Silencioso para versões superiores
  }

  console.log('🔧 Aplicando polyfills para Android 8.0...');

  // Apenas garantir que AbortController funciona
  try {
    const testController = new AbortController();
    testController.abort();
    console.log('✅ AbortController nativo OK');
  } catch (error) {
    console.log('🔄 Aplicando polyfill para AbortController');
    (global as any).AbortController = PolyfillAbortController;
  }

  console.log('✅ Polyfills aplicados');
};

/**
 * Criar AbortController compatível com Android 8.0
 */
export const createCompatibleAbortController = (): AbortController => {
  if (isAndroid8OrLower()) {
    return new PolyfillAbortController() as AbortController;
  }
  
  return new AbortController();
};

/**
 * Criar AbortSignal com timeout compatível com Android 8.0
 */
export const createCompatibleAbortSignalTimeout = (timeout: number): AbortSignal => {
  if (isAndroid8OrLower()) {
    return createAbortSignalTimeout(timeout);
  }
  
  // Usar AbortSignal.timeout nativo se disponível
  if (AbortSignal.timeout) {
    return AbortSignal.timeout(timeout);
  }
  
  // Fallback para AbortController manual
  const controller = new AbortController();
  setTimeout(() => controller.abort(), timeout);
  return controller.signal;
};

export default {
  applyPolyfills,
  createCompatibleAbortController,
  createCompatibleAbortSignalTimeout,
  isAndroid8OrLower,
};
