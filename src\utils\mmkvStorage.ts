// Storage compatível com Expo - Usando AsyncStorage com fallback
// MMKV removido por incompatibilidade com Expo
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage em memória como fallback
let memoryStorage: { [key: string]: any } = {};
let useMemoryFallback = false;

// Interface para compatibilidade com Expo
interface StorageInterface {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
  getAllKeys(): string[];
}

// Wrapper compatível com Expo (AsyncStorage + fallback)
class ExpoCompatibleStorage implements StorageInterface {
  getItem(key: string): string | null {
    try {
      if (useMemoryFallback) {
        return memoryStorage[key] || null;
      }

      // Para compatibilidade síncrona, usar memória se disponível
      if (memoryStorage[key]) {
        return memoryStorage[key];
      }

      // Se não está em memória, tentar AsyncStorage assincronamente
      AsyncStorage.getItem(key).then(value => {
        if (value) {
          memoryStorage[key] = value;
        }
      }).catch(error => {
        console.warn(`⚠️ AsyncStorage falhou para ${key}:`, error);
        useMemoryFallback = true;
      });

      return memoryStorage[key] || null;
    } catch (error) {
      console.error(`❌ Erro ao obter item ${key}:`, error);
      useMemoryFallback = true;
      return memoryStorage[key] || null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      // Salvar imediatamente em memória
      memoryStorage[key] = value;

      if (!useMemoryFallback) {
        // Salvar assincronamente no AsyncStorage
        AsyncStorage.setItem(key, value).catch(error => {
          console.warn(`⚠️ AsyncStorage falhou para ${key}:`, error);
          useMemoryFallback = true;
        });
      }

      console.log(`💾 Item salvo: ${key}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar item ${key}:`, error);
      useMemoryFallback = true;
    }
  }

  removeItem(key: string): void {
    try {
      // Remover da memória
      delete memoryStorage[key];

      if (!useMemoryFallback) {
        // Remover do AsyncStorage
        AsyncStorage.removeItem(key).catch(error => {
          console.warn(`⚠️ AsyncStorage falhou para remover ${key}:`, error);
          useMemoryFallback = true;
        });
      }

      console.log(`🗑️ Item removido: ${key}`);
    } catch (error) {
      console.error(`❌ Erro ao remover item ${key}:`, error);
    }
  }

  clear(): void {
    try {
      // Limpar memória
      memoryStorage = {};

      if (!useMemoryFallback) {
        // Limpar AsyncStorage
        AsyncStorage.clear().catch(error => {
          console.warn('⚠️ AsyncStorage falhou ao limpar:', error);
          useMemoryFallback = true;
        });
      }

      console.log('🧹 Storage limpo');
    } catch (error) {
      console.error('❌ Erro ao limpar storage:', error);
    }
  }

  getAllKeys(): string[] {
    try {
      return Object.keys(memoryStorage);
    } catch (error) {
      console.error('❌ Erro ao obter chaves:', error);
      return [];
    }
  }

  // Métodos específicos para objetos JSON
  getObject<T>(key: string): T | null {
    try {
      const value = this.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`❌ Erro ao obter objeto ${key}:`, error);
      return null;
    }
  }

  setObject(key: string, value: any): void {
    try {
      this.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`❌ Erro ao salvar objeto ${key}:`, error);
    }
  }

  // Métodos específicos para booleanos
  getBoolean(key: string): boolean {
    try {
      const value = this.getItem(key);
      return value === 'true';
    } catch (error) {
      console.error(`❌ Erro ao obter boolean ${key}:`, error);
      return false;
    }
  }

  setBoolean(key: string, value: boolean): void {
    try {
      this.setItem(key, value.toString());
      console.log(`💾 Boolean salvo: ${key} = ${value}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar boolean ${key}:`, error);
    }
  }

  // Métodos específicos para números
  getNumber(key: string): number {
    try {
      const value = this.getItem(key);
      return value ? parseFloat(value) : 0;
    } catch (error) {
      console.error(`❌ Erro ao obter number ${key}:`, error);
      return 0;
    }
  }

  setNumber(key: string, value: number): void {
    try {
      this.setItem(key, value.toString());
      console.log(`💾 Number salvo: ${key} = ${value}`);
    } catch (error) {
      console.error(`❌ Erro ao salvar number ${key}:`, error);
    }
  }

  // Método para carregar dados do AsyncStorage para memória
  async loadFromAsyncStorage(): Promise<void> {
    try {
      if (useMemoryFallback) return;

      const keys = await AsyncStorage.getAllKeys();
      const items = await AsyncStorage.multiGet(keys);

      items.forEach(([key, value]) => {
        if (value !== null) {
          memoryStorage[key] = value;
        }
      });

      console.log(`📱 ${keys.length} itens carregados do AsyncStorage para memória`);
    } catch (error) {
      console.warn('⚠️ Erro ao carregar do AsyncStorage:', error);
      useMemoryFallback = true;
    }
  }

  // Método para verificar se está usando fallback
  isUsingMemoryFallback(): boolean {
    return useMemoryFallback;
  }

  // Método para obter tipo de storage em uso
  getStorageType(): string {
    return useMemoryFallback ? 'Memory (AsyncStorage failed)' : 'AsyncStorage + Memory Cache';
  }
}

// Instância singleton
export const mmkvStorage = new ExpoCompatibleStorage();

// Carregar dados do AsyncStorage na inicialização
mmkvStorage.loadFromAsyncStorage();

// Funções específicas para login
export const saveLoginData = (cardOrEmail: string, password: string, rememberData: boolean) => {
  try {
    if (rememberData) {
      mmkvStorage.setItem('saved_cardOrEmail', cardOrEmail);
      mmkvStorage.setItem('saved_password', password);
      mmkvStorage.setBoolean('saved_rememberData', true);
      console.log('💾 Dados de login salvos no MMKV');
    } else {
      // Se não marcou lembrar, limpar dados salvos
      mmkvStorage.removeItem('saved_cardOrEmail');
      mmkvStorage.removeItem('saved_password');
      mmkvStorage.removeItem('saved_rememberData');
      console.log('🗑️ Dados de login removidos do MMKV');
    }

    // SEMPRE salvar para recarregamento automático (independente de lembrar dados)
    mmkvStorage.setItem('cartao_login', cardOrEmail);
    mmkvStorage.setItem('senha_login', password);
    
  } catch (error) {
    console.error('❌ Erro ao salvar dados de login:', error);
  }
};

export const loadLoginData = () => {
  try {
    const rememberData = mmkvStorage.getBoolean('saved_rememberData');
    
    if (rememberData) {
      return {
        cardOrEmail: mmkvStorage.getItem('saved_cardOrEmail') || '',
        password: mmkvStorage.getItem('saved_password') || '',
        rememberData: true
      };
    }
    
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  } catch (error) {
    console.error('❌ Erro ao carregar dados de login:', error);
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  }
};

export const getReloadCredentials = () => {
  try {
    return {
      cartao: mmkvStorage.getItem('cartao_login'),
      senha: mmkvStorage.getItem('senha_login')
    };
  } catch (error) {
    console.error('❌ Erro ao obter credenciais para reload:', error);
    return { cartao: null, senha: null };
  }
};

export const clearLoginData = () => {
  try {
    mmkvStorage.removeItem('saved_cardOrEmail');
    mmkvStorage.removeItem('saved_password');
    mmkvStorage.removeItem('saved_rememberData');
    mmkvStorage.removeItem('cartao_login');
    mmkvStorage.removeItem('senha_login');
    
    console.log('🧹 Todos os dados de login limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados de login:', error);
  }
};

// Export padrão
export default mmkvStorage;

console.log('💾 Storage compatível com Expo inicializado (AsyncStorage + Memory Cache)');
console.log('✅ MMKV removido - agora 100% compatível com Expo!');
