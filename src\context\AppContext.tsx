import React, { createContext, useContext, useState, ReactNode } from 'react';
import { ApiResponse, MesData, SinteticoData, UsuarioData, atualizarDadosUsuario } from '../services/api';

interface AppContextType {
  userData: ApiResponse | null;
  setUserData: (data: ApiResponse | null) => void;
  getMesesData: () => MesData[];
  getSinteticoData: () => SinteticoData | null;
  getUsuarioData: () => UsuarioData | null;
  autorizacoesAtualizadas: boolean;
  setAutorizacoesAtualizadas: (value: boolean) => void;
  atualizarDadosUsuario: (cartao?: string, senha?: string) => Promise<boolean>;
  dadosAtualizados: boolean;
  setDadosAtualizados: (value: boolean) => void;

}

const AppContext = createContext<AppContextType | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [userData, setUserData] = useState<ApiResponse | null>(null);
  const [autorizacoesAtualizadas, setAutorizacoesAtualizadas] = useState(false);
  const [dadosAtualizados, setDadosAtualizados] = useState(false);


  // Função para extrair dados dos meses
  const getMesesData = (): MesData[] => {
    if (!userData || !Array.isArray(userData)) return [];
    
    return userData.filter(item => 'mes' in item) as MesData[];
  };

  // Função para extrair dados sintéticos
  const getSinteticoData = (): SinteticoData | null => {
    if (!userData || !Array.isArray(userData)) return null;
    
    const sinteticoItem = userData.find(item => 'sintetico' in item) as SinteticoData | undefined;
    return sinteticoItem || null;
  };

  // Função para extrair dados do usuário
  const getUsuarioData = (): UsuarioData | null => {
    if (!userData || !Array.isArray(userData)) return null;

    const usuarioItem = userData.find(item => 'usuario' in item) as UsuarioData | undefined;
    return usuarioItem || null;
  };

  // Função para atualizar dados do usuário
  const atualizarDadosUsuarioFunc = async (cartao?: string, senha?: string): Promise<boolean> => {
    try {
      const usuarioAtual = getUsuarioData();

      // Se não foram fornecidos cartão e senha, tentar obter do storage
      if (!cartao || !senha) {
        try {
          const { getStoredData } = require('../utils/storage');
          const dadosSalvos = await getStoredData();

          if (dadosSalvos && dadosSalvos.cardOrEmail && dadosSalvos.password) {
            cartao = dadosSalvos.cardOrEmail;
            senha = dadosSalvos.password;
            console.log('✅ Usando dados salvos do storage para atualização');
          } else {
            console.log('❌ Cartão e senha são necessários para atualização');
            return false;
          }
        } catch (error) {
          console.log('❌ Erro ao obter dados do storage:', error);
          return false;
        }
      }

      if (!usuarioAtual?.usuario?.carass) {
        console.log('❌ Dados do usuário não encontrados para atualização');
        return false;
      }

      console.log('🔄 Iniciando atualização dos dados do usuário...');
      const response = await atualizarDadosUsuario(cartao, senha);

      if (response.success && response.data) {
        console.log('✅ Dados atualizados com sucesso, atualizando contexto...');
        setUserData(response.data);
        setDadosAtualizados(true);

        // Reset flag após um tempo
        setTimeout(() => setDadosAtualizados(false), 1000);

        return true;
      } else {
        console.log('❌ Falha ao atualizar dados:', response.message);
        return false;
      }
    } catch (error) {
      console.log('❌ Erro ao atualizar dados do usuário:', error);
      return false;
    }
  };



  const value: AppContextType = {
    userData,
    setUserData,
    getMesesData,
    getSinteticoData,
    getUsuarioData,
    autorizacoesAtualizadas,
    setAutorizacoesAtualizadas,
    atualizarDadosUsuario: atualizarDadosUsuarioFunc,
    dadosAtualizados,
    setDadosAtualizados,

  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppContext = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};
