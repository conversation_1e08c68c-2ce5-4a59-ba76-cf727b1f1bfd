import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StatusBar, Platform } from 'react-native';
import Cores from '../constants/Cores';
import { getStatusBarHeight } from '../utils/safeArea';

interface HeaderProps {
  title: string;
  onBackPress?: () => void;
  showBackButton?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  title,
  onBackPress,
  showBackButton = true
}) => {
  // Calcular padding top seguro de forma mais simples
  const safeTopPadding = Platform.OS === 'android' ? (getStatusBarHeight() || 0) : 8;

  return (
    <>
      <StatusBar backgroundColor={Cores.primaria} barStyle="light-content" />
      <View style={[styles.header, { paddingTop: safeTopPadding }]}>
        {showBackButton ? (
          <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.backButton} />
        )}
        
        <Text style={styles.title}>{title}</Text>

        <View style={styles.spacer} />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: Cores.primaria,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  title: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    flex: 1,
  },
  spacer: {
    width: 40,
  },
});

export default Header;
