// Storage Nativo com SecureStore + fallback em memória
import { Alert } from 'react-native';
import * as SecureStore from 'expo-secure-store';

// Storage em memória persistente
let memoryStorage: { [key: string]: any } = {};
let isInitialized = false;

// Interface do Storage Nativo
interface NativeStorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
  getAllKeys(): string[];
  getBoolean(key: string): Promise<boolean>;
  setBoolean(key: string, value: boolean): Promise<void>;
  getNumber(key: string): Promise<number>;
  setNumber(key: string, value: number): Promise<void>;
  getObject<T>(key: string): Promise<T | null>;
  setObject(key: string, value: any): Promise<void>;
}

// Implementação do Storage Nativo
class NativeStorage implements NativeStorageInterface {
  
  constructor() {
    this.initialize();
  }

  private initialize() {
    if (!isInitialized) {
      console.log('💾 Inicializando Storage Nativo JavaScript...');
      
      // Tentar carregar dados salvos anteriormente (se existirem)
      this.loadFromLocalStorage();
      
      // Salvar automaticamente a cada mudança
      this.setupAutoSave();
      
      isInitialized = true;
      console.log('✅ Storage Nativo inicializado com sucesso');
    }
  }

  // Tentar carregar do localStorage do navegador (se disponível)
  private loadFromLocalStorage() {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const saved = window.localStorage.getItem('tecbiz_native_storage');
        if (saved) {
          memoryStorage = JSON.parse(saved);
          console.log('📱 Dados carregados do localStorage');
        }
      }
    } catch (error) {
      console.log('⚠️ localStorage não disponível, usando apenas memória');
    }
  }

  // Salvar automaticamente no localStorage (se disponível)
  private saveToLocalStorage() {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        window.localStorage.setItem('tecbiz_native_storage', JSON.stringify(memoryStorage));
      }
    } catch (error) {
      // Ignorar erros de localStorage
    }
  }

  // Configurar salvamento automático
  private setupAutoSave() {
    // Salvar a cada 5 segundos se houver mudanças
    setInterval(() => {
      this.saveToLocalStorage();
    }, 5000);
  }

  // Métodos principais (agora async)
  async getItem(key: string): Promise<string | null> {
    try {
      // Tentar SecureStore primeiro
      const secureValue = await SecureStore.getItemAsync(key);
      if (secureValue !== null) {
        console.log(`📖 Item lido do SecureStore: ${key}`);
        return secureValue;
      }

      // Fallback para memória
      const value = memoryStorage[key];
      console.log(`📖 Item lido da memória: ${key}`);
      return value || null;
    } catch (error) {
      console.warn(`⚠️ Erro ao ler item ${key}, usando memória:`, error);
      return memoryStorage[key] || null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      // Salvar no SecureStore primeiro
      await SecureStore.setItemAsync(key, value);
      console.log(`💾 Item salvo no SecureStore: ${key}`);

      // Também salvar na memória como backup
      memoryStorage[key] = value;
      this.saveToLocalStorage();
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar no SecureStore, usando memória: ${key}`, error);
      // Fallback para memória
      memoryStorage[key] = value;
      this.saveToLocalStorage();
    }
  }

  removeItem(key: string): void {
    try {
      delete memoryStorage[key];
      this.saveToLocalStorage();
      console.log(`🗑️ Item removido: ${key}`);
    } catch (error) {
      console.warn(`⚠️ Erro ao remover item ${key}:`, error);
    }
  }

  clear(): void {
    try {
      memoryStorage = {};
      this.saveToLocalStorage();
      console.log('🧹 Storage limpo');
    } catch (error) {
      console.warn('⚠️ Erro ao limpar storage:', error);
    }
  }

  getAllKeys(): string[] {
    try {
      return Object.keys(memoryStorage);
    } catch (error) {
      console.warn('⚠️ Erro ao obter chaves:', error);
      return [];
    }
  }

  // Métodos específicos para booleanos
  getBoolean(key: string): boolean {
    try {
      const value = this.getItem(key);
      return value === 'true';
    } catch (error) {
      console.warn(`⚠️ Erro ao obter boolean ${key}:`, error);
      return false;
    }
  }

  setBoolean(key: string, value: boolean): void {
    try {
      this.setItem(key, value.toString());
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar boolean ${key}:`, error);
    }
  }

  // Métodos específicos para números
  getNumber(key: string): number {
    try {
      const value = this.getItem(key);
      return value ? parseFloat(value) : 0;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter number ${key}:`, error);
      return 0;
    }
  }

  setNumber(key: string, value: number): void {
    try {
      this.setItem(key, value.toString());
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar number ${key}:`, error);
    }
  }

  // Métodos específicos para objetos JSON
  getObject<T>(key: string): T | null {
    try {
      const value = this.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn(`⚠️ Erro ao obter objeto ${key}:`, error);
      return null;
    }
  }

  setObject(key: string, value: any): void {
    try {
      this.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`⚠️ Erro ao salvar objeto ${key}:`, error);
    }
  }

  // Métodos de diagnóstico
  getStorageInfo(): string {
    const keys = this.getAllKeys();
    const size = JSON.stringify(memoryStorage).length;
    return `Storage Nativo JavaScript\nItens: ${keys.length}\nTamanho: ${size} bytes`;
  }

  // Método para exportar dados (backup)
  exportData(): string {
    try {
      return JSON.stringify(memoryStorage, null, 2);
    } catch (error) {
      console.error('❌ Erro ao exportar dados:', error);
      return '{}';
    }
  }

  // Método para importar dados (restore)
  importData(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      memoryStorage = parsed;
      this.saveToLocalStorage();
      console.log('✅ Dados importados com sucesso');
      return true;
    } catch (error) {
      console.error('❌ Erro ao importar dados:', error);
      return false;
    }
  }

  // Método para mostrar dados (debug)
  showStorageData(): void {
    const data = this.exportData();
    Alert.alert(
      'Dados do Storage',
      data.length > 500 ? data.substring(0, 500) + '...' : data,
      [{ text: 'OK' }]
    );
  }
}

// Instância singleton
export const nativeStorage = new NativeStorage();

// Funções específicas para login
export const saveLoginData = (cardOrEmail: string, password: string, rememberData: boolean) => {
  try {
    if (rememberData) {
      nativeStorage.setItem('saved_cardOrEmail', cardOrEmail);
      nativeStorage.setItem('saved_password', password);
      nativeStorage.setBoolean('saved_rememberData', true);
      console.log('💾 Dados de login salvos');
    } else {
      // Se não marcou lembrar, limpar dados salvos
      nativeStorage.removeItem('saved_cardOrEmail');
      nativeStorage.removeItem('saved_password');
      nativeStorage.removeItem('saved_rememberData');
      console.log('🗑️ Dados de login removidos');
    }

    // SEMPRE salvar para recarregamento automático (independente de lembrar dados)
    nativeStorage.setItem('cartao_login', cardOrEmail);
    nativeStorage.setItem('senha_login', password);
    
  } catch (error) {
    console.error('❌ Erro ao salvar dados de login:', error);
  }
};

export const loadLoginData = () => {
  try {
    const rememberData = nativeStorage.getBoolean('saved_rememberData');
    
    if (rememberData) {
      const cardOrEmail = nativeStorage.getItem('saved_cardOrEmail');
      const password = nativeStorage.getItem('saved_password');
      
      return {
        cardOrEmail: cardOrEmail || '',
        password: password || '',
        rememberData: true
      };
    }
    
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  } catch (error) {
    console.error('❌ Erro ao carregar dados de login:', error);
    return {
      cardOrEmail: '',
      password: '',
      rememberData: false
    };
  }
};

export const clearLoginData = () => {
  try {
    nativeStorage.removeItem('saved_cardOrEmail');
    nativeStorage.removeItem('saved_password');
    nativeStorage.removeItem('saved_rememberData');
    nativeStorage.removeItem('cartao_login');
    nativeStorage.removeItem('senha_login');
    
    console.log('🧹 Todos os dados de login limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados de login:', error);
  }
};

// Export padrão
export default nativeStorage;

console.log('💾 Storage Nativo JavaScript inicializado - SEM DEPENDÊNCIAS EXTERNAS!');
