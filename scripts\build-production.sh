#!/bin/bash

# Script para build de produção otimizado para FCM
# Garante que tokens reais sejam gerados no APK

echo "🚀 INICIANDO BUILD DE PRODUÇÃO OTIMIZADO"
echo "========================================"

# Verificar se EAS CLI está instalado
if ! command -v eas &> /dev/null; then
    echo "❌ EAS CLI não encontrado. Instalando..."
    npm install -g @expo/eas-cli
fi

# Verificar se está logado no EAS
echo "🔐 Verificando login EAS..."
eas whoami || {
    echo "❌ Não está logado no EAS. Execute: eas login"
    exit 1
}

# Limpar cache
echo "🧹 Limpando cache..."
eas build --platform android --profile preview --clear-cache --non-interactive || {
    echo "❌ Falha na limpeza de cache"
    exit 1
}

# Verificar configurações
echo "🔍 Verificando configurações..."

# Verificar google-services.json
if [ ! -f "google-services.json" ]; then
    echo "❌ google-services.json não encontrado!"
    exit 1
fi

# Verificar package name
PACKAGE_NAME=$(grep -o '"package_name": "[^"]*"' google-services.json | cut -d'"' -f4)
echo "📦 Package name no google-services.json: $PACKAGE_NAME"

# Verificar app.json
APP_PACKAGE=$(grep -o '"package": "[^"]*"' app.json | cut -d'"' -f4)
echo "📦 Package name no app.json: $APP_PACKAGE"

if [ "$PACKAGE_NAME" != "$APP_PACKAGE" ]; then
    echo "⚠️ AVISO: Package names não coincidem!"
    echo "   google-services.json: $PACKAGE_NAME"
    echo "   app.json: $APP_PACKAGE"
fi

# Verificar project ID
PROJECT_ID=$(grep -o '"project_id": "[^"]*"' google-services.json | cut -d'"' -f4)
echo "🆔 Project ID: $PROJECT_ID"

# Build de produção
echo "🏗️ Iniciando build de produção..."
echo "   Profile: preview"
echo "   Platform: android"
echo "   Clear cache: yes"

eas build --platform android --profile preview --clear-cache --non-interactive || {
    echo "❌ Falha no build de produção"
    exit 1
}

echo ""
echo "✅ BUILD DE PRODUÇÃO CONCLUÍDO!"
echo "================================"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Baixe o APK gerado"
echo "2. Instale em um dispositivo físico"
echo "3. Teste a obtenção do token FCM"
echo "4. Verifique se o token é real (ExponentPushToken[...])"
echo "5. Teste o envio de notificações"
echo ""
echo "🔍 COMANDOS DE DEBUG:"
echo "- Verificar logs: adb logcat | grep -i 'firebase\\|expo\\|token'"
echo "- Testar token: Execute o app e vá em Debug Token"
echo ""
echo "🎯 CRITÉRIOS DE SUCESSO:"
echo "- ✅ Token deve começar com 'ExponentPushToken['"
echo "- ✅ Token deve ter mais de 50 caracteres"
echo "- ✅ Notificações devem ser recebidas"
echo "- ✅ Token deve ser enviado no login"
