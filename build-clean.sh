#!/bin/bash

echo "🧹 Limpando projeto para build limpo..."

# Limpar cache do npm
echo "📦 Limpando cache do npm..."
npm cache clean --force

# Limpar node_modules e reinstalar
echo "🗑️ Removendo node_modules..."
rm -rf node_modules
echo "📦 Reinstalando dependências..."
npm install

# <PERSON><PERSON> builds Android
echo "🤖 Limpando builds Android..."
rm -rf android/app/build
rm -rf android/app/.cxx
rm -rf android/build
rm -rf android/.gradle

# Limpar cache do Metro
echo "🚇 Limpando cache do Metro..."
npx expo start --clear

echo "✅ Limpeza concluída!"
echo ""
echo "🚀 Agora execute o build:"
echo "eas build --platform android --profile preview --clear-cache"
