import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
  TextInput,
} from 'react-native';
import Cores from '../constants/Cores';
import SafeHeader from '../components/SafeHeader';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import { bloquearCartao } from '../services/api';
import { NavigationProp } from '../types/navigation';

interface BloquearCartaoScreenProps {
  navigation: NavigationProp;
}

const BloquearCartaoScreen: React.FC<BloquearCartaoScreenProps> = ({ navigation }) => {
  const { getUsuarioData, atualizarDadosUsuario } = useAppContext();


  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack?.());
  const [senha, setSenha] = useState('');
  const [justificativa, setJustificativa] = useState('');
  const [loading, setLoading] = useState(false);
  const [mostrarSenha, setMostrarSenha] = useState(false);

  const usuarioData = getUsuarioData();

  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack?.());

  const obterStatusCartao = () => {
    const sitcar = usuarioData?.usuario?.sitcar;
    if (!sitcar) return { texto: 'Indisponível', cor: Cores.textoMedio };

    switch (sitcar) {
      case 'L':
        return { texto: 'Liberado', cor: Cores.sucesso };
      case 'B':
        return { texto: 'Bloqueado', cor: Cores.erro };
      default:
        return { texto: 'Cancelado', cor: Cores.erro };
    }
  };

  const validarCampos = (): string => {
    let erros = '';

    if (senha.length < 6) {
      erros += 'Senha deve ter pelo menos 6 caracteres!\n';
    }

    if (justificativa.length < 5) {
      erros += 'Informe uma justificativa coerente com a situação!\n';
    }

    return erros;
  };

  const handleBloquearCartao = async () => {
    const erros = validarCampos();
    
    if (erros) {
      Alert.alert('Atenção', erros.trim());
      return;
    }

    if (!usuarioData?.usuario?.carass) {
      Alert.alert('Erro', 'Dados do cartão não encontrados');
      return;
    }

    Alert.alert(
      'Confirmar Bloqueio',
      'Tem certeza que deseja bloquear o cartão? Esta ação só poderá ser desfeita pela sua Entidade!',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Bloquear',
          style: 'destructive',
          onPress: executarBloqueio,
        },
      ]
    );
  };

  const executarBloqueio = async () => {
    try {
      setLoading(true);

      // Primeiro, bloquear o cartão
      const response = await bloquearCartao(
        senha,
        usuarioData!.usuario.carass,
        justificativa
      );

      if (response.success) {
        // Atualizar dados do usuário para refletir o bloqueio
        console.log('🔄 Atualizando dados após bloqueio do cartão...');
        const dadosAtualizados = await atualizarDadosUsuario(
          usuarioData!.usuario.carass,
          senha
        );

        // Triggerar recarregamento na HomeScreen (igual ao Cordova)
        if ((globalThis as any).recarregarHomeScreen) {
          console.log('🔄 Chamando recarregamento da HomeScreen após bloqueio');
          (globalThis as any).recarregarHomeScreen();
        }

        if (dadosAtualizados) {
          Alert.alert(
            'Sucesso',
            response.message + '\n\nPara desbloquear, entre em contato com sua Entidade.',
            [
              {
                text: 'OK',
                onPress: () => navigation.navigate('Home'),
              },
            ]
          );
        } else {
          Alert.alert(
            'Sucesso',
            response.message + '\n\nCartão bloqueado! Os dados serão atualizados automaticamente.',
            [
              {
                text: 'OK',
                onPress: () => navigation.navigate('Home'),
              },
            ]
          );
        }
      } else {
        Alert.alert('Erro', response.message);
      }
    } catch (error) {
      console.log('❌ Erro ao bloquear cartão:', error);
      Alert.alert('Erro', 'Falha ao bloquear cartão. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  if (!usuarioData) {
    return (
      <View style={styles.container}>
        <SafeHeader
          title="Bloquear Cartão"
          onBackPress={() => navigation.goBack?.()}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Dados do usuário não encontrados</Text>
        </View>
      </View>
    );
  }

  const statusCartaoInfo = obterStatusCartao();
  const cartaoJaBloqueado = usuarioData?.usuario?.sitcar !== 'L';

  return (
    <View style={styles.container}>
      <SafeHeader
        title="Bloquear Cartão"
        onBackPress={() => navigation.goBack?.()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Status do Cartão */}
        <View style={styles.statusContainer}>
          <Text style={styles.statusLabel}>Cartão:</Text>
          <Text style={[styles.statusValue, { color: statusCartaoInfo.cor }]}>
            {statusCartaoInfo.texto}
          </Text>
        </View>

        {cartaoJaBloqueado ? (
          /* Cartão já bloqueado/cancelado */
          <View style={styles.bloqueadoContainer}>
            <Text style={styles.bloqueadoIcon}>🚫</Text>
            <Text style={styles.bloqueadoTitulo}>Cartão Indisponível</Text>
            <Text style={styles.bloqueadoDescricao}>
              {usuarioData?.usuario?.sitcar === 'B'
                ? 'Seu cartão já está bloqueado. Entre em contato com sua Entidade para desbloqueá-lo.'
                : 'Seu cartão foi cancelado. Entre em contato com sua Entidade para mais informações.'
              }
            </Text>
          </View>
        ) : (
          /* Formulário de bloqueio */
          <View style={styles.formContainer}>
            {/* Campo Senha */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Senha</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={senha}
                  onChangeText={setSenha}
                  placeholder="Digite sua senha"
                  placeholderTextColor={Cores.textoMedio}
                  secureTextEntry={!mostrarSenha}
                  keyboardType="numeric"
                  maxLength={6}
                />
                <TouchableOpacity 
                  style={styles.eyeButton}
                  onPress={() => setMostrarSenha(!mostrarSenha)}
                >
                  <Text style={styles.eyeIcon}>
                    {mostrarSenha ? '🚫' : '👁'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Campo Justificativa */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Justificativa</Text>
              <TextInput
                style={styles.textAreaInput}
                value={justificativa}
                onChangeText={setJustificativa}
                placeholder="Informe o motivo do bloqueio (mínimo 5 caracteres)"
                placeholderTextColor={Cores.textoMedio}
                multiline={true}
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Botão Bloquear */}
            <TouchableOpacity
              style={[styles.bloquearButton, loading && styles.buttonDisabled]}
              onPress={handleBloquearCartao}
              disabled={loading}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="small" color="white" />
                  <Text style={styles.loadingText}>Processando...</Text>
                </View>
              ) : (
                <Text style={styles.bloquearButtonText}>Bloquear Cartão</Text>
              )}
            </TouchableOpacity>

            {/* Aviso */}
            <View style={styles.avisoContainer}>
              <Text style={styles.avisoIcon}>⚠️</Text>
              <Text style={styles.avisoTexto}>
                Esta ação só poderá ser desfeita pela sua Entidade!
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginRight: 10,
  },
  statusValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  bloqueadoContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 30,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  bloqueadoIcon: {
    fontSize: 48,
    marginBottom: 15,
  },
  bloqueadoTitulo: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 10,
    textAlign: 'center',
  },
  bloqueadoDescricao: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    lineHeight: 24,
  },
  formContainer: {
    gap: 20,
  },
  inputContainer: {
    gap: 8,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.primaria,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Cores.bordaClara,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: Cores.textoEscuro,
  },
  eyeButton: {
    padding: 12,
    marginRight: 4,
  },
  eyeIcon: {
    fontSize: 20,
  },
  textAreaInput: {
    backgroundColor: 'white',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Cores.bordaClara,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: Cores.textoEscuro,
    minHeight: 100,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  bloquearButton: {
    backgroundColor: Cores.erro,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    marginTop: 10,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  bloquearButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  avisoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff3cd',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  avisoIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  avisoTexto: {
    flex: 1,
    fontSize: 14,
    color: '#856404',
    fontWeight: '500',
  },
});

export default BloquearCartaoScreen;
