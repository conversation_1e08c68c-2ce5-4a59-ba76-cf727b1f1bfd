-- ============================================
-- TABELA PARA SINCRONIZAÇÃO DE NOTIFICAÇÕES - POSTGRESQL
-- ============================================

-- Criar tabela tbz_notificacao_enviada
CREATE TABLE IF NOT EXISTS tbz_notificacao_enviada (
    -- Chave primária
    id SERIAL PRIMARY KEY,

    -- Dados da notificação
    titulo VARCHAR(255) NOT NULL,
    mensagem TEXT NOT NULL,

    -- Dados do dispositivo
    token VARCHAR(255) NOT NULL,

    -- Dados extras
    dados_json TEXT,

    -- Controle de tempo
    timestamp INTEGER NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_entrega TIMESTAMP NULL,

    -- <PERSON><PERSON> de leitura (novo campo)
    lida BOOLEAN DEFAULT FALSE,

    -- Dados do usuário
    codass INTEGER NULL,
    codent INTEGER NULL,
    cartao VARCHAR(20) NULL
);

-- Índices recomendados para performance
CREATE INDEX IF NOT EXISTS idx_tbz_token ON tbz_notificacao_enviada (token);
CREATE INDEX IF NOT EXISTS idx_tbz_timestamp ON tbz_notificacao_enviada (timestamp);
CREATE INDEX IF NOT EXISTS idx_tbz_token_timestamp ON tbz_notificacao_enviada (token, timestamp);
CREATE INDEX IF NOT EXISTS idx_tbz_lida ON tbz_notificacao_enviada (lida);
CREATE INDEX IF NOT EXISTS idx_tbz_codass ON tbz_notificacao_enviada (codass);
CREATE INDEX IF NOT EXISTS idx_tbz_codent ON tbz_notificacao_enviada (codent);
CREATE INDEX IF NOT EXISTS idx_tbz_data_criacao ON tbz_notificacao_enviada (data_criacao);

-- ============================================
-- DADOS DE EXEMPLO (OPCIONAL)
-- ============================================

-- Inserir algumas notificações de exemplo para teste
INSERT INTO tbz_notificacao_enviada
(titulo, mensagem, token, dados_json, timestamp, lida, codass, codent, cartao)
VALUES
(
    'Bem-vindo ao TecBiz!',
    'Sua conta foi configurada com sucesso.',
    'ExponentPushToken[EXEMPLO123]',
    '{"screen": "Home", "action": "welcome"}',
    EXTRACT(EPOCH FROM NOW())::INTEGER,
    FALSE,
    1,
    1,
    '1234567890'
),
(
    'Nova funcionalidade',
    'Confira as novidades do app!',
    'ExponentPushToken[EXEMPLO123]',
    '{"screen": "News", "action": "new_feature"}',
    EXTRACT(EPOCH FROM NOW())::INTEGER - 3600,
    TRUE,
    1,
    1,
    '1234567890'
);

-- ============================================
-- CONSULTAS ÚTEIS
-- ============================================

-- Ver todas as notificações
-- SELECT * FROM tbz_notificacao_enviada ORDER BY id DESC;

-- Ver notificações não entregues
-- SELECT * FROM tbz_notificacao_enviada WHERE entregue = 0 ORDER BY data_criacao DESC;

-- Ver notificações de um token específico
-- SELECT * FROM tbz_notificacao_enviada WHERE token = 'SEU_TOKEN_AQUI' ORDER BY data_criacao DESC;

-- Marcar notificação como entregue
-- UPDATE tbz_notificacao_enviada SET entregue = 1, data_entrega = NOW() WHERE id = 1;

-- Limpar notificações antigas (mais de 30 dias)
-- DELETE FROM tbz_notificacao_enviada WHERE data_criacao < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Estatísticas
-- SELECT 
--     COUNT(*) as total,
--     SUM(entregue) as entregues,
--     COUNT(*) - SUM(entregue) as pendentes
-- FROM tbz_notificacao_enviada;

-- ============================================
-- ESTRUTURA DA TABELA
-- ============================================

/*
CAMPOS PRINCIPAIS:
- id: Chave primária auto incremento
- titulo: Título da notificação (VARCHAR 255)
- mensagem: Corpo da mensagem (TEXT)
- token: Token do dispositivo (VARCHAR 255)
- dados_json: Dados extras em JSON (TEXT)
- timestamp: Timestamp Unix do envio (INT)
- entregue: 0=Não entregue, 1=Entregue (TINYINT)
- data_criacao: Data de criação automática (TIMESTAMP)
- data_entrega: Data da entrega (TIMESTAMP NULL)
- usuario_id: ID do usuário opcional (INT NULL)
- cartao: Número do cartão opcional (VARCHAR 20)

ÍNDICES:
- idx_token_timestamp: Para buscar por token e período
- idx_entregue: Para filtrar entregues/não entregues
- idx_usuario: Para buscar por usuário
- idx_cartao: Para buscar por cartão
- idx_data_criacao: Para ordenar por data
- idx_token_entregue: Para buscar pendentes de um token

CARACTERÍSTICAS:
- Engine: InnoDB (suporte a transações)
- Charset: utf8mb4 (suporte completo a Unicode)
- Collation: utf8mb4_unicode_ci (ordenação Unicode)
*/
