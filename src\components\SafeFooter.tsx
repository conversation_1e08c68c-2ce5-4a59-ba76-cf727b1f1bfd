import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import Cores from '../constants/Cores';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getNavigationBarHeight, getSafeBottomPadding, useSafeInsets } from '../utils/safeArea';

interface SafeFooterProps {
  currentScreen: 'Home' | 'Extrato' | 'CartaoVirtual';
  navigation: {
    navigate: (screen: string) => void;
  };
}

const SafeFooter = ({ currentScreen, navigation }: SafeFooterProps) => {
  const insets = useSafeInsets();
  const navigationBarHeight = getNavigationBarHeight();
  
  const handleNavigation = (screen: 'Home' | 'Extrato' | 'CartaoVirtual') => {
    if (screen !== currentScreen) {
      navigation.navigate(screen);
    }
  };

  // Calcular padding bottom seguro
  // Usar o maior valor entre insets.bottom e navigationBarHeight, com mínimo de 20px no Android
  const minimumPadding = Platform.OS === 'android' ? 20 : 0;
  const calculatedPadding = Math.max(insets.bottom, navigationBarHeight) + 12;
  const safeBottomPadding = Math.max(calculatedPadding, minimumPadding + 12);
  
  // Debug info (apenas em desenvolvimento)
  if (__DEV__) {
    console.log('🦶 SafeFooter Debug:', {
      platform: Platform.OS,
      insetsBottom: insets.bottom,
      navigationBarHeight,
      finalPadding: safeBottomPadding,
    });
  }

  return (
    <View style={[styles.footer, { paddingBottom: safeBottomPadding }]}>
      <View style={styles.footerContent}>
        <TouchableOpacity
          style={[styles.footerButton, currentScreen === 'Home' && styles.activeButton]}
          onPress={() => handleNavigation('Home')}
          activeOpacity={0.7}
        >
          <Text style={[styles.footerIcon, currentScreen === 'Home' && styles.activeIcon]}>🏠</Text>
          <Text style={[styles.footerText, currentScreen === 'Home' && styles.activeText]}>Início</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.footerButton, currentScreen === 'Extrato' && styles.activeButton]}
          onPress={() => handleNavigation('Extrato')}
          activeOpacity={0.7}
        >
          <Text style={[styles.footerIcon, currentScreen === 'Extrato' && styles.activeIcon]}>📄</Text>
          <Text style={[styles.footerText, currentScreen === 'Extrato' && styles.activeText]}>Extrato</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.footerButton, currentScreen === 'CartaoVirtual' && styles.activeButton]}
          onPress={() => handleNavigation('CartaoVirtual')}
          activeOpacity={0.7}
        >
          <Text style={[styles.footerIcon, currentScreen === 'CartaoVirtual' && styles.activeIcon]}>💳</Text>
          <Text style={[styles.footerText, currentScreen === 'CartaoVirtual' && styles.activeText]}>Cartão</Text>
        </TouchableOpacity>
      </View>
      
      {/* Indicador visual para debug (apenas em desenvolvimento) */}
      {__DEV__ && (
        <View style={styles.debugIndicator}>
          <Text style={styles.debugText}>
            Safe: {safeBottomPadding}px | Nav: {navigationBarHeight}px | Insets: {insets.bottom}px
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  footer: {
    backgroundColor: Cores.primaria,
    paddingTop: 8,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: Cores.primaria,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // paddingBottom será aplicado dinamicamente
  },
  footerContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  footerButton: {
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
    minWidth: 50,
  },
  activeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  footerIcon: {
    fontSize: 16,
    marginBottom: 2,
  },
  activeIcon: {
    transform: [{ scale: 1.1 }],
  },
  footerText: {
    color: Cores.textoBranco,
    fontSize: 9,
    fontWeight: '500',
    textAlign: 'center',
  },
  activeText: {
    fontWeight: '700',
    color: Cores.textoBranco,
  },
  debugIndicator: {
    position: 'absolute',
    top: -20,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 0, 0, 0.8)',
    paddingVertical: 2,
    paddingHorizontal: 8,
  },
  debugText: {
    color: 'white',
    fontSize: 10,
    textAlign: 'center',
    fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
  },
});

export default SafeFooter;
