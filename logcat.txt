layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.218  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117819, oldVsyncId=1117819,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.234  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117826, oldVsyncId=1117819, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.235  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117826, oldVsyncId=1117826,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.251  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117833, oldVsyncId=1117826, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.252  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117833, oldVsyncId=1117833,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.268  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117840, oldVsyncId=1117833,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.268  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117840, oldVsyncId=1117840,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.284  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117847, oldVsyncId=1117840, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.285  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117847, oldVsyncId=1117847,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.285 12505 12505 D nativeloader: Configuring clns-7 for other apk /system_ext/framework/miui-cameraopt.jar.
target_sdk_version=34, uses_libraries=ALL,
library_path=/product/priv-app/MiuiCamera/lib/arm64:/product/priv-app/MiuiCamera/MiuiCamera.apk!/lib/arm64-v8a,
permitted_path=/data:/mnt/expand:/data/user/0/com.android.camera
07-22 12:00:50.285 12505 12505 D nativeloader: Extending system_exposed_libraries: libbinauralrenderer_wrapper.qti.so:libhoaeffec 
ts.qti.so:libmisys_jni.xiaomi.so:libpag.hyperos.so:libffavc.hyperos.so:libxiaomi_modemdumpsdx55_jni.modem.so:libxiaomi_modemdump_ 
jni.modem.so:libmodemsmartengine_jni.modem.so:libupdateprof.qti.so:libthermalclient.qti.so:libdiag_system.qti.so:libqape.qti.so:l 
ibqesdk_ndk_platform.qti.so:liblistenjni.qti.so
07-22 12:00:50.301  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117854, oldVsyncId=1117847,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.301  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117854, oldVsyncId=1117854,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.310 12505 12505 D nativeloader: Configuring product-clns-8 for unbundled product apk 
/product/priv-app/MiuiCamera/MiuiCamera.apk. target_sdk_version=34,
uses_libraries=libOpenCL.so:libcdsprpc.so:libmialgo_ai_vision.so:libmialgo_utils.so, library_path=/product/priv-app/MiuiCamera/li 
b/arm64:/product/priv-app/MiuiCamera/MiuiCamera.apk!/lib/arm64-v8a:/product/lib64:/system/product/lib64,
permitted_path=/data:/mnt/expand:/data/user/0/com.android.camera:/product/lib64:/system/product/lib64
07-22 12:00:50.318  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117861, oldVsyncId=1117854,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.318  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117861, oldVsyncId=1117861, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.335  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117868, oldVsyncId=1117861, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.335  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117868, oldVsyncId=1117868,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.338 12505 12505 I MiuiForceDarkConfig: setConfig density:2.750000, mainRule:0, secondaryRule:0, tertiaryRule:0     
07-22 12:00:50.351  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117875, oldVsyncId=1117868, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.352  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117875, oldVsyncId=1117875,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.365  1193  1227 W IzatSvc_Wiper: wifiScanNotificationMsg:495] Associated AP not in LOWI scan.
07-22 12:00:50.368  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117882, oldVsyncId=1117875,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.368  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117882, oldVsyncId=1117882,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.375 12362 12391 I ReactNativeJS: Ô£à Token enviado junto com login para o PHP processar
07-22 12:00:50.375 12362 12391 I ReactNativeJS: '­ƒÄ» Token:', 'tecbiz_dev_1753196435368_va8p58wxg...'
07-22 12:00:50.380 12505 12505 I FirebaseApp: Device unlocked: initializing all Firebase APIs for app [DEFAULT]
07-22 12:00:50.383  2313  2734 D PasspointManager: ANQP entry not found for: e4:c0:e2:e9:cd:95:<TecBiz_Tim_5G>
07-22 12:00:50.385  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117889, oldVsyncId=1117882,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.385  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117889, oldVsyncId=1117889,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.395 12505 12505 I FirebaseCrashlytics: Initializing Firebase Crashlytics 18.3.6 for com.android.camera
07-22 12:00:50.398 12362 12391 I ReactNativeJS: ­ƒöÉ Oferecendo configura├º├úo de biometria...
07-22 12:00:50.402  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117896, oldVsyncId=1117889,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.402  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117896, oldVsyncId=1117896,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.415 12362 12362 D ScrollerOptimizationManager: registerConfigChangedListener
07-22 12:00:50.418 12362 12362 D ScrollerOptimizationManager: registerConfigChangedListener
07-22 12:00:50.418  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117903, oldVsyncId=1117896,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.422 12505 12526 E FirebaseCrashlytics: Settings request failed.
07-22 12:00:50.426 12362 12362 I ForceDarkHelperStubImpl: setViewRootImplForceDark: false for
com.mauriciocdz07.tecbizexpoapp.MainActivity@44c6f, reason: DarkModeEnabled
07-22 12:00:50.427 12505 12505 I FirebaseInitProvider: FirebaseApp initialization successful
07-22 12:00:50.433  2313  4062 D WindowManager: wms.Input focus has changed to Window{fbeb904 u0 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} display=0 updateInputWindows = false
07-22 12:00:50.434  2313  2364 D WindowManager: wms.Focus not requested for window=Window{fbeb904 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} because it has no surface or is not focusable.      
07-22 12:00:50.436 12505 12535 E MCAM_LocalMivi4InfoDataSource: local config file is not exists!!!
07-22 12:00:50.441  2313  4062 D CoreBackPreview: Window{fbeb904 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}: Setting back callback
OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@1a53021, mPriority=0,
mIsAnimationCallback=false}
07-22 12:00:50.444 12505 12536 W MCAM_CameraCloudConfig: cloudConfig initialized.
07-22 12:00:50.444 12505 12536 I CloudConfig: update request status, current: null, expectPrev: 9 status: 2
07-22 12:00:50.448 12505 12542 I CloudConfig: update request status, current: null, expectPrev: 9 status: 2
07-22 12:00:50.449 12505 12545 I CloudConfig: start request data, module: camera_app_mivi_v4_0
07-22 12:00:50.449 12505 12546 I CloudConfig: start request data, module: camera_feature
07-22 12:00:50.453 12362 12362 W 7.tecbizexpoapp: type=1400 audit(0.0:33051): avc:  denied  { read } for
name="u:object_r:vendor_display_prop:s0" dev="tmpfs" ino=457 scontext=u:r:untrusted_app:s0:c200,c257,c512,c768
tcontext=u:object_r:vendor_display_prop:s0 tclass=file permissive=0 app=com.mauriciocdz07.tecbizexpoapp
07-22 12:00:50.452  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117911, oldVsyncId=1117903, layerName=fbeb904        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:50.452  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117911, oldVsyncId=1117911, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.452  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117911, oldVsyncId=1117911,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.457 12362 12362 I ForceDarkHelperStubImpl: setViewRootImplForceDark: false for
com.mauriciocdz07.tecbizexpoapp.MainActivity@44c6f, reason: DarkModeEnabled
07-22 12:00:50.468  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117918, oldVsyncId=1117911, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.468  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117918, oldVsyncId=1117918, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.468  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117918, oldVsyncId=1117918, layerName=fbeb904        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:50.468  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117918, oldVsyncId=1117918,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.469  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117918, oldVsyncId=1117918,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.476  2313  3149 D WindowManager: wms.finishDrawingLocked: mDrawState=COMMIT_DRAW_PENDING Window{fbeb904 u0 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} in
Surface(name=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833)/@0x446b34
07-22 12:00:50.485  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117937, oldVsyncId=1117918, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.486  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117937, oldVsyncId=1117937, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.488  2313  2364 I WindowManager: wms.showSurfaceRobustly mWin:Window{fbeb904 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117937,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944, layerName=fbeb904        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:50.502  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117944, oldVsyncId=1117944,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.517 12505 12546 D HttpRequestHandler: [CloudConfig] channel[camera_feature] request not time yet
07-22 12:00:50.517 12505 12545 D HttpRequestHandler: [CloudConfig] channel[camera_app_mivi_v4_0] request not time yet
07-22 12:00:50.517 12505 12546 I MCAM_CameraCloudConfig: CloudConfigInfo: {"moduleKey":"camera_feature","itemInfo":[]}
07-22 12:00:50.517 12505 12546 I CloudConfig: start request data, module: camera_feature
07-22 12:00:50.517 12505 12558 I CloudConfig: start request data, module: camera_app_mivi_v4_0
07-22 12:00:50.518  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117957, oldVsyncId=1117957,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.518  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117957, oldVsyncId=1117957, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.525 12505 12558 I CloudConfig: start request data, module: camera_app_mivi_v4_0
07-22 12:00:50.525 12505 12558 I CloudConfig: start request data, module: camera_app_mivi_v4_0
07-22 12:00:50.525 12505 12558 I CloudConfig: start request data, module: camera_app_mivi_v4_0
07-22 12:00:50.528 12505 12546 I CloudConfig: update request status, current: 2, expectPrev: 2 status: 4
07-22 12:00:50.528 12505 12558 E MCAM_LocalMivi4InfoDataSource: local config file is not exists!!!
07-22 12:00:50.529 12505 12545 I MCAM_CameraCloudConfig: CloudConfigInfo: {"moduleKey":"camera_app_mivi_v4_0","itemInfo":[]}      
07-22 12:00:50.529 12505 12545 I CloudConfig: update request status, current: 2, expectPrev: 2 status: 4
07-22 12:00:50.535  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117982, oldVsyncId=1117957,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.535  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117982, oldVsyncId=1117982, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.535  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117982, oldVsyncId=1117982,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.536  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1117982, oldVsyncId=1117982, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.545 12505 12546 D HttpRequestHandler: [CloudConfig] channel[camera_app_mivi_v4_0] request not time yet
07-22 12:00:50.547 12505 12536 E MCAM_LocalMivi4InfoDataSource: local config file is not exists!!!
07-22 12:00:50.548 12505 12536 I CloudConfig: update request status, current: null, expectPrev: 9 status: 2
07-22 12:00:50.548 12505 12536 I CloudConfig: start request data, module: camera_dynamic
07-22 12:00:50.551 12505 12536 D HttpRequestHandler: [CloudConfig] channel[camera_dynamic] request not time yet
07-22 12:00:50.551 12505 12536 I CloudConfig: update request status, current: 2, expectPrev: 2 status: 4
07-22 12:00:50.551 12505 12536 I MCAM_CameraCloudConfig: CloudConfigInfo: {"moduleKey":"camera_dynamic","itemInfo":[]}
07-22 12:00:50.551 12505 12546 D HttpRequestHandler: [CloudConfig] channel[camera_app_mivi_v4_0] request not time yet
07-22 12:00:50.551 12505 12536 W MCAM_CloudDynamicInfoDataSource: Local configuration does not exist.
07-22 12:00:50.552  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118001, oldVsyncId=1118001,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.554 12505 12545 D HttpRequestHandler: [CloudConfig] channel[camera_app_mivi_v4_0] request not time yet
07-22 12:00:50.562 12505 12560 D HttpRequestHandler: [CloudConfig] channel[camera_app_mivi_v4_0] request not time yet
07-22 12:00:50.564 12505 12562 D HttpRequestHandler: [CloudConfig] channel[camera_feature] request not time yet
07-22 12:00:50.568  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118026, oldVsyncId=1118001, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.569  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118026, oldVsyncId=1118026,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.569  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118026, oldVsyncId=1118026,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.569  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118026, oldVsyncId=1118026,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.571 12505 12505 I MCAM_BugHunterManager: [P_PERFORM]: BUG_HUNTER_PROP=-1, bugHunterCloud=-1,
bugHunterAppConfig=-1, sBugHunterType=-1
07-22 12:00:50.585  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118051, oldVsyncId=1118026,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.585  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118051, oldVsyncId=1118051,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.585  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118051, oldVsyncId=1118051, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.585  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118051, oldVsyncId=1118051,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.585  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118051, oldVsyncId=1118051,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.602  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118070, oldVsyncId=1118070, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.602  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118070, oldVsyncId=1118070,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.602  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118070, oldVsyncId=1118070,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.602  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118070, oldVsyncId=1118070,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.619  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118089, oldVsyncId=1118089, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.619  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118089, oldVsyncId=1118089,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.619  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118089, oldVsyncId=1118089,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.619  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118089, oldVsyncId=1118089,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.619  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118089, oldVsyncId=1118089,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.635  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118115, oldVsyncId=1118115, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.635  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118115, oldVsyncId=1118115,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.635  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118115, oldVsyncId=1118115,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.635  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118115, oldVsyncId=1118115,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.635  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118115, oldVsyncId=1118115,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.652  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118134, oldVsyncId=1118134, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.652  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118134, oldVsyncId=1118134,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.652  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118134, oldVsyncId=1118134,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.652  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118134, oldVsyncId=1118134,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.652  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118134, oldVsyncId=1118134,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153, layerName=fbeb904 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1834
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.669  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118153, oldVsyncId=1118153,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.685  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118178, oldVsyncId=1118178,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.685  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118178, oldVsyncId=1118178,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.686  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118178, oldVsyncId=1118178,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:50.702  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118197, oldVsyncId=1118197, layerName=40967a5 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.702  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118197, oldVsyncId=1118197,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.702  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118197, oldVsyncId=1118197,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.719  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118216, oldVsyncId=1118197, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:50.719  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118216, oldVsyncId=1118216,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.719  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118216, oldVsyncId=1118216,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.736  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118229, oldVsyncId=1118216, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.736  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118229, oldVsyncId=1118229,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.752  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118242, oldVsyncId=1118229, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.752  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118242, oldVsyncId=1118242,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.769  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118249, oldVsyncId=1118242, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.769  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118249, oldVsyncId=1118249,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.786  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118256, oldVsyncId=1118249, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.786  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118256, oldVsyncId=1118256,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.802  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118263, oldVsyncId=1118256, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.803  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118263, oldVsyncId=1118263,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.819  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118270, oldVsyncId=1118263, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.819  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118270, oldVsyncId=1118270,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.836  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118277, oldVsyncId=1118270, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.836  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118277, oldVsyncId=1118277,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.852  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118284, oldVsyncId=1118277, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.853  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118284, oldVsyncId=1118284, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.869  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118291, oldVsyncId=1118284,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.869  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118291, oldVsyncId=1118291, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.886  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118298, oldVsyncId=1118291,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.886  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118298, oldVsyncId=1118298, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.903  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118305, oldVsyncId=1118298,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.903  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118305, oldVsyncId=1118305,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.919  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118312, oldVsyncId=1118305, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.919  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118312, oldVsyncId=1118312, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.936  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118319, oldVsyncId=1118312, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.936  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118319, oldVsyncId=1118319,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.953  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118326, oldVsyncId=1118326, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.953  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118326, oldVsyncId=1118326,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.969  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118339, oldVsyncId=1118326, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.970  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118339, oldVsyncId=1118339,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.986  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118352, oldVsyncId=1118339, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.986  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118352, oldVsyncId=1118352,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:50.997 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~Y7pgSxQL3fTpOOJWCOEdGQ==/com.google.android.contacts-d7AMWbkQa6Ri4WOCz3mkBw==/split_config.arm64_v8a.apk' with 1      
weak references
07-22 12:00:50.997 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and 
/data/app/~~Y7pgSxQL3fTpOOJWCOEdGQ==/com.google.android.contacts-d7AMWbkQa6Ri4WOCz3mkBw==/split_config.pt.apk' with 1 weak        
references
07-22 12:00:50.997 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~Y7pgSxQL3fTpOOJWCOEdGQ==/com.google.android.contacts-d7AMWbkQa6Ri4WOCz3mkBw==/split_config.xxhdpi.apk' with 1 weak    
references
07-22 12:00:51.000 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~ANRuXGEnPIAfbEgr2Y_I6g==/com.google.android.gms-uNW3D0EquZNU4pf1uyqWdA==/split_config.pt.apk' with 1 weak references  
07-22 12:00:51.000 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~ANRuXGEnPIAfbEgr2Y_I6g==/com.google.android.gms-uNW3D0EquZNU4pf1uyqWdA==/split_config.xxhdpi.apk' with 1 weak
references
07-22 12:00:51.002 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~PgxuzdgIlo7Mmkuoxak9dQ==/com.google.android.apps.tachyon-tqboGZTboMxu6Exs9mIcMg==/split_config.arm64_v8a.apk' with 1  
weak references
07-22 12:00:51.002 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~PgxuzdgIlo7Mmkuoxak9dQ==/com.google.android.apps.tachyon-tqboGZTboMxu6Exs9mIcMg==/split_config.pt.apk' with 1 weak    
references
07-22 12:00:51.002 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~PgxuzdgIlo7Mmkuoxak9dQ==/com.google.android.apps.tachyon-tqboGZTboMxu6Exs9mIcMg==/split_config.xxhdpi.apk' with 1     
weak references
07-22 12:00:51.003  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118365, oldVsyncId=1118352,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.003  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118365, oldVsyncId=1118365, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.005 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~-2vHmWYRV9c102B40mja1g==/com.whatsapp-5w11tgcWmnAWTj2ASaRyMA==/split_config.arm64_v8a.apk' with 1 weak references     
07-22 12:00:51.007 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~G1w7cZfFER7TsNqxGa7a2A==/us.zoom.videomeetings-8O4ZCUxMVmY9D9YybPWl8Q==/split_config.arm64_v8a.apk' with 1 weak       
references
07-22 12:00:51.007 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~G1w7cZfFER7TsNqxGa7a2A==/us.zoom.videomeetings-8O4ZCUxMVmY9D9YybPWl8Q==/split_config.xxhdpi.apk' with 1 weak
references
07-22 12:00:51.009 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~q8O7jRxY3bGTRawf0BzQbw==/com.microsoft.teams-wt3HiOGTPK0RaSkxMnCYeg==/split_config.arm64_v8a.apk' with 1 weak
references
07-22 12:00:51.009 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~q8O7jRxY3bGTRawf0BzQbw==/com.microsoft.teams-wt3HiOGTPK0RaSkxMnCYeg==/split_config.pt.apk' with 1 weak references     
07-22 12:00:51.010 11550 11556 W d.process.acore: ApkAssets: Deleting an ApkAssets object '<empty> and
/data/app/~~q8O7jRxY3bGTRawf0BzQbw==/com.microsoft.teams-wt3HiOGTPK0RaSkxMnCYeg==/split_config.xxhdpi.apk' with 1 weak references 
07-22 12:00:51.020  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118378, oldVsyncId=1118378,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.020  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118378, oldVsyncId=1118378,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.036  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118391, oldVsyncId=1118391, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.036  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118391, oldVsyncId=1118391,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.053  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118404, oldVsyncId=1118404, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.053  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118404, oldVsyncId=1118404,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.070  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118417, oldVsyncId=1118417, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.087  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118430, oldVsyncId=1118430, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.103  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118443, oldVsyncId=1118443, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.103  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118443, oldVsyncId=1118443,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.120  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118456, oldVsyncId=1118456,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.120  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118456, oldVsyncId=1118456,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.137  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118469, oldVsyncId=1118469, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.137  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118469, oldVsyncId=1118469,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.153  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118482, oldVsyncId=1118482, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.153  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118482, oldVsyncId=1118482,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.170  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118495, oldVsyncId=1118495,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.170  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118495, oldVsyncId=1118495,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.187  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118508, oldVsyncId=1118508, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.187  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118508, oldVsyncId=1118508,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.204  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118521, oldVsyncId=1118521, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.220  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118534, oldVsyncId=1118534,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.220  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118534, oldVsyncId=1118534, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.237  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118547, oldVsyncId=1118547, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.237  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118547, oldVsyncId=1118547,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.254  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118560, oldVsyncId=1118560, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.254  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118560, oldVsyncId=1118560,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.270  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118573, oldVsyncId=1118573, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.270  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118573, oldVsyncId=1118573,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.287  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118586, oldVsyncId=1118586, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.287  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118586, oldVsyncId=1118586,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.304  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118599, oldVsyncId=1118599, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.304  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118599, oldVsyncId=1118599,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.321  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118612, oldVsyncId=1118612, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.337  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118625, oldVsyncId=1118625, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.337  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118625, oldVsyncId=1118625,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.354  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118638, oldVsyncId=1118638, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.354  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118638, oldVsyncId=1118638,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.371  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118651, oldVsyncId=1118651, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.371  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118651, oldVsyncId=1118651,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.387  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118664, oldVsyncId=1118664, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.387  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118664, oldVsyncId=1118664,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.404  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118677, oldVsyncId=1118677, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.404  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118677, oldVsyncId=1118677,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.421  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118690, oldVsyncId=1118690, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.421  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118690, oldVsyncId=1118690,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.438  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118703, oldVsyncId=1118703, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.455  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118716, oldVsyncId=1118716, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.471  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118729, oldVsyncId=1118729, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.471  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118729, oldVsyncId=1118729, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.487  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118742, oldVsyncId=1118742,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.488  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118742, oldVsyncId=1118742,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.504  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118755, oldVsyncId=1118755, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.505  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118755, oldVsyncId=1118755,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.521  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118768, oldVsyncId=1118768, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.538  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118781, oldVsyncId=1118781, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.554  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118794, oldVsyncId=1118794, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.554  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118794, oldVsyncId=1118794,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.571  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118807, oldVsyncId=1118807, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.571  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118807, oldVsyncId=1118807,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.588  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118820, oldVsyncId=1118820, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.604  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118833, oldVsyncId=1118833, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.605  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118833, oldVsyncId=1118833,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.621  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118846, oldVsyncId=1118846, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.621  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118846, oldVsyncId=1118846,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.638  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118859, oldVsyncId=1118859, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.638  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118859, oldVsyncId=1118859,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.655  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118872, oldVsyncId=1118872, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.655  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118872, oldVsyncId=1118872,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.671  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118885, oldVsyncId=1118885, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.671  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118885, oldVsyncId=1118885,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.688  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118898, oldVsyncId=1118898, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.688  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118898, oldVsyncId=1118898,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.705  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118911, oldVsyncId=1118911, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.705  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118911, oldVsyncId=1118911,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.712 12594 12594 D nativeloader: Configuring clns-shared-7 for other apk
/system/priv-app/HTMLViewer/HTMLViewer.apk. target_sdk_version=35, uses_libraries=,
library_path=/system/priv-app/HTMLViewer/lib/arm64:/system/lib64:/system_ext/lib64,
permitted_path=/data:/mnt/expand:/data/user/0/com.android.htmlviewer:/system/priv-app/HTMLViewer:/system/lib64:/system_ext/lib64  
07-22 12:00:51.721  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118924, oldVsyncId=1118924, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.721  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118924, oldVsyncId=1118924,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.733 12594 12594 I MiuiForceDarkConfig: setConfig density:2.750000, mainRule:0, secondaryRule:0, tertiaryRule:0
07-22 12:00:51.738  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118937, oldVsyncId=1118937,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.738  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118937, oldVsyncId=1118937,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.755  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118950, oldVsyncId=1118950, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.755  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118950, oldVsyncId=1118950,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.772  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118963, oldVsyncId=1118963, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.772  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118963, oldVsyncId=1118963,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.788  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118976, oldVsyncId=1118976,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.788  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118976, oldVsyncId=1118976,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.805  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118989, oldVsyncId=1118989, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.805  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1118989, oldVsyncId=1118989,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.822  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119002, oldVsyncId=1119002, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.822  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119002, oldVsyncId=1119002,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.838  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119015, oldVsyncId=1119015, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.838  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119015, oldVsyncId=1119015,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.855  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119028, oldVsyncId=1119028, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.855  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119028, oldVsyncId=1119028,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.872  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119041, oldVsyncId=1119041,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.872  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119041, oldVsyncId=1119041,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.888  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119054, oldVsyncId=1119054, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.889  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119054, oldVsyncId=1119054,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.892  2313  2693 I MIUIInput: [MotionEvent] publisher action=0x0, deviceId=5, 11915307, channel 'fbeb904 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity (server)'
07-22 12:00:51.893 12362 12362 I MIUIInput: [MotionEvent] ViewRootImpl windowName
'com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity', { action=ACTION_DOWN, id[0]=0, pointerCount=1,    
eventTime=11915307, downTime=11915307, phoneEventTime=12:00:51.891 } moveCount:0
07-22 12:00:51.905  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119067, oldVsyncId=1119067,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.905  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119067, oldVsyncId=1119067,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.922  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119080, oldVsyncId=1119080, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.922  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119080, oldVsyncId=1119080,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.939  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119093, oldVsyncId=1119093, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.956  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119106, oldVsyncId=1119106, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.972  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119119, oldVsyncId=1119119, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.980  2313  2693 I MIUIInput: [MotionEvent] publisher action=0x1, deviceId=5, 11915395, channel 'fbeb904 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity (server)'
07-22 12:00:51.981 12362 12362 I MIUIInput: [MotionEvent] ViewRootImpl windowName
'com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity', { action=ACTION_UP, id[0]=0, pointerCount=1,      
eventTime=11915395, downTime=11915307, phoneEventTime=12:00:51.979 } moveCount:0
07-22 12:00:51.989  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119132, oldVsyncId=1119132,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:51.989  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119132, oldVsyncId=1119132,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.005  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119145, oldVsyncId=1119132, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:52.005  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119145, oldVsyncId=1119145,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.015  2313  6015 D CoreBackPreview: Window{fbeb904 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}: Setting back callback null
07-22 12:00:52.022  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119158, oldVsyncId=1119145,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:52.022  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119158, oldVsyncId=1119158,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.022  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119158, oldVsyncId=1119158,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.026 12362 12391 I ReactNativeJS: 'ƒöù Fazendo requisi├º├úo para autoriza├º├Áes:',
'https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=eac833&codass=60&codent=52'
07-22 12:00:52.033  2313  6015 W InputManager-JNI: Input channel object 'fbeb904 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity (client)' was disposed without first being removed   
with the input manager!
07-22 12:00:52.039  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119177, oldVsyncId=1119177,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.040  2313  6015 D WindowManager: wms.Input focus has changed to Window{40967a5 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} display=0 updateInputWindows = false
07-22 12:00:52.055  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119196, oldVsyncId=1119177, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203, layerName=fbeb904        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:52.072  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119203, oldVsyncId=1119203,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:52.089  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119228, oldVsyncId=1119228, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.089  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119228, oldVsyncId=1119228,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.106  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119253, oldVsyncId=1119228, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.106  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119253, oldVsyncId=1119253,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.122  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119272, oldVsyncId=1119253, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.122  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119272, oldVsyncId=1119272,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.139  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119291, oldVsyncId=1119272, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.139  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119291, oldVsyncId=1119291,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.156  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119310, oldVsyncId=1119291, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.156  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119310, oldVsyncId=1119310, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.173  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119329, oldVsyncId=1119310, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.173  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119329, oldVsyncId=1119329,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.189  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119348, oldVsyncId=1119348, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.189  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119348, oldVsyncId=1119348,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.206  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119367, oldVsyncId=1119367,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.206  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119367, oldVsyncId=1119367,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.222  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119386, oldVsyncId=1119367, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.223  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119386, oldVsyncId=1119386, 
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.223  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119386, oldVsyncId=1119386,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:52.239  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119405, oldVsyncId=1119405, layerName=fbeb904 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830
07-22 12:00:52.239  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119405, oldVsyncId=1119405,
layerName=Surface(name=fbeb904 com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1830)/@0x9a9cce9 -    
animation-leash of window_animation#1837
07-22 12:00:52.239  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119405, oldVsyncId=1119405, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.239  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119405, oldVsyncId=1119405,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1833
07-22 12:00:52.256  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119430, oldVsyncId=1119430, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.256  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119430, oldVsyncId=1119430, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.273  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119449, oldVsyncId=1119449, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.273  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119449, oldVsyncId=1119449, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.289  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119468, oldVsyncId=1119449, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.306  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119487, oldVsyncId=1119487, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.323  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119500, oldVsyncId=1119487, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.339  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119513, oldVsyncId=1119513, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.356  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119526, oldVsyncId=1119513, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.359 12362 12362 D ScrollerOptimizationManager: registerConfigChangedListener
07-22 12:00:52.366 12362 12362 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:ee69b1f8: onRequestHide at ORIGIN_CLIENT reason      
HIDE_SOFT_INPUT fromUser false
07-22 12:00:52.367  2313  2703 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:ee69b1f8: onCancelled at PHASE_SERVER_SHOULD_HIDE    
07-22 12:00:52.387  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 10000000 based on min/max delay_ns
07-22 12:00:52.443  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 20000000 based on min/max delay_ns
07-22 12:00:52.456  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119586, oldVsyncId=1119573, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.456  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119586, oldVsyncId=1119586, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.458  2313  2630 D MiuiFreeformServiceImpl: addLaunchFreeformActivityOptionIfNeed skip due to not find sourceTask:  
null sourceFfas: null sourceFreeFormActivity: ActivityRecord{e292cac u0 com.mauriciocdz07.tecbizexpoapp/.MainActivity t188}       
07-22 12:00:52.466  2313  2630 D WindowManager: setLockTaskAuth: task=Task{2930657 #188 type=standard
A=10456:com.mauriciocdz07.tecbizexpoapp} mLockTaskAuth=LOCK_TASK_AUTH_PINNABLE
07-22 12:00:52.467  2313  2630 D MiuiFreeFormManagerService: onStartActivityInner as = Task{2930657 #188 type=standard
A=10456:com.mauriciocdz07.tecbizexpoapp} op = null
07-22 12:00:52.476  2313  2630 I ActivityTaskManager: START u0 {act=android.content.pm.action.REQUEST_PERMISSIONS
pkg=com.google.android.permissioncontroller
cmp=com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity (has
extras)} with LAUNCH_MULTIPLE from uid 10456 from pid 12362 callingPackage com.mauriciocdz07.tecbizexpoapp
(BAL_ALLOW_VISIBLE_WINDOW) result code=0
07-22 12:00:52.478  3295  3461 I SoScStageCoordinator: Transition requested:TransitionRequestInfo { type = OPEN, triggerTask = 
TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN
cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} topActivity=ComponentInf 
o{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity}
origActivity=null realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}        
numActivities=2 lastActiveTime=11915889 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1
isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@e63a8e3}  
topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1
lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{7642e0
com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[] positionInParent=Point(0, 0)
parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1        
isTopActivityTransparent=true appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false
topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false
isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false
isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1
topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2400 isUserFullscreenOverrideEnabled=false
isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden
freeformCameraCompatMode=inactive}} isImmersive=false mTopActivityRequestOrientation=-1 mBehindAppLockPkg=null mOriginatingUid=0  
isEmbedded=false shouldBeVisible=true mIsCastMode=false mTopActivityMediaSize=null mTopActivityRecordName=ActivityRecord{ec525a8  
u0 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity t188}
mTopActivityOrientation=-2}, pipTask = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 132 }
isSoScActive:false triggerTask:TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true baseIntent=Intent {
act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000
cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} topActivity=ComponentInf 
o{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity}
origActivity=null realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}        
numActivities=2 lastActiveTime=11915889 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1
isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@e63a8e3}  
topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1
lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{7642e0
com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[] positionInParent=Point(0, 0)
parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1        
isTopActivityTransparent=true appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false
topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false isLetterboxDo 
07-22 12:00:52.479 12362 12362 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:f03b4ed5: onRequestHide at ORIGIN_CLIENT reason      
HIDE_SOFT_INPUT_CLOSE_CURRENT_SESSION fromUser false
07-22 12:00:52.480  2313  2703 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:f03b4ed5: onCancelled at PHASE_SERVER_SHOULD_HIDE
07-22 12:00:52.483  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 10000000 based on min/max delay_ns   
07-22 12:00:52.485  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 200000000 based on min/max delay_ns  
07-22 12:00:52.488  3295  3295 D NotificationHeaderExpandController: onExpansionChanged: progress =  0.0
07-22 12:00:52.499  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 20000000 based on min/max delay_ns   
07-22 12:00:52.503  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 200000000 based on min/max delay_ns  
07-22 12:00:52.507  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119625, oldVsyncId=1119612, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.507  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119625, oldVsyncId=1119625, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.508  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 1000000000 based on min/max delay_ns 
07-22 12:00:52.509  6671 21706 I ProcessMonitor: onForegroundInfoChanged:
ForegroundInfo{mForegroundPackageName='com.google.android.permissioncontroller', mForegroundUid=10273, mForegroundPid=9675,       
mLastForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', mLastForegroundUid=10456, mLastForegroundPid=12362,
mMultiWindowForegroundPackageName='com.google.android.permissioncontroller', mMultiWindowForegroundUid=10273, mFlags=0}
07-22 12:00:52.509  3168 24595 D BluetoothLatencyMode:
ForegroundInfo{mForegroundPackageName='com.google.android.permissioncontroller', mForegroundUid=10273, mForegroundPid=9675,       
mLastForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', mLastForegroundUid=10456, mLastForegroundPid=12362,
mMultiWindowForegroundPackageName='com.google.android.permissioncontroller', mMultiWindowForegroundUid=10273, mFlags=0}
07-22 12:00:52.513 16154 28766 I GST     : forePkg: com.google.android.permissioncontroller, preForePkg:
com.mauriciocdz07.tecbizexpoapp
07-22 12:00:52.521  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 10000000 based on min/max delay_ns   
07-22 12:00:52.523  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119644, oldVsyncId=1119625,
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:52.525  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 200000000 based on min/max delay_ns
07-22 12:00:52.527  9675 12630 I AdrenoGLES-0: Build Config                     : S P 12.1.1 AArch64
07-22 12:00:52.536  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 20000000 based on min/max delay_ns   
07-22 12:00:52.547  9367 14500 D PerfEngineController: 
ForegroundInfo{mForegroundPackageName='com.google.android.permissioncontroller', mForegroundUid=10273, mForegroundPid=9675,       
mLastForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', mLastForegroundUid=10456, mLastForegroundPid=12362,
mMultiWindowForegroundPackageName='com.google.android.permissioncontroller', mMultiWindowForegroundUid=10273, mFlags=0}
07-22 12:00:52.577  3295  3295 D MiuiBubbleManager: onEntryUpdated: 
com.android.systemui.statusbar.notification.collection.NotificationEntry@8777af8 true
07-22 12:00:52.591  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 10000000 based on min/max delay_ns
07-22 12:00:52.602  9675  9675 D ScrollerOptimizationManager: registerConfigChangedListener
07-22 12:00:52.612  3355  3635 W RecentsModel: getRunningTask   taskInfo=TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true 
baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000
cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} topActivity=ComponentInf 
o{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity}
origActivity=null realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}        
numActivities=2 lastActiveTime=11916024 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1
isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@b84a580}  
topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1
lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{2e7a7b9
com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[] positionInParent=Point(0, 0)
parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1        
isTopActivityTransparent=true appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false
topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false
isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false
isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1
topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2400 isUserFullscreenOverrideEnabled=false
isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden
freeformCameraCompatMode=inactive}} isImmersive=false mTopActivityRequestOrientation=-1 mBehindAppLockPkg=null mOriginatingUid=0  
isEmbedded=false shouldBeVisible=true mIsCastMode=false mTopActivityMediaSize=null mTopActivityRecordName=ActivityRecord{ec525a8  
u0 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity t188}
mTopActivityOrientation=-1}
07-22 12:00:52.624  3355  3635 E ActivityManagerWrapper:  mainTaskId=188   userId=0   windowMode=1   baseIntent=Intent {
act=android.intent.action.MAIN flag=270532608
cmp=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} }
07-22 12:00:52.636  3355  3635 W RecentsModel: getRunningTask   taskInfo=TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true 
baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000
cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} topActivity=ComponentInf 
o{com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity}
origActivity=null realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}        
numActivities=2 lastActiveTime=11916050 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1
isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@4d72b5f}  
topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1
lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{94e99ac
com.android.permissioncontroller.permission.ui.GrantPermissionsActivity} launchCookies=[] positionInParent=Point(0, 0)
parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isSleeping=false locusId=null displayAreaFeatureId=1        
isTopActivityTransparent=true appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false
topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation= falseisLetterboxEducationEnabled= false
isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false
isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1
topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2400 isUserFullscreenOverrideEnabled=false
isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo { cameraCompatControlState=hidden
freeformCameraCompatMode=inactive}} isImmersive=false mTopActivityRequestOrientation=-1 mBehindAppLockPkg=null mOriginatingUid=0  
isEmbedded=false shouldBeVisible=true mIsCastMode=false mTopActivityMediaSize=null mTopActivityRecordName=ActivityRecord{ec525a8  
u0 com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity t188}
mTopActivityOrientation=-1}
07-22 12:00:52.640  3295  3295 D NotificationHeaderExpandController: onExpansionChanged: progress =  0.0
07-22 12:00:52.641  3355  3635 E ActivityManagerWrapper:  mainTaskId=188   userId=0   windowMode=1   baseIntent=Intent {
act=android.intent.action.MAIN flag=270532608
cmp=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} }
07-22 12:00:52.659  3295  3295 I PromptViewAnimState: topActivityMayChanged: topActivity=com.mauriciocdz07.tecbizexpoapp --> 
com.google.android.permissioncontroller
07-22 12:00:52.752  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.752  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.752  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.752  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.756  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.756  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.756  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.756  1351  2063 D MI-SF   : debug.config.media.video.dolby_vision_suports == false
07-22 12:00:52.757  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119740, oldVsyncId=1119740, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.765  9675  9675 E GrantPermissionsViewModel: None of [android.permission.ACCESS_FINE_LOCATION, 
android.permission.ACCESS_COARSE_LOCATION] in {android.permission-group.STORAGE=[android.permission.READ_EXTERNAL_STORAGE,        
android.permission.WRITE_EXTERNAL_STORAGE], android.permission-group.NOTIFICATIONS=[android.permission.POST_NOTIFICATIONS]}       
07-22 12:00:52.767  2313  2364 D WindowManager: Calling onTransitionReady info={id=132 t=OPEN f=0x0 trk=0 opt={t=FROM_STYLE}      
r=[0@Point(0, 0)] c=[{null m=OPEN f=TRANSLUCENT|FILLS_TASK leash=Surface(name=ActivityRecord{ec525a8 u0
com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity
t188}#1840)/@0x11af454 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) d=-1->0 r=-1->0:-1 windowMode=0->1 bc=fff1f0f7
component=com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity
v=true}] mk=[true] noAni=[false] df=[false] rsa=[false] oa=[true] ho=[false] iSync=[-1]}, mToken=Token{9281150
TransitionRecord{faf9866 id=132 type=OPEN flags=0x0}}
07-22 12:00:52.773  2313  6015 D MiuiFreeFormGestureController: deliverResultForFinishActivity resultTo: ActivityRecord{e292cac   
u0 com.mauriciocdz07.tecbizexpoapp/.MainActivity t188} resultFrom: ActivityRecord{ec525a8 u0
com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity t188 f}}
isInVideoOrGameScene: false intent: Intent { act=android.content.pm.action.REQUEST_PERMISSIONS flg=0x800000
pkg=com.google.android.permissioncontroller
cmp=com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity (has extras)  
}
07-22 12:00:52.790  2313  2630 D PerfStubImpl: perfWarmLaunchBoost: com.mauriciocdz07.tecbizexpoapp, -1, 1
07-22 12:00:52.791  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119778, oldVsyncId=1119778, layerName=40967a5 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.797  2313  2630 D WindowManager: wms.Input focus has changed to Window{40967a5 u0
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} display=0 updateInputWindows = true
07-22 12:00:52.801  3186  3405 I DynamicDDSService: handleMessage(ACTIVITY_INVOKE{appInfo=com.mauriciocdz07.tecbizexpoapp
isFullScreen=}true)
07-22 12:00:52.802 12362 12374 D ActivityThread: handleBoundsCompatInfoChanged remove: name=com.mauriciocdz07.tecbizexpoapp       
07-22 12:00:52.805  2313  2630 D WindowManager: Calling onTransitionReady info={id=133 t=CLOSE f=0x0 trk=1 opt={t=FROM_STYLE}     
r=[0@Point(0, 0)] c=[{null m=CLOSE f=TRANSLUCENT|FILLS_TASK|IS_OCCLUDED leash=Surface(name=ActivityRecord{ec525a8 u0
com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity
t188}#1840)/@0x11af454 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) d=0 bc=fff1f0f7
component=com.google.android.permissioncontroller/com.android.permissioncontroller.permission.ui.GrantPermissionsActivity
v=false}] mk=[true] noAni=[false] df=[false] rsa=[false] oa=[true] ho=[false] iSync=[-1]}, mToken=Token{5905367
TransitionRecord{4653705 id=133 type=CLOSE flags=0x0}}
07-22 12:00:52.815  6671 24163 I ProcessMonitor: onForegroundInfoChanged: 
ForegroundInfo{mForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', mForegroundUid=10456, mForegroundPid=12362,
mLastForegroundPackageName='com.google.android.permissioncontroller', mLastForegroundUid=10273, mLastForegroundPid=9675,
mMultiWindowForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', mMultiWindowForegroundUid=10456, mFlags=0}
07-22 12:00:52.816  3168  8393 D BluetoothLatencyMode: ForegroundInfo{mForegroundPackageName='com.mauriciocdz07.tecbizexpoapp',   
mForegroundUid=10456, mForegroundPid=12362, mLastForegroundPackageName='com.google.android.permissioncontroller',
mLastForegroundUid=10273, mLastForegroundPid=9675, mMultiWindowForegroundPackageName='com.mauriciocdz07.tecbizexpoapp',
mMultiWindowForegroundUid=10456, mFlags=0}
07-22 12:00:52.816 16154 28766 I GST     : forePkg: com.mauriciocdz07.tecbizexpoapp, preForePkg:
com.google.android.permissioncontroller
07-22 12:00:52.820  6671 24163 W MIUISafety-Monitor: screen share ensure fg = com.mauriciocdz07.tecbizexpoapp protect = false     
07-22 12:00:52.823  2313  2891 D CameraBlackCoveredManager: mForegroundPackage = com.mauriciocdz07.tecbizexpoapp
mOccupiedPackage = null
07-22 12:00:52.823  9367 14500 D PerfEngineController: ForegroundInfo{mForegroundPackageName='com.mauriciocdz07.tecbizexpoapp', 
mForegroundUid=10456, mForegroundPid=12362, mLastForegroundPackageName='com.google.android.permissioncontroller',
mLastForegroundUid=10273, mLastForegroundPid=9675, mMultiWindowForegroundPackageName='com.mauriciocdz07.tecbizexpoapp',
mMultiWindowForegroundUid=10456, mFlags=0}
07-22 12:00:52.824  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119804, oldVsyncId=1119804, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.829  3355  3355 D RecentsImpl: mActivityStateObserver com.mauriciocdz07.tecbizexpoapp.MainActivity
07-22 12:00:52.829  3355  3355 W RecentsImpl: onResumed className=com.mauriciocdz07.tecbizexpoapp.MainActivity
forceUpdate=false   mIsInAnotherPro=false   isKeyguardLocked=false   mIsStatusBarExpansion=false   mSecuritySpaceGuideShown=false 
07-22 12:00:52.863 12362 12391 I ReactNativeJS: ­ƒöì Debug token storage (fallback):
07-22 12:00:52.863  2313  2703 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:9b609650: onRequestHide at ORIGIN_SERVER reason      
HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
07-22 12:00:52.863 12362 12391 I ReactNativeJS: '   - Token encontrado:', 'N├âO'
07-22 12:00:52.863 12362 12391 I ReactNativeJS: ÔÜá´©Å Nenhum token v├ílido encontrado
07-22 12:00:52.863 12362 12391 I ReactNativeJS: '­ƒÄ» Token final para login:', 'NENHUM'
07-22 12:00:52.863  2313  2703 I ImeTracker: com.mauriciocdz07.tecbizexpoapp:9b609650: onCancelled at PHASE_SERVER_SHOULD_HIDE    
07-22 12:00:52.863 12362 12391 I ReactNativeJS: ÔØî Nenhum token dispon├¡vel - login sem tokenid
07-22 12:00:52.863 12362 12391 I ReactNativeJS: 'ƒöù URL FINAL da requisi├º├úo:',
'https://www2.tecbiz.com.br/tecbiz/tecbiz.php?a=2547a0&senass=123456&carass=6298690100016687'
07-22 12:00:52.864 12362 12391 I ReactNativeJS:   tokenid: 'N├âO ENVIADO' }
07-22 12:00:52.868  4501  4501 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1311
onStartInput(EditorInfo{EditorInfo{packageName=com.mauriciocdz07.tecbizexpoapp, inputType=0, inputTypeString=NULL,
enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED,     
actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null,
extras=Bundle[mParcelledData.dataSize=72], hintText=null, hintLocales=[]}}, false)
07-22 12:00:52.870  2313  6014 W PackageConfigPersister: App-specific configuration not found for packageName:
com.mauriciocdz07.tecbizexpoapp and userId: 0
07-22 12:00:52.874  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119867, oldVsyncId=1119867, layerName=40967a5 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.880  2313  2364 D WindowManager: setLockTaskAuth: task=Task{2930657 #188 type=standard
A=10456:com.mauriciocdz07.tecbizexpoapp} mLockTaskAuth=LOCK_TASK_AUTH_PINNABLE
07-22 12:00:52.891  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119892, oldVsyncId=1119892, layerName=40967a5 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.903  3355  3635 W RecentsModel: getRunningTask   taskInfo=TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true 
baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000
cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}
topActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} origActivity=null
realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} numActivities=1
lastActiveTime=11916320 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1   
minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@462960a} topActivityType=1
pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1
displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{d6d1e7b com.mauriciocdz07.tecbizexpoapp.MainActivity}     
launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true
isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo {
topActivityInSizeCompat=false topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation=
falseisLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false     
topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1
topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2400
isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo {
cameraCompatControlState=hidden freeformCameraCompatMode=inactive}} isImmersive=false mTopActivityRequestOrientation=-1
mBehindAppLockPkg=null mOriginatingUid=0 isEmbedded=false shouldBeVisible=true mIsCastMode=false mTopActivityMediaSize=null       
mTopActivityRecordName=ActivityRecord{e292cac u0 com.mauriciocdz07.tecbizexpoapp/.MainActivity t188} mTopActivityOrientation=-1}  
07-22 12:00:52.905  3295  3295 I FocusedNotifPromptContr: onTopActivityMayChanged: topActivity=com.mauriciocdz07.tecbizexpoapp    
07-22 12:00:52.905  3295  3295 I PromptViewAnimState: topActivityMayChanged: topActivity=com.google.android.permissioncontroller  
--> com.mauriciocdz07.tecbizexpoapp
07-22 12:00:52.905  3295  3295 D KeyguardEditorHelper: onTopActivityMayChanged,
topActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}; mState=IDEL
07-22 12:00:52.906  3355  3635 E ActivityManagerWrapper:  mainTaskId=188   userId=0   windowMode=1   baseIntent=Intent {
act=android.intent.action.MAIN flag=270532608
cmp=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} }
07-22 12:00:52.907  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119923, oldVsyncId=1119892, layerName=40967a5        
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:52.911  3355  3635 W RecentsModel: getRunningTask   taskInfo=TaskInfo{userId=0 taskId=188 displayId=0 isRunning=true  
baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000
cmp=com.mauriciocdz07.tecbizexpoapp/.MainActivity }
baseActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity}
topActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} origActivity=null
realActivity=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} numActivities=1
lastActiveTime=11916328 supportsSplitScreenMultiWindow=true supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1   
minHeight=-1 defaultMinSize=200 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@9c994f1} topActivityType=1
pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1
displayCutoutSafeInsets=Rect(0, 94 - 0, 0) topActivityInfo=ActivityInfo{99c08d6 com.mauriciocdz07.tecbizexpoapp.MainActivity}     
launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true
isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false appCompatTaskInfo=AppCompatTaskInfo {
topActivityInSizeCompat=false topActivityInMiuiSizeCompat=false topActivityEligibleForLetterboxEducation=
falseisLetterboxEducationEnabled= false isLetterboxDoubleTapEnabled= false topActivityEligibleForUserAspectRatioButton= false     
topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1
topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=1080 topActivityLetterboxHeight=2400
isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false cameraCompatTaskInfo=CameraCompatTaskInfo {
cameraCompatControlState=hidden freeformCameraCompatMode=inactive}} isImmersive=false mTopActivityRequestOrientation=-1
mBehindAppLockPkg=null mOriginatingUid=0 isEmbedded=false shouldBeVisible=true mIsCastMode=false mTopActivityMediaSize=null       
mTopActivityRecordName=ActivityRecord{e292cac u0 com.mauriciocdz07.tecbizexpoapp/.MainActivity t188} mTopActivityOrientation=-1}  
07-22 12:00:52.913  3355  3635 E ActivityManagerWrapper:  mainTaskId=188   userId=0   windowMode=1   baseIntent=Intent {
act=android.intent.action.MAIN flag=270532608
cmp=ComponentInfo{com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity} }
07-22 12:00:52.962  2313  6014 I ActivityManager: Cancel FGS notification userId: 0 definingUid: 10335 packageName: 
com.life360.android.safetymapd processName:com.life360.android.safetymapd:service ChannelId:Location updates
foregroundNotiWhen:1753196452312 Notiflags:98
07-22 12:00:52.977  3168  3841 D BtGatt.ScanManager: configureRegularScanParams() - queue=3
07-22 12:00:52.979  2313  2313 I NotificationService: Cancel FGS notification CallingUid: 10335 CallingPid: 11701 Pkg:
com.life360.android.safetymapd Id:2000 Tag:null UserId:0 MustHaveFlags:0 MustNotHaveFlags:0 Reason:8
foregroundNotiWhen:1753196452312 foregroundNotiFlags:98 foregroundNotiChannelId:Location updates
07-22 12:00:52.987  3295  3295 D HideDeletedFocusController: onNotificationRemoved 8 
0|com.life360.android.safetymapd|2000|null|10335
07-22 12:00:52.989  3295  3295 D MiuiBubbleManager: onEntryRemoved:
com.android.systemui.statusbar.notification.collection.NotificationEntry@8777af8
07-22 12:00:53.018 16154 16203 I HeavyPackageIdentify: splitMode: false, splitPackages: null, foregroundPkg: 
com.mauriciocdz07.tecbizexpoapp
07-22 12:00:53.025  3295  3295 D NotificationHeaderExpandController: onExpansionChanged: progress =  0.0
07-22 12:00:53.041  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1119948, oldVsyncId=1119948, 
layerName=com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1818
07-22 12:00:53.058  1351  1351 E MI-SF   : MiSurfaceFlingerImpl.cpp.vsyncId=1120015, oldVsyncId=1120015, layerName=40967a5 
com.mauriciocdz07.tecbizexpoapp/com.mauriciocdz07.tecbizexpoapp.MainActivity#1817
07-22 12:00:54.364 12362 12391 I ReactNativeJS: ­ƒöö NotificationIcon recebeu foco - atualizando contador
07-22 12:00:54.555  3253  3253 D QtiCarrierConfigHelper: WARNING, no carrier configs on phone Id: 1
07-22 12:00:54.559  2313  2313 D UiModeManager: updateConfigurationLocked: mDockState=0; mCarMode=false; mCar=false;
mPowerSave=false; mOverrideNightModeOn=false; mOverrideNightModeOff=false; mComputedNightMode=false; mNightModeValue=0; uiMode=17 
07-22 12:00:55.826 12362 12391 I ReactNativeJS: ÔÜá´©Å Token n├úo encontrado - pulando sincroniza├º├úo
07-22 12:00:56.368 12362 12391 I ReactNativeJS: ­ƒöö NotificationIcon recebeu foco - atualizando contador
07-22 12:00:56.477  3253  3253 D QtiCarrierConfigHelper: WARNING, no carrier configs on phone Id: 1
07-22 12:00:56.480  2313  2313 D UiModeManager: updateConfigurationLocked: mDockState=0; mCarMode=false; mCar=false;
mPowerSave=false; mOverrideNightModeOn=false; mOverrideNightModeOff=false; mComputedNightMode=false; mNightModeValue=0; uiMode=17 
07-22 12:00:57.502  1204  1204 I sensors-hal: set_config:60, sample_period_ns is adjusted to 0 based on min/max delay_ns
07-22 12:00:58.372 12362 12391 I ReactNativeJS: ­ƒöö NotificationIcon recebeu foco - atualizando contador
07-22 12:00:58.828 12362 12391 I ReactNativeJS: ÔÜá´©Å Token n├úo encontrado - pulando sincroniza├º├úo