import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, StatusBar, Platform } from 'react-native';
import Cores from '../constants/Cores';

interface SafeHeaderProps {
  title: string;
  showBackButton?: boolean;
  onBackPress?: () => void;
  rightComponent?: React.ReactNode;
}

const SafeHeader: React.FC<SafeHeaderProps> = ({
  title,
  showBackButton = true,
  onBackPress,
  rightComponent
}) => {
  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      console.warn('SafeHeader: onBackPress não fornecido e navigation não disponível');
    }
  };

  return (
    <>
      <StatusBar backgroundColor={Cores.fundoHeader} barStyle="light-content" />
      <View style={styles.header}>
        {showBackButton ? (
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.placeholder} />
        )}
        
        <Text style={styles.headerTitle}>{title}</Text>
        
        {rightComponent ? (
          <View style={styles.rightComponent}>
            {rightComponent}
          </View>
        ) : (
          <View style={styles.placeholder} />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: Platform.OS === 'ios' ? 50 : 40, // Mais espaço no iOS para status bar
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40,
    height: 40,
  },
  rightComponent: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SafeHeader;
