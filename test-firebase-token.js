#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando configurações Firebase para tokens reais...\n');

const checks = [
  {
    name: 'Firebase SDK importado no firebaseProductionService',
    check: () => {
      try {
        const content = fs.readFileSync('src/services/firebaseProductionService.ts', 'utf8');
        return content.includes('import { initializeApp, FirebaseApp }') && 
               content.includes('import { getMessaging, getToken');
      } catch {
        return false;
      }
    },
    fix: 'Firebase SDK já foi adicionado ao serviço'
  },
  {
    name: 'Método initializeFirebaseSDK existe',
    check: () => {
      try {
        const content = fs.readFileSync('src/services/firebaseProductionService.ts', 'utf8');
        return content.includes('private async initializeFirebaseSDK()');
      } catch {
        return false;
      }
    },
    fix: '<PERSON>étodo initializeFirebaseSDK já foi adicionado'
  },
  {
    name: 'Estratégia FCM implementada no getToken',
    check: () => {
      try {
        const content = fs.readFileSync('src/services/firebaseProductionService.ts', 'utf8');
        return content.includes('Token FCM real via Firebase SDK') && 
               content.includes('await getToken(this.messaging');
      } catch {
        return false;
      }
    },
    fix: 'Estratégia FCM já foi implementada'
  },
  {
    name: 'Configuração Firebase correta',
    check: () => {
      try {
        const config = fs.readFileSync('src/config/firebase.ts', 'utf8');
        return config.includes('tecbizappass') && 
               config.includes('608372388905') &&
               config.includes('AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk');
      } catch {
        return false;
      }
    },
    fix: 'Configuração Firebase está correta'
  },
  {
    name: 'google-services.json existe e está correto',
    check: () => {
      try {
        const googleServices = JSON.parse(fs.readFileSync('google-services.json', 'utf8'));
        return googleServices.project_info.project_id === 'tecbizappass' &&
               googleServices.client[0].client_info.android_client_info.package_name === 'com.tecbiz.tecbizassociadospush';
      } catch {
        return false;
      }
    },
    fix: 'google-services.json está correto'
  },
  {
    name: 'Validação de token real atualizada',
    check: () => {
      try {
        const config = fs.readFileSync('src/config/production.ts', 'utf8');
        return config.includes('token.length > 140') && 
               config.includes('!token.includes(\'simulator\')');
      } catch {
        return false;
      }
    },
    fix: 'Validação de token real foi atualizada'
  },
  {
    name: 'Firebase dependency instalada',
    check: () => {
      try {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        return packageJson.dependencies && packageJson.dependencies.firebase;
      } catch {
        return false;
      }
    },
    fix: 'Execute: npm install firebase'
  }
];

let allPassed = true;

checks.forEach((check, index) => {
  const passed = check.check();
  const status = passed ? '✅' : '❌';
  console.log(`${status} ${check.name}`);
  
  if (!passed) {
    console.log(`   💡 Solução: ${check.fix}`);
    allPassed = false;
  }
});

console.log('\n' + '='.repeat(60));

if (allPassed) {
  console.log('🎉 TODAS AS CONFIGURAÇÕES ESTÃO CORRETAS!');
  console.log('\n🚀 PRÓXIMOS PASSOS:');
  console.log('1. Faça um novo build: eas build --platform android --profile preview --clear-cache');
  console.log('2. Instale o APK em um dispositivo físico');
  console.log('3. Abra o app e verifique se o token é real');
  console.log('4. O token deve começar com "ExponentPushToken[" ou ter mais de 140 caracteres');
  
  console.log('\n🔍 COMO VERIFICAR SE FUNCIONOU:');
  console.log('- Abra o app no dispositivo físico');
  console.log('- Vá para a tela de debug/configurações');
  console.log('- Verifique se o token é marcado como "REAL"');
  console.log('- O token deve ser diferente dos tokens de desenvolvimento');
  
  console.log('\n📋 MELHORIAS IMPLEMENTADAS:');
  console.log('- ✅ Firebase SDK inicializado corretamente');
  console.log('- ✅ Tentativa de token FCM real primeiro');
  console.log('- ✅ Fallback para Expo token se FCM falhar');
  console.log('- ✅ Validação melhorada de tokens reais');
  console.log('- ✅ Configurações Firebase corretas');
} else {
  console.log('⚠️ ALGUMAS CONFIGURAÇÕES PRECISAM SER CORRIGIDAS');
  console.log('\n🔧 Execute as correções indicadas acima');
}

console.log('\n💡 DICA IMPORTANTE:');
console.log('Tokens FCM reais só funcionam em:');
console.log('- Dispositivos físicos (não simuladores)');
console.log('- Builds de produção (EAS build)');
console.log('- Apps instalados via APK (não Expo Go)');
console.log('\n🎯 Com essas correções, você deve obter tokens reais!');
