# Correções para Android 8.0 - Problemas de Conexão HTTPS

## Problema Identificado

O aplicativo estava falhando no Android 8.0 devido ao uso de `AbortSignal.timeout()` que não está disponível nesta versão do Android. O erro específico era:

```
TypeError: undefined is not a function
at ?anon_0_ (address at index.android.bundle:1:865674)
```

## ⚠️ ATUALIZAÇÃO - Correção de Loop Infinito

Durante os testes, identificamos que o interceptador global de fetch estava causando um loop infinito com o Metro bundler. As correções foram simplificadas para evitar esse problema.

## Correções Implementadas

### 1. Polyfills para Android 8.0 (`src/utils/polyfills.ts`)

- Criado polyfill customizado para `AbortController` e `AbortSignal`
- Implementado `AbortSignal.timeout()` usando `setTimeout` + `AbortController`
- Aplicação automática dos polyfills quando Android 8.0 é detectado

### 2. Correção do NetworkConfig (`src/utils/networkConfig.ts`) - SIMPLIFICADO

**ANTES:**
```typescript
signal: AbortSignal.timeout(config.timeout), // ❌ Não funciona no Android 8.0
```

**DEPOIS (Versão Simplificada):**
```typescript
// Para Android 8.0, usar AbortController manual
if (isAndroid8OrLower()) {
  const controller = createCompatibleAbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, 90000);

  const enhancedOptions = {
    ...options,
    headers: { /* headers específicos */ },
    signal: controller.signal,
  };

  return fetch(url, enhancedOptions);
}

// Para versões superiores, usar comportamento padrão
return fetch(url, options);
```

### 3. Substituição de fetch() direto por fetchWithTimeout()

Todas as chamadas diretas de `fetch()` no arquivo `src/services/api.ts` foram substituídas por `fetchWithTimeout()` para garantir compatibilidade:

- `getExtrato()` - ✅ Corrigido
- `getAutorizacoesPendentes()` - ✅ Corrigido  
- `confirmarAutorizacao()` - ✅ Corrigido
- `testConnectivity()` - ✅ Corrigido
- `esqueceuSenha()` - ✅ Corrigido
- `getUsuario()` - ✅ Corrigido
- `alterarSenha()` - ✅ Corrigido
- `listarEstados()` - ✅ Corrigido
- `listarCidades()` - ✅ Corrigido
- `listarSegmentos()` - ✅ Corrigido
- `listarEstabelecimentos()` - ✅ Corrigido
- `bloquearCartao()` - ✅ Corrigido
- `listarFiliais()` - ✅ Corrigido

### 4. Inicialização dos Polyfills (`App.tsx`)

```typescript
useEffect(() => {
  // Aplicar polyfills para compatibilidade com Android 8.0
  applyPolyfills();
  
  // Inicializar configurações de rede
  initializeNetworkConfig();
  
  // ... resto da inicialização
}, []);
```

### 5. Testes de Compatibilidade (`src/utils/android8TestUtils.ts`)

Criado sistema de testes para verificar:
- Funcionamento do `AbortController`
- Fetch com timeout
- Conexão HTTPS com servidor TecBiz
- Informações detalhadas do dispositivo

### 6. Configurações SSL/TLS Mantidas

As configurações SSL/TLS no `MainApplication.kt` foram mantidas:
- TLS 1.2 habilitado para Android 8.0
- TrustManager configurado corretamente
- Network Security Config permitindo conexões com TecBiz

## Verificação SSL

O teste SSL confirma que o servidor da TecBiz suporta Android 8.0:
```
Android 8.0: RSA 2048 (SHA256) TLS 1.2 > http/1.1 TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384 ECDH x25519 FS
```

## Como Testar

1. **Build do aplicativo:**
   ```bash
   npx expo run:android
   ```

2. **Verificar logs no Android 8.0:**
   - Procurar por "📱 Android 8.0 detectado"
   - Verificar se polyfills foram aplicados
   - Confirmar que testes de compatibilidade passaram

3. **Testar login:**
   - Inserir credenciais válidas
   - Verificar se requisição HTTPS funciona
   - Confirmar que não há mais erro "undefined is not a function"

## Logs Esperados no Android 8.0

```
📱 Android 8.0 detectado - executando testes de compatibilidade...
🔧 Aplicando polyfills para Android 8.0...
✅ AbortController funcionando corretamente
✅ Fetch funcionando - tempo: XXXms
✅ Conexão OK - Status: 200, Tempo: XXXms
📱 Usando fetch compatível com Android 8.0
✅ Requisição bem-sucedida no Android 8.0
```

## Arquivos Modificados

- `src/utils/polyfills.ts` - **NOVO**
- `src/utils/networkConfig.ts` - **MODIFICADO**
- `src/services/api.ts` - **MODIFICADO**
- `src/utils/android8TestUtils.ts` - **NOVO**
- `src/screens/LoginScreen.tsx` - **MODIFICADO**
- `App.tsx` - **MODIFICADO**
- `ANDROID_8_FIXES.md` - **NOVO**

## Compatibilidade

- ✅ Android 8.0 (API 26) e inferior
- ✅ Android 8.1+ (mantém funcionalidade nativa)
- ✅ iOS (não afetado)
- ✅ Versões superiores do Android (fallback para APIs nativas)

## Próximos Passos

1. Testar em dispositivo Android 8.0 real
2. Verificar se login funciona corretamente
3. Confirmar que push notifications funcionam
4. Validar outras funcionalidades do app
