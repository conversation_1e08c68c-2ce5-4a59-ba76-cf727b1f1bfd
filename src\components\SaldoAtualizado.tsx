import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions } from 'react-native';
import Cores from '../constants/Cores';
import { useAppContext } from '../context/AppContext';
import {
  isSmallScreen,
  isMediumSmallScreen,
  getResponsiveTextSize,
  isProblematicResolution,
  getProblematicResolutionAdjustments
} from '../utils/responsive';

interface SaldoAtualizadoProps {
  onPress?: () => void;
  style?: any;
  forceRefresh?: boolean; // Para forçar atualização quando necessário
}

const SaldoAtualizado: React.FC<SaldoAtualizadoProps> = ({
  onPress,
  style,
  forceRefresh = false
}) => {
  // Volta para AppContext para evitar loops
  const { getSinteticoData, atualizarDadosUsuario } = useAppContext();
  const sinteticoData = getSinteticoData();

  // Responsividade melhorada
  const screenWidth = Dimensions.get('window').width;
  const isSmall = isSmallScreen();
  const isMediumSmall = isMediumSmallScreen();
  const isProblematic = isProblematicResolution();
  const adjustments = getProblematicResolutionAdjustments();

  // Função para atualizar saldo manualmente quando necessário
  const atualizarSaldo = async () => {
    console.log('🔄 Atualizando saldo manualmente...');
    // Tenta atualizar sem parâmetros (usa storage automaticamente)
    await atualizarDadosUsuario();
  };

  // Se forceRefresh for true, atualizar uma vez
  React.useEffect(() => {
    if (forceRefresh) {
      atualizarSaldo();
    }
  }, [forceRefresh]);

  const formatarValor = (valor: string): string => {
    if (!valor) return 'R$ 0,00';

    // Remove espaços e caracteres especiais, mantém apenas números, vírgula e ponto
    let numeroLimpo = valor.toString().trim();

    // Se já tem R$, remove para reprocessar
    numeroLimpo = numeroLimpo.replace(/R\$\s*/g, '');

    // Remove pontos que são separadores de milhares (mantém apenas o último ponto se for decimal)
    // Exemplo: "3.565,25" ou "13.430,68"
    const temVirgula = numeroLimpo.includes(',');

    if (temVirgula) {
      // Se tem vírgula, ela é o separador decimal
      // Remove todos os pontos (separadores de milhares)
      const partes = numeroLimpo.split(',');
      const parteInteira = partes[0].replace(/\./g, ''); // Remove pontos da parte inteira
      const parteDecimal = partes[1] || '00';
      numeroLimpo = `${parteInteira}.${parteDecimal}`; // Usa ponto como separador decimal para parseFloat
    } else {
      // Se não tem vírgula, verifica se o último ponto é decimal ou separador de milhares
      const pontos = numeroLimpo.split('.');
      if (pontos.length > 2) {
        // Múltiplos pontos = separadores de milhares
        numeroLimpo = numeroLimpo.replace(/\./g, '');
      } else if (pontos.length === 2 && pontos[1].length <= 2) {
        // Um ponto com 1-2 dígitos depois = separador decimal
        // Mantém como está
      } else {
        // Um ponto com mais de 2 dígitos = separador de milhares
        numeroLimpo = numeroLimpo.replace(/\./g, '');
      }
    }

    // Converte para número
    const numero = parseFloat(numeroLimpo);
    if (isNaN(numero)) {
      return 'R$ 0,00';
    }

    // Formata no padrão brasileiro
    return `R$ ${numero.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };



  // Verifica se tem dados de saldo
  if (!sinteticoData?.sintetico) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.indisponivelText}>Saldo indisponível</Text>
      </View>
    );
  }

  const saldoMensal = sinteticoData.sintetico.saldo_mensal || '0';
  const saldoTotal = sinteticoData.sintetico.saldo_total || '0';
  const temSaldoTotal = saldoTotal && saldoTotal !== '0';

  return (
    <TouchableOpacity 
      style={[styles.container, style]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.header}>
        <Text style={[
          styles.titulo,
          {
            fontSize: getResponsiveTextSize(isSmall ? 14 : 18) + (adjustments.fontSize || 0)
          }
        ]}>
          Saldo Disponível
        </Text>
      </View>

      {temSaldoTotal ? (
        // Dois cartões lado a lado
        <View style={styles.doisCartoes}>
          <View style={[styles.cartaoSaldo, adjustments.flexAdjustments]}>
            <Text style={[
              styles.labelSaldo,
              {
                fontSize: getResponsiveTextSize(isSmall ? 10 : isMediumSmall ? 12 : 14) + (adjustments.fontSize || 0)
              }
            ]}>
              Mensal
            </Text>
            <Text
              style={[
                styles.valorSaldo,
                {
                  fontSize: getResponsiveTextSize(isSmall ? 14 : isMediumSmall ? 16 : 18) + (adjustments.fontSize || 0)
                }
              ]}
              numberOfLines={1}
              adjustsFontSizeToFit
              minimumFontScale={0.7}
            >
              {formatarValor(saldoMensal)}
            </Text>
          </View>
          <View style={[styles.cartaoSaldo, adjustments.flexAdjustments]}>
            <Text style={[
              styles.labelSaldo,
              {
                fontSize: getResponsiveTextSize(isSmall ? 10 : isMediumSmall ? 12 : 14) + (adjustments.fontSize || 0)
              }
            ]}>
              Total
            </Text>
            <Text
              style={[
                styles.valorSaldo,
                {
                  fontSize: getResponsiveTextSize(isSmall ? 14 : isMediumSmall ? 16 : 18) + (adjustments.fontSize || 0)
                }
              ]}
              numberOfLines={1}
              adjustsFontSizeToFit
              minimumFontScale={0.7}
            >
              {formatarValor(saldoTotal)}
            </Text>
          </View>
        </View>
      ) : (
        // Um cartão centralizado
        <View style={styles.cartaoSaldoUnico}>
          <Text style={styles.labelSaldoUnico}>Saldo Mensal</Text>
          <Text style={styles.valorSaldoUnico}>
            {formatarValor(saldoMensal)}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 10,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    marginHorizontal: 10,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    marginHorizontal: 10,
  },
  titulo: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
  },

  doisCartoes: {
    flexDirection: 'row',
    gap: 15,
  },
  cartaoSaldo: {
    flex: 1,
    backgroundColor: Cores.primaria + '10',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  cartaoSaldoUnico: {
    backgroundColor: Cores.primaria + '10',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  labelSaldo: {
    fontSize: 14,
    color: Cores.textoMedio,
    marginBottom: 5,
  },
  labelSaldoUnico: {
    fontSize: 16,
    color: Cores.textoMedio,
    marginBottom: 8,
  },
  valorSaldo: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.primaria,
  },
  valorSaldoUnico: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Cores.primaria,
  },
  indisponivelText: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default SaldoAtualizado;
