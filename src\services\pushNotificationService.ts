// Serviço COMPLETO de Push Notifications - LIMPO E FUNCIONAL
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { AppState } from 'react-native';
import { nativeStorage } from '../utils/nativeStorage';
import { FIREBASE_CONFIG } from '../config/firebase';

// Event emitter para notificações
class NotificationEventEmitter {
  private listeners: { [key: string]: Function[] } = {};

  on(event: string, callback: Function) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: Function) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  emit(event: string, data?: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }
}

export const notificationEvents = new NotificationEventEmitter();

// FORÇAR LOGS SEMPRE (para debug)
const log = (message: string, ...args: any[]) => {
  console.log(`[PUSH-SERVICE] ${message}`, ...args);
};

// Interface para notificações
export interface PushNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: number;
  read: boolean;
  type?: 'local' | 'remote' | 'database';
}

class PushNotificationService {
  private isInitialized = false;
  private currentToken: string | null = null;
  private notifications: PushNotification[] = [];
  private listeners: any[] = [];

  /**
   * Inicializar serviço completo de push notifications
   */
  async initialize(): Promise<string | null> {
    try {
      log('🚀 Inicializando Push Notification Service...');
      log('📱 Dispositivo físico:', Device.isDevice);
      log('🏭 Ambiente:', Constants.executionEnvironment);

      // Configurar comportamento das notificações
      await this.setupNotificationHandler();

      // Verificar se é dispositivo físico
      if (!Device.isDevice) {
        log('⚠️ SIMULADOR - Push notifications limitadas');
        return this.initializeFallback();
      }

      // Solicitar permissões
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        log('❌ PERMISSÃO NEGADA');
        return this.initializeFallback();
      }

      // Obter token
      const token = await this.getPushToken();
      if (token) {
        this.currentToken = token;
        this.isInitialized = true;

        // Salvar token
        await nativeStorage.setItem('push_token', token);
        await nativeStorage.setItem('token_type', 'expo');

        // Salvar token localmente
        await this.saveTokenLocally(token);

        // Configurar listeners
        this.setupListeners();

        // Carregar notificações salvas
        await this.loadStoredNotifications();

        // ✨ NOVO: Verificar se o app foi aberto por uma notificação
        await this.checkInitialNotification();

        // Verificar notificações pendentes no servidor
        this.checkPendingNotifications();

        log('🎉 SUCESSO! Token:', token.substring(0, 50) + '...');
        return token;
      } else {
        log('💀 Falha ao obter token');
        return this.initializeFallback();
      }

    } catch (error) {
      log('❌ ERRO na inicialização:', error);
      return this.initializeFallback();
    }
  }

  /**
   * Configurar handler de notificações
   */
  private async setupNotificationHandler(): Promise<void> {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    });
    log('📱 Handler configurado');
  }

  /**
   * Solicitar permissões
   */
  private async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      const granted = finalStatus === 'granted';
      log('🔐 Permissões:', granted ? 'CONCEDIDAS' : 'NEGADAS');
      return granted;
    } catch (error) {
      log('❌ Erro ao solicitar permissões:', error);
      return false;
    }
  }

  /**
   * Obter token com múltiplas tentativas
   */
  private async getPushToken(): Promise<string | null> {
    const maxAttempts = 5;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        log(`🔄 Tentativa ${attempt}/${maxAttempts}...`);

        const projectId = Constants.expoConfig?.extra?.eas?.projectId || 
                         Constants.expoConfig?.extra?.expoProjectId || 
                         FIREBASE_CONFIG.projectId;

        const tokenData = await Notifications.getExpoPushTokenAsync({
          projectId: projectId,
        });

        const token = tokenData?.data;
        if (token) {
          log(`✅ TOKEN OBTIDO na tentativa ${attempt}!`);
          return token;
        }

        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        log(`❌ Erro na tentativa ${attempt}:`, error);
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    return null;
  }

  /**
   * Inicializar fallback
   */
  private async initializeFallback(): Promise<string | null> {
    log('🔄 Modo FALLBACK...');
    
    const fallbackToken = `tecbiz_fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentToken = fallbackToken;
    this.isInitialized = true;
    
    await nativeStorage.setItem('push_token', fallbackToken);
    await nativeStorage.setItem('token_type', 'fallback');
    
    // Ainda configurar listeners para notificações locais
    this.setupListeners();
    await this.loadStoredNotifications();

    // ✨ NOVO: Verificar se o app foi aberto por uma notificação (mesmo no fallback)
    await this.checkInitialNotification();
    
    log('✅ Fallback inicializado:', fallbackToken);
    return fallbackToken;
  }

  /**
   * Configurar listeners de notificação
   */
  private setupListeners(): void {
    log('📨 Configurando listeners...');

    // Listener para notificações recebidas (app em foreground)
    const receivedListener = Notifications.addNotificationReceivedListener(notification => {
      log('📨 NOTIFICAÇÃO RECEBIDA (foreground)!');
      log('📋 Título:', notification.request.content.title);
      log('📋 Corpo:', notification.request.content.body);
      log('📋 Dados:', notification.request.content.data);
      log('📋 ID:', notification.request.identifier);

      const pushNotification: PushNotification = {
        id: notification.request.identifier,
        title: notification.request.content.title || 'Notificação',
        body: notification.request.content.body || '',
        data: notification.request.content.data || {},
        timestamp: Date.now(),
        read: false,
        type: 'remote'
      };

      log('💾 Adicionando notificação ao storage local (foreground)...');
      this.addNotification(pushNotification);
      log('✅ Notificação adicionada com sucesso!');
    });

    // Listener para quando app volta do background (captura notificações perdidas)
    const backgroundListener = Notifications.addNotificationReceivedListener(notification => {
      log('📨 NOTIFICAÇÃO RECEBIDA (background/perdida)!');

      // Verificar se já existe para evitar duplicação
      const exists = this.notifications.some(n => n.id === notification.request.identifier);
      if (!exists) {
        const pushNotification: PushNotification = {
          id: notification.request.identifier,
          title: notification.request.content.title || 'Notificação',
          body: notification.request.content.body || '',
          data: notification.request.content.data || {},
          timestamp: Date.now(),
          read: false,
          type: 'remote'
        };

        log('💾 Adicionando notificação perdida ao storage...');
        this.addNotification(pushNotification);
        log('✅ Notificação perdida recuperada!');
      } else {
        log('⚠️ Notificação já existe, ignorando duplicação');
      }
    });

    // Listener para quando usuário toca na notificação
    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      log('👆 NOTIFICAÇÃO TOCADA!');
      log('📋 Título:', response.notification.request.content.title);
      log('📋 Dados:', response.notification.request.content.data);

      const notificationId = response.notification.request.identifier;
      log('🔍 Processando notificação tocada:', notificationId);

      // Adicionar ao storage local se não existir (caso tenha chegado em background)
      const pushNotification: PushNotification = {
        id: response.notification.request.identifier,
        title: response.notification.request.content.title || 'Notificação',
        body: response.notification.request.content.body || '',
        data: response.notification.request.content.data || {},
        timestamp: Date.now(),
        read: false, // Manter como não lida para aparecer no contador
        type: 'remote'
      };

      // Verificar se já existe
      const exists = this.notifications.some(n => n.id === pushNotification.id);
      if (!exists) {
        log('💾 Adicionando notificação tocada ao storage...');
        this.addNotification(pushNotification);

        // Emitir evento para atualizar contador
        notificationEvents.emit('notificationAdded', {
          notification: pushNotification,
          unreadCount: this.getUnreadCount()
        });
      }

      // Emitir evento especial para navegação automática
      log('🧭 Emitindo evento para navegar para tela de notificações...');
      notificationEvents.emit('notificationTapped', {
        notification: pushNotification,
        shouldNavigate: true
      });
    });

    // Listener para mudanças no estado do app
    const appStateListener = AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        log('📱 App ativo - verificando notificações pendentes...');

        // ✨ NOVO: Sempre verificar se foi aberto por notificação
        this.checkInitialNotification();

        this.checkPendingNotifications();
        // Verificar notificações que chegaram em background
        this.checkBackgroundNotifications();
      }
    });

    this.listeners = [receivedListener, backgroundListener, responseListener, appStateListener];
    log('✅ Listeners configurados');
  }

  /**
   * ✨ NOVO: Verificar se o app foi aberto por uma notificação
   * Esta função captura notificações que abriram o app quando estava fechado
   */
  private async checkInitialNotification(): Promise<void> {
    try {
      log('🚀 Verificando se app foi aberto por notificação...');

      // ✨ NOVO: Aguardar um pouco para garantir que dados do usuário estejam carregados
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Obter a última resposta de notificação (se o app foi aberto por uma)
      const lastNotificationResponse = await Notifications.getLastNotificationResponseAsync();

      if (lastNotificationResponse) {
        log('🎯 APP FOI ABERTO POR NOTIFICAÇÃO!');
        log('📋 Título:', lastNotificationResponse.notification.request.content.title);
        log('📋 Corpo:', lastNotificationResponse.notification.request.content.body);
        log('📋 ID:', lastNotificationResponse.notification.request.identifier);
        log('📋 Data:', lastNotificationResponse.notification.request.content.data);

        // ✨ NOVO: Verificar se temos dados do usuário antes de processar
        const userKey = await this.getUserStorageKey();
        log('🔑 Chave do usuário para notificação inicial:', userKey);

        const pushNotification: PushNotification = {
          id: lastNotificationResponse.notification.request.identifier,
          title: lastNotificationResponse.notification.request.content.title || 'Notificação',
          body: lastNotificationResponse.notification.request.content.body || '',
          data: lastNotificationResponse.notification.request.content.data || {},
          timestamp: Date.now(),
          read: false, // Marcar como não lida para aparecer no contador
          type: 'remote'
        };

        // Verificar se já existe para evitar duplicação
        const exists = this.notifications.some(n => n.id === pushNotification.id);
        if (!exists) {
          log('💾 Adicionando notificação que abriu o app...');
          await this.addNotification(pushNotification);
          log('✅ Notificação que abriu o app foi registrada!');

          // Emitir evento para atualizar contador
          notificationEvents.emit('notificationAdded', {
            notification: pushNotification,
            unreadCount: this.getUnreadCount()
          });
        } else {
          log('ℹ️ Notificação já existe no storage - ID:', pushNotification.id);

          // Mesmo que já exista, emitir evento para garantir que UI seja atualizada
          notificationEvents.emit('notificationExists', {
            notification: pushNotification,
            unreadCount: this.getUnreadCount(),
            totalCount: this.notifications.length
          });
        }
      } else {
        log('ℹ️ App não foi aberto por notificação');
      }

    } catch (error) {
      log('❌ Erro ao verificar notificação inicial:', error);
    }
  }

  /**
   * Verificar notificações que chegaram em background
   */
  private async checkBackgroundNotifications(): Promise<void> {
    try {
      log('🔍 Verificando notificações em background...');

      // Obter notificações apresentadas (que chegaram mas podem não ter sido clicadas)
      const presentedNotifications = await Notifications.getPresentedNotificationsAsync();

      log('📋 Notificações apresentadas encontradas:', presentedNotifications.length);

      for (const notification of presentedNotifications) {
        const notificationId = notification.request.identifier;
        const exists = this.notifications.some(n => n.id === notificationId);

        if (!exists) {
          log('📨 Recuperando notificação não clicada:', notification.request.content.title);
          log('🆔 ID da notificação:', notificationId);

          const pushNotification: PushNotification = {
            id: notificationId,
            title: notification.request.content.title || 'Notificação',
            body: notification.request.content.body || '',
            data: notification.request.content.data || {},
            timestamp: Date.now(),
            read: false,
            type: 'remote'
          };

          await this.addNotification(pushNotification);
          log('✅ Notificação recuperada e adicionada!');

          // ✨ NOVO: Emitir evento específico para notificações recuperadas
          notificationEvents.emit('notificationRecovered', {
            notification: pushNotification,
            unreadCount: this.getUnreadCount(),
            totalCount: this.notifications.length
          });
        } else {
          log('ℹ️ Notificação já existe no storage - ID:', notificationId);
          log('📊 Total atual de notificações:', this.notifications.length);
        }
      }

      // Se recuperou alguma notificação, forçar atualização da UI
      if (presentedNotifications.length > 0) {
        log('🔄 Forçando atualização da UI após recuperar notificações...');

        // Emitir evento geral de atualização
        notificationEvents.emit('notificationsUpdated', {
          totalCount: this.notifications.length,
          unreadCount: this.getUnreadCount(),
          recoveredCount: presentedNotifications.length
        });
      }

      // Limpar notificações apresentadas para evitar acúmulo
      await Notifications.dismissAllNotificationsAsync();

    } catch (error) {
      log('❌ Erro ao verificar notificações em background:', error);
    }
  }

  /**
   * Salvar token apenas localmente (sem servidor)
   */
  private async saveTokenLocally(token: string): Promise<void> {
    try {
      log('💾 Salvando token localmente...');

      // Salvar token com informações básicas
      const tokenInfo = {
        token: token,
        saved_at: Date.now(),
        device_info: {
          platform: 'android',
          app_version: '1.0.0'
        }
      };

      nativeStorage.setObject('push_token_info', tokenInfo);
      log('✅ Token salvo localmente com sucesso');

    } catch (error) {
      log('❌ Erro ao salvar token localmente:', error);
    }
  }

  /**
   * Verificar notificações pendentes (APENAS LOCAL - SEM SERVIDOR)
   * Agora funciona apenas com push direto + armazenamento local
   */
  private async checkPendingNotifications(): Promise<void> {
    // Sistema simplificado - apenas carrega notificações locais
    try {
      log('🔍 Carregando notificações locais...');

      // Carregar notificações do storage local
      const stored = await nativeStorage.getItem('push_notifications');
      if (stored) {
        this.notifications = JSON.parse(stored);
      }

      log('✅ Notificações locais carregadas:', this.notifications.length, 'total');
      log('📊 Contador não lidas:', this.getUnreadCount());

    } catch (error) {
      log('❌ Erro ao carregar notificações locais:', error);
    }
  }

  /**
   * Adicionar notificação
   */
  private async addNotification(notification: PushNotification): Promise<void> {
    // Verificar duplicação
    const exists = this.notifications.some(n => n.id === notification.id);
    if (exists) {
      log('⚠️ Notificação duplicada ignorada:', notification.id);
      return;
    }

    // Adicionar à lista
    this.notifications.unshift(notification);

    // Manter apenas as últimas 100 notificações
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }

    // Salvar no storage
    await this.saveNotifications();

    // Emitir evento para atualizar UI automaticamente
    notificationEvents.emit('notificationAdded', {
      notification,
      unreadCount: this.getUnreadCount(),
      totalCount: this.notifications.length
    });

    log('✅ Notificação adicionada. Total:', this.notifications.length);
    log('📊 Não lidas:', this.getUnreadCount());

    // Salvar
    await this.saveNotifications();
    
    log('📝 Notificação adicionada:', notification.title);
  }

  /**
   * Marcar notificação como lida (OTIMIZADO)
   */
  async markAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification && !notification.read) {
      // Marcar localmente
      notification.read = true;
      await this.saveNotifications();

      // Emitir evento para atualizar UI
      notificationEvents.emit('notificationRead', {
        notificationId,
        unreadCount: this.getUnreadCount(),
        totalCount: this.notifications.length
      });

      log('✅ Notificação marcada como lida:', notificationId);
      log('📊 Não lidas restantes:', this.getUnreadCount());
    }
  }

  /**
   * Marcar todas como lidas
   */
  async markAllAsRead(): Promise<void> {
    let count = 0;
    this.notifications.forEach(notification => {
      if (!notification.read) {
        notification.read = true;
        count++;
      }
    });

    if (count > 0) {
      await this.saveNotifications();

      // Emitir evento para atualizar UI
      notificationEvents.emit('allNotificationsRead', {
        markedCount: count,
        unreadCount: this.getUnreadCount(),
        totalCount: this.notifications.length
      });

      log('✅ Marcadas', count, 'notificações como lidas');
      log('📊 Não lidas restantes:', this.getUnreadCount());
    }
  }

  /**
   * ✨ NOVO: Obter chave única do usuário para armazenamento (múltiplas fontes)
   */
  private async getUserStorageKey(): Promise<string> {
    try {
      // FONTE 1: Tentar obter dados do usuário do storage (salvo no login)
      try {
        const userData = await nativeStorage.getItem('user_data');
        if (userData) {
          const user = JSON.parse(userData);
          if (user.codass && user.codent) {
            return `push_notifications_${user.codass}_${user.codent}`;
          }
        }
      } catch (error) {
        // Silencioso
      }

      // FONTE 2: Tentar obter do robustStorage (backup)
      try {
        const { robustStorage } = await import('../utils/robustStorage');
        const userData = await robustStorage.getItem('user_data');
        if (userData) {
          const user = JSON.parse(userData);
          if (user.codass && user.codent) {
            return `push_notifications_${user.codass}_${user.codent}`;
          }
        }
      } catch (error) {
        // Silencioso
      }

      // FONTE 3: Fallback com hash do cartão
      try {
        const cartaoLogin = await nativeStorage.getItem('cartao_login');
        if (cartaoLogin) {
          const hashCode = cartaoLogin.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
          }, 0);
          return `push_notifications_temp_${Math.abs(hashCode)}`;
        }
      } catch (error) {
        // Silencioso
      }

      // FALLBACK: usar chave genérica
      return 'push_notifications_guest';
    } catch (error) {
      return 'push_notifications_guest';
    }
  }

  /**
   * Carregar notificações salvas (por usuário)
   */
  private async loadStoredNotifications(): Promise<void> {
    try {
      const storageKey = await this.getUserStorageKey();
      const stored = await nativeStorage.getItem(storageKey);

      if (stored) {
        const loadedNotifications = JSON.parse(stored);
        this.notifications = Array.isArray(loadedNotifications) ? loadedNotifications : [];
      } else {
        this.notifications = [];
      }
    } catch (error) {
      this.notifications = [];
    }
  }

  /**
   * Salvar notificações (por usuário)
   */
  private async saveNotifications(): Promise<void> {
    try {
      const storageKey = await this.getUserStorageKey();
      await nativeStorage.setItem(storageKey, JSON.stringify(this.notifications));
      log('💾 Notificações salvas para:', storageKey, '- Total:', this.notifications.length);
    } catch (error) {
      log('❌ Erro ao salvar notificações:', error);
    }
  }

  /**
   * ✨ NOVO: Limpar notificações do usuário atual (para logout)
   */
  async clearUserNotifications(): Promise<void> {
    try {
      const storageKey = await this.getUserStorageKey();
      nativeStorage.removeItem(storageKey);
      this.notifications = [];
      log('🗑️ Notificações do usuário limpas:', storageKey);

      // Emitir evento para atualizar UI
      notificationEvents.emit('notificationsCleared', {
        unreadCount: 0,
        totalCount: 0
      });
    } catch (error) {
      log('❌ Erro ao limpar notificações:', error);
    }
  }

  /**
   * ✨ NOVO: Recarregar notificações para novo usuário (após login)
   */
  async reloadUserNotifications(): Promise<void> {
    try {
      log('🔄 Recarregando notificações para novo usuário...');

      // Obter chave do usuário atual
      const currentUserKey = await this.getUserStorageKey();
      log('🔑 Chave do usuário atual:', currentUserKey);

      // Limpar notificações atuais da memória
      this.notifications = [];

      // Carregar notificações do novo usuário
      await this.loadStoredNotifications();

      // Emitir evento para atualizar UI
      notificationEvents.emit('notificationsReloaded', {
        unreadCount: this.getUnreadCount(),
        totalCount: this.notifications.length,
        userKey: currentUserKey
      });

      log('✅ Notificações recarregadas. Total:', this.notifications.length);
      log('📊 Não lidas:', this.getUnreadCount());
    } catch (error) {
      log('❌ Erro ao recarregar notificações:', error);
    }
  }

  /**
   * ✨ NOVO: Limpar dados do usuário anterior (para troca de usuário)
   */
  async clearPreviousUserData(): Promise<void> {
    try {
      log('🧹 Limpando dados do usuário anterior...');

      // Limpar notificações da memória
      this.notifications = [];

      // Limpar dados do usuário do storage
      try {
        nativeStorage.removeItem('user_data');
        log('🗑️ user_data removido do nativeStorage');
      } catch (error) {
        log('⚠️ Erro ao remover user_data do nativeStorage:', error);
      }

      try {
        const { robustStorage } = await import('../utils/robustStorage');
        await robustStorage.removeItem('user_data');
        log('🗑️ user_data removido do robustStorage');
      } catch (error) {
        log('⚠️ Erro ao remover user_data do robustStorage:', error);
      }

      // Emitir evento para atualizar UI
      notificationEvents.emit('previousUserCleared', {
        unreadCount: 0,
        totalCount: 0
      });

      log('✅ Dados do usuário anterior limpos');
    } catch (error) {
      log('❌ Erro ao limpar dados do usuário anterior:', error);
    }
  }



  // Métodos públicos
  getToken(): string | null {
    return this.currentToken;
  }

  getNotifications(): PushNotification[] {
    return this.notifications;
  }

  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Adicionar notificação (método público para SendMessageScreen)
   */
  async addNotificationPublic(notification: PushNotification): Promise<void> {
    await this.addNotification(notification);
  }

  isServiceInitialized(): boolean {
    return this.isInitialized;
  }

  getServiceInfo(): any {
    return {
      initialized: this.isInitialized,
      token: this.currentToken?.substring(0, 50) + '...',
      notificationCount: this.notifications.length,
      unreadCount: this.getUnreadCount(),
      environment: Constants.executionEnvironment,
      isDevice: Device.isDevice
    };
  }

  /**
   * Cleanup ao destruir o serviço
   */
  destroy(): void {
    this.listeners.forEach(listener => {
      if (listener && typeof listener.remove === 'function') {
        listener.remove();
      }
    });
    this.listeners = [];
    log('🧹 Serviço limpo');
  }
}

// Exportar instância única
const pushNotificationService = new PushNotificationService();
export default pushNotificationService;
