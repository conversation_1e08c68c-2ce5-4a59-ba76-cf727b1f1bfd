import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import SafeHeader from '../components/SafeHeader';
import Cores from '../constants/Cores';
import { useAppContext } from '../context/AppContext';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import {
  listarEstabelecimentos,
  listarFiliaisEstabelecimento,
  Estabelecimento,
  FilialEstabelecimento
} from '../services/api';

interface RedeConveniadaResultadosScreenProps {
  navigation: {
    goBack: () => void;
  };
  route: {
    params: {
      estado: string;
      cidade: string;
      segmento: string;
    };
  };
}

const RedeConveniadaResultadosScreen = ({ navigation, route }: RedeConveniadaResultadosScreenProps) => {
  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => navigation.goBack());
  const [estabelecimentos, setEstabelecimentos] = useState<Estabelecimento[]>([]);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [filiaisData, setFiliaisData] = useState<{ [key: string]: FilialEstabelecimento[] }>({});
  const [loading, setLoading] = useState(false);
  const [loadingFiliais, setLoadingFiliais] = useState<Set<string>>(new Set());

  const { getUsuarioData } = useAppContext();
  const { estado, cidade, segmento } = route.params;

  useEffect(() => {
    carregarEstabelecimentos();
  }, []);

  const carregarEstabelecimentos = async () => {
    setLoading(true);
    try {
      const usuarioData = getUsuarioData();
      if (!usuarioData) {
        Alert.alert('Erro', 'Dados do usuário não encontrados');
        return;
      }

      const codent = usuarioData.usuario.codent.toString();
      
      console.log('🔍 Carregando estabelecimentos com filtros:', {
        codent,
        estado: estado !== '0' ? estado : undefined,
        cidade: cidade !== '0' ? cidade : undefined,
        segmento: segmento !== '0' ? segmento : undefined
      });

      const response = await listarEstabelecimentos(
        codent,
        estado !== '0' ? estado : undefined,
        cidade !== '0' ? cidade : undefined,
        segmento !== '0' ? segmento : undefined
      );

      if (response.success) {
        setEstabelecimentos(response.data || []);
        console.log(`✅ ${response.data?.length || 0} estabelecimentos carregados`);
      } else {
        Alert.alert('Erro', response.message);
      }

    } catch (error) {
      console.error('❌ Erro ao carregar estabelecimentos:', error);
      Alert.alert('Erro', 'Erro ao carregar estabelecimentos');
    } finally {
      setLoading(false);
    }
  };

  const toggleExpanded = async (lojcodloj: string) => {
    const newExpanded = new Set(expandedItems);
    
    if (expandedItems.has(lojcodloj)) {
      // Recolher
      newExpanded.delete(lojcodloj);
    } else {
      // Expandir e carregar filiais se necessário
      newExpanded.add(lojcodloj);
      
      if (!filiaisData[lojcodloj]) {
        await carregarFiliais(lojcodloj);
      }
    }
    
    setExpandedItems(newExpanded);
  };

  const carregarFiliais = async (ecs: string) => {
    const newLoadingFiliais = new Set(loadingFiliais);
    newLoadingFiliais.add(ecs);
    setLoadingFiliais(newLoadingFiliais);

    try {
      const response = await listarFiliaisEstabelecimento(
        ecs,
        estado !== '0' ? estado : undefined,
        cidade !== '0' ? cidade : undefined
      );

      if (response.success) {
        setFiliaisData(prev => ({
          ...prev,
          [ecs]: response.data || []
        }));
        console.log(`✅ ${response.data?.length || 0} filiais carregadas para ${ecs}`);
      }

    } catch (error) {
      console.error('❌ Erro ao carregar filiais:', error);
    } finally {
      const newLoadingFiliais = new Set(loadingFiliais);
      newLoadingFiliais.delete(ecs);
      setLoadingFiliais(newLoadingFiliais);
    }
  };

  const renderEstabelecimento = ({ item, index }: { item: Estabelecimento; index: number }) => {
    const isExpanded = expandedItems.has(item.lojcodloj);
    const isLoadingFiliais = loadingFiliais.has(item.lojcodloj);
    const filiais = filiaisData[item.lojcodloj] || [];

    /* Na forma abaixo, estava sempre respeitando a cor vinda da API
    const backgroundColor = item.cor || (index % 2 === 0 ? Cores.primaria : Cores.fundoPrincipal);
    */
    //Alternar a cor para o padrão do projeto, linhas pares e impares na variavel de estilo de Cores
    const backgroundColor = index % 2 === 0 ? Cores.primaria : Cores.fundoCinza;

    return (
      <View style={styles.estabelecimentoContainer}>
        <TouchableOpacity
          style={[styles.estabelecimentoHeader, { backgroundColor }]}
          onPress={() => toggleExpanded(item.lojcodloj)}
        >
          <View style={styles.estabelecimentoInfo}>
            <Text style={styles.estabelecimentoNome}>{item.nome}</Text>
            <Text style={styles.estabelecimentoSegmento}>{item.segmento}</Text>
          </View>
          <Text style={styles.expandIcon}>
            {isExpanded ? '▼' : '▶'}
          </Text>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.filiaisContainer}>
            {isLoadingFiliais ? (
              <View style={styles.loadingFiliais}>
                <ActivityIndicator size="small" color={Cores.primaria} />
                <Text style={styles.loadingFiliaisText}>Carregando filiais...</Text>
              </View>
            ) : filiais.length > 0 ? (
              filiais.map((filial, filialIndex) => (
                <View key={filialIndex} style={styles.filialItem}>
                  <Text style={styles.filialEndereco}>{filial.endereco}</Text>
                  <Text style={styles.filialCidade}>{filial.cidade} - {filial.uf}</Text>
                  {filial.fone && (
                    <Text style={styles.filialTelefone}>📞 {filial.fone}</Text>
                  )}
                </View>
              ))
            ) : (
              <View style={styles.semFiliais}>
                <Text style={styles.semFiliaisText}>Nenhuma filial encontrada</Text>
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <SafeHeader
        title="Resultado da Pesquisa"
        onBackPress={() => navigation.goBack()}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Cores.primaria} />
          <Text style={styles.loadingText}>Carregando estabelecimentos...</Text>
        </View>
      ) : estabelecimentos.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Nenhum estabelecimento encontrado</Text>
          <Text style={styles.emptySubtext}>Tente ajustar os filtros de busca</Text>
        </View>
      ) : (
        <FlatList
          data={estabelecimentos}
          renderItem={renderEstabelecimento}
          keyExtractor={(item) => item.lojcodloj}
          style={styles.lista}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listaContent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingVertical: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    color: Cores.textoBranco,
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerTitle: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 34,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Cores.textoMedio,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    textAlign: 'center',
    marginBottom: 10,
  },
  emptySubtext: {
    fontSize: 14,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  lista: {
    flex: 1,
  },
  listaContent: {
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  estabelecimentoContainer: {
    marginBottom: 10,
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  estabelecimentoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    justifyContent: 'space-between',
  },
  estabelecimentoInfo: {
    flex: 1,
  },
  estabelecimentoNome: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Cores.textoBranco,
    marginBottom: 4,
  },
  estabelecimentoSegmento: {
    fontSize: 14,
    color: Cores.textoBranco,
    opacity: 0.9,
  },
  expandIcon: {
    fontSize: 20,
    color: Cores.textoBranco,
    fontWeight: 'bold',
  },
  filiaisContainer: {
    backgroundColor: Cores.fundoCard,
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  loadingFiliais: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  loadingFiliaisText: {
    marginLeft: 10,
    color: Cores.textoMedio,
  },
  filialItem: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: Cores.bordaClara,
  },
  filialEndereco: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 4,
  },
  filialCidade: {
    fontSize: 14,
    color: Cores.textoMedio,
    marginBottom: 4,
  },
  filialTelefone: {
    fontSize: 14,
    color: Cores.textoEscuro,
    fontWeight: 'bold',
  },
  semFiliais: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  semFiliaisText: {
    fontSize: 14,
    color: Cores.textoMedio,
    fontStyle: 'italic',
  },
});

export default RedeConveniadaResultadosScreen;
