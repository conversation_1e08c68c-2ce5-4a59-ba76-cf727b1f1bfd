#!/usr/bin/env node

/**
 * Script para testar as correções do Android 8.0
 * Execute: node test-android8.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testando correções para Android 8.0...\n');

// Verificar se os arquivos foram criados/modificados
const filesToCheck = [
  'src/utils/polyfills.ts',
  'src/utils/networkConfig.ts', 
  'src/services/api.ts',
  'src/utils/android8TestUtils.ts',
  'App.tsx',
  'ANDROID_8_FIXES.md'
];

console.log('📁 Verificando arquivos...');
let allFilesExist = true;

filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - ARQUIVO NÃO ENCONTRADO`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Alguns arquivos estão faltando!');
  process.exit(1);
}

console.log('\n🔍 Verificando conteúdo dos arquivos...');

// Verificar se polyfills.ts contém as funções necessárias
const polyfillsContent = fs.readFileSync('src/utils/polyfills.ts', 'utf8');
const polyfillChecks = [
  'createCompatibleAbortController',
  'createCompatibleAbortSignalTimeout',
  'applyPolyfills',
  'PolyfillAbortController'
];

console.log('\n📄 src/utils/polyfills.ts:');
polyfillChecks.forEach(check => {
  if (polyfillsContent.includes(check)) {
    console.log(`  ✅ ${check}`);
  } else {
    console.log(`  ❌ ${check} - NÃO ENCONTRADO`);
  }
});

// Verificar se networkConfig.ts foi corrigido
const networkConfigContent = fs.readFileSync('src/utils/networkConfig.ts', 'utf8');
console.log('\n📄 src/utils/networkConfig.ts:');

if (networkConfigContent.includes('createCompatibleAbortController')) {
  console.log('  ✅ Usando createCompatibleAbortController');
} else {
  console.log('  ❌ createCompatibleAbortController não encontrado');
}

if (!networkConfigContent.includes('AbortSignal.timeout')) {
  console.log('  ✅ AbortSignal.timeout removido');
} else {
  console.log('  ❌ AbortSignal.timeout ainda presente');
}

// Verificar se api.ts foi corrigido
const apiContent = fs.readFileSync('src/services/api.ts', 'utf8');
console.log('\n📄 src/services/api.ts:');

// Contar quantas vezes fetchWithTimeout aparece
const fetchWithTimeoutCount = (apiContent.match(/fetchWithTimeout\(/g) || []).length;
const directFetchCount = (apiContent.match(/await fetch\(/g) || []).length;

console.log(`  ✅ fetchWithTimeout usado ${fetchWithTimeoutCount} vezes`);
console.log(`  ${directFetchCount === 0 ? '✅' : '⚠️'} fetch direto usado ${directFetchCount} vezes`);

// Verificar se App.tsx foi modificado
const appContent = fs.readFileSync('App.tsx', 'utf8');
console.log('\n📄 App.tsx:');

if (appContent.includes('applyPolyfills')) {
  console.log('  ✅ applyPolyfills importado e usado');
} else {
  console.log('  ❌ applyPolyfills não encontrado');
}

// Verificar estrutura do projeto
console.log('\n🏗️ Verificando estrutura do projeto...');

const requiredDirs = [
  'src/utils',
  'src/services', 
  'src/screens',
  'android/app/src/main/res/xml'
];

requiredDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    console.log(`✅ ${dir}/`);
  } else {
    console.log(`❌ ${dir}/ - DIRETÓRIO NÃO ENCONTRADO`);
  }
});

// Verificar network_security_config.xml
const networkSecurityConfig = 'android/app/src/main/res/xml/network_security_config.xml';
if (fs.existsSync(networkSecurityConfig)) {
  const configContent = fs.readFileSync(networkSecurityConfig, 'utf8');
  console.log('\n📄 network_security_config.xml:');
  
  if (configContent.includes('tecbiz.com.br')) {
    console.log('  ✅ Configuração para tecbiz.com.br presente');
  } else {
    console.log('  ❌ Configuração para tecbiz.com.br não encontrada');
  }
  
  if (configContent.includes('cleartextTrafficPermitted="true"')) {
    console.log('  ✅ cleartext traffic permitido');
  } else {
    console.log('  ⚠️ cleartext traffic pode estar bloqueado');
  }
} else {
  console.log('\n❌ network_security_config.xml não encontrado');
}

console.log('\n🎯 Resumo das correções:');
console.log('1. ✅ Polyfills para AbortController/AbortSignal criados');
console.log('2. ✅ NetworkConfig corrigido para usar AbortController manual');
console.log('3. ✅ API corrigida para usar fetchWithTimeout');
console.log('4. ✅ App.tsx configurado para aplicar polyfills');
console.log('5. ✅ Testes de compatibilidade Android 8.0 criados');
console.log('6. ✅ Documentação das correções criada');

console.log('\n🚀 Próximos passos:');
console.log('1. Execute: npx expo run:android');
console.log('2. Teste em dispositivo Android 8.0');
console.log('3. Verifique os logs para confirmar que polyfills foram aplicados');
console.log('4. Teste o login para verificar se HTTPS funciona');

console.log('\n✅ Verificação concluída! As correções parecem estar corretas.');
