// Configuração Firebase para TecBiz - CENTRALIZED CONFIG
// Todas as chaves e configurações Firebase em um local

// ========================================
// CONFIGURAÇÃO PRINCIPAL DO FIREBASE
// ========================================

export const FIREBASE_CONFIG = {
  // Projeto TecBiz Principal - CORRIGIDO PARA MATCH COM google-services.json
  apiKey: "AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk",
  authDomain: "tecbizappass.firebaseapp.com",
  projectId: "tecbizappass",
  storageBucket: "tecbizappass.firebasestorage.app",
  messagingSenderId: "608372388905",
  appId: "1:608372388905:android:25e23e2d6867cb314ad838"
};

// ========================================
// CONFIGURAÇÃO ALTERNATIVA (BACKUP)
// ========================================

export const FIREBASE_CONFIG_BACKUP = {
  // Projeto TecBiz Backup
  apiKey: "AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk",
  authDomain: "tecbizappass.firebaseapp.com",
  projectId: "tecbizappass",
  storageBucket: "tecbizappass.appspot.com",
  messagingSenderId: "608372388905",
  appId: "1:608372388905:android:25e23e2d6867cb314ad838"
};

// ========================================
// CHAVES VAPID PARA WEB PUSH
// ========================================

export const VAPID_KEYS = {
  // Chave VAPID principal (obter no Console Firebase)
  primary: "BJIdKr66q5FyfsNAG6YEzLjE=39cO7PxaTNTfKoNEaCVoEXpgYQScfnnRjK8snu8bbJB0trvEq34k0jLiABJl6rkzQ66qF1y5FqNA6YEslEz93cOP7aXfFTxoNEcaOEoVjQyPScfhnUxRR93SbbHtv_IO5trVEq34lxQfjLlA",
  
  // Chave VAPID backup
  backup: "BM8snu8bbJB0trvEq34k0jLiABJl6rkzQ66qF1y5FqNA6YEslEz93cOP7aXfFTxoNEcaOEoVjQyPScfhnUxRR93SbbHtv_IO5trVEq34lxQfjLlABJIdKr66q5FyfsNAG6YEzLjE=39cO7PxaTNTf"
};

// ========================================
// CONFIGURAÇÃO EXPO
// ========================================

export const EXPO_CONFIG = {
  // Project ID para Expo Push Notifications
  projectId: FIREBASE_CONFIG.projectId,
  
  // Configurações específicas do Expo
  experienceId: "@tecbiz/TecBizExpoApp",
  
  // URL do servidor Expo (se usando servidor próprio)
  pushUrl: "https://exp.host/--/api/v2/push/send"
};

// ========================================
// CONFIGURAÇÃO DINÂMICA
// ========================================

// Função para obter configuração baseada no ambiente
export const getFirebaseConfig = (useBackup: boolean = false) => {
  const config = useBackup ? FIREBASE_CONFIG_BACKUP : FIREBASE_CONFIG;
  
  console.log(`🔥 Usando configuração Firebase: ${config.projectId}`);
  console.log(`📱 Sender ID: ${config.messagingSenderId}`);
  
  return config;
};

// Função para obter chave VAPID
export const getVapidKey = (useBackup: boolean = false) => {
  const key = useBackup ? VAPID_KEYS.backup : VAPID_KEYS.primary;
  
  console.log(`🔑 Usando chave VAPID: ${key.substring(0, 20)}...`);
  
  return key;
};

// ========================================
// VALIDAÇÃO DE CONFIGURAÇÃO
// ========================================

export const validateFirebaseConfig = (config: typeof FIREBASE_CONFIG): boolean => {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'messagingSenderId', 'appId'];
  
  for (const field of requiredFields) {
    if (!config[field as keyof typeof config]) {
      console.error(`❌ Campo obrigatório ausente: ${field}`);
      return false;
    }
  }
  
  console.log('✅ Configuração Firebase válida');
  return true;
};

// ========================================
// INSTRUÇÕES PARA CONFIGURAÇÃO
// ========================================

export const SETUP_INSTRUCTIONS = {
  firebase: [
    "1. Acesse o Console Firebase: https://console.firebase.google.com/",
    "2. Selecione seu projeto TecBiz",
    "3. Vá em Project Settings > General",
    "4. Copie as configurações do seu app",
    "5. Cole as chaves no FIREBASE_CONFIG acima"
  ],
  
  vapid: [
    "1. No Console Firebase, vá em Project Settings",
    "2. Clique na aba 'Cloud Messaging'",
    "3. Em 'Web configuration', clique em 'Generate key pair'",
    "4. Copie a chave VAPID gerada",
    "5. Cole no VAPID_KEYS.primary acima"
  ],
  
  expo: [
    "1. Execute: expo install expo-notifications",
    "2. Configure o app.json com as configurações de push",
    "3. Para development build: expo run:android",
    "4. Para Expo Go: limitações no SDK 53+"
  ]
};

// Função para mostrar instruções
export const showSetupInstructions = () => {
  console.log('\n📋 INSTRUÇÕES DE CONFIGURAÇÃO FIREBASE:');
  console.log('\n🔥 Firebase:');
  SETUP_INSTRUCTIONS.firebase.forEach(step => console.log(step));
  
  console.log('\n🔑 VAPID Key:');
  SETUP_INSTRUCTIONS.vapid.forEach(step => console.log(step));
  
  console.log('\n📱 Expo:');
  SETUP_INSTRUCTIONS.expo.forEach(step => console.log(step));
  
  console.log('\n💡 Dica: Para tokens FCM reais, use um dispositivo físico ou development build');
};

// ========================================
// EXPORT DEFAULT
// ========================================

export default {
  FIREBASE_CONFIG,
  FIREBASE_CONFIG_BACKUP,
  VAPID_KEYS,
  EXPO_CONFIG,
  getFirebaseConfig,
  getVapidKey,
  validateFirebaseConfig,
  showSetupInstructions
};
