import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Cores from '../constants/Cores';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { getSafeBottomPadding } from '../utils/safeArea';

interface FooterProps {
  currentScreen: 'Home' | 'Extrato' | 'CartaoVirtual';
  navigation: {
    navigate: (screen: any) => void;
  };
}

const Footer = ({ currentScreen, navigation }: FooterProps) => {
  const handleNavigation = (screen: 'Home' | 'Extrato' | 'CartaoVirtual') => {
    if (screen !== currentScreen) {
      navigation.navigate(screen);
    }
  };

  // Usar a função melhorada de safe bottom padding
  const safeBottomPadding = getSafeBottomPadding();

  return (
    <View style={[styles.footer, { paddingBottom: safeBottomPadding }]}>
      {/* <PERSON><PERSON><PERSON><PERSON> "Acesso rápido" */}
      <View style={styles.headerContainer}>
        <Text style={styles.headerText}>Acesso rápido</Text>
      </View>

      {/* Menu de ícones */}
      <View style={styles.menuContainer}>
        {/* Botão Extrato - Primeira posição */}
        <TouchableOpacity
          style={[
            styles.footerButton,
            currentScreen === 'Extrato' && styles.activeButton
          ]}
          onPress={() => handleNavigation('Extrato')}
        >
          <Text style={[
            styles.footerIcon,
            currentScreen === 'Extrato' && styles.activeIcon
          ]}>
            📋
          </Text>
          <Text style={[
            styles.footerText,
            currentScreen === 'Extrato' && styles.activeText
          ]}>
            Extrato
          </Text>
        </TouchableOpacity>

        {/* Botão Home - Centro */}
        <TouchableOpacity
          style={[
            styles.footerButton,
            currentScreen === 'Home' && styles.activeButton
          ]}
          onPress={() => handleNavigation('Home')}
        >
          <Text style={[
            styles.footerIcon,
            currentScreen === 'Home' && styles.activeIcon
          ]}>
            🏠
          </Text>
          <Text style={[
            styles.footerText,
            currentScreen === 'Home' && styles.activeText
          ]}>
            Home
          </Text>
        </TouchableOpacity>

        {/* Botão Cartão Virtual - Última posição */}
        <TouchableOpacity
          style={[
            styles.footerButton,
            currentScreen === 'CartaoVirtual' && styles.activeButton
          ]}
          onPress={() => handleNavigation('CartaoVirtual')}
        >
          <Text style={[
            styles.footerIcon,
            currentScreen === 'CartaoVirtual' && styles.activeIcon
          ]}>
            💳
          </Text>
          <Text style={[
            styles.footerText,
            currentScreen === 'CartaoVirtual' && styles.activeText
          ]}>
            Cartão
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  footer: {
    backgroundColor: Cores.primaria,
    paddingTop: 8,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: Cores.primaria,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // paddingBottom será aplicado dinamicamente via props
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 4,
  },
  headerText: {
    fontSize: 10,
    color: Cores.textoBranco,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  menuContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
footerButton: {
  flexDirection: 'column', // Ícone em cima, texto embaixo
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: 4,
  paddingHorizontal: 8,
  minWidth: 60,
  borderRadius: 6,
  height: 50,
},
  activeButton: {
    backgroundColor: Cores.textoBranco,
    borderWidth: 2,
    borderColor: Cores.textoBranco,
  },
footerIcon: {
  fontSize: 18, // Ícone menor
  color: Cores.textoBranco,
},
footerText: {
  fontSize: 9,
  color: Cores.textoBranco,
  fontWeight: '500',
  marginTop: 2, // Espaço menor entre ícone e texto
},
  activeIcon: {
    color: Cores.primaria, // Ícone ativo usa cor primária
  },
  activeText: {
    color: Cores.primaria,
    fontWeight: 'bold',
  },
});

export default Footer;
