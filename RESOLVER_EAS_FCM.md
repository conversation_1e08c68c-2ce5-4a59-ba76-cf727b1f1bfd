# 🏗️ RESOLVER PROBLEMA FCM NO EAS BUILD

## 🎯 **PROBLEMA IDENTIFICADO:**

✅ **Funcionava no Expo Go** (usa credenciais do Expo)  
❌ **Não funciona no EAS Build** (precisa de suas próprias credenciais)  
✅ **Token real obtido** (`ExponentPushToken[eMZsHICR_9RNghuZC5vlWT]`)  
✅ **Chave FCM configurada** (`AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk`)  
❌ **EAS não sabe usar essas credenciais**  

## 🔧 **DIFERENÇA EXPO GO vs EAS BUILD:**

### **📱 EXPO GO:**
- Usa credenciais compartilhadas do Expo
- Push funciona automaticamente
- Não precisa configurar FCM

### **🏗️ EAS BUILD:**
- Usa suas próprias credenciais
- Precisa configurar FCM no projeto Expo
- Cada build tem suas próprias credenciais

---

## 🚀 **SOLUÇÃO PASSO A PASSO:**

### **PASSO 1: CONFIGURAR FCM NO EAS**
```bash
# 1. Executar script automático
node configurar_fcm_eas.js
```

**OU manualmente:**
```bash
# 1. Verificar login
npx expo whoami

# 2. Configurar FCM
npx expo push:android:upload --api-key AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk

# 3. Verificar configuração
npx expo push:android:show
```

### **PASSO 2: FAZER NOVO BUILD EAS**
```bash
# Build com cache limpo
eas build --platform android --profile preview --clear-cache
```

### **PASSO 3: INSTALAR E TESTAR**
```bash
# 1. Instalar novo APK no dispositivo
# 2. Fazer login no app
# 3. Obter novo token (pode ter mudado)
# 4. Testar push
php test_eas_push.php
```

---

## 🔍 **DIAGNÓSTICO DETALHADO:**

### **1. VERIFICAR CONFIGURAÇÃO ATUAL:**
```bash
# Ver configuração FCM no EAS
npx expo push:android:show

# Resultado esperado:
# ✅ "FCM server key is configured"
# ❌ "No FCM server key configured"
```

### **2. VERIFICAR BUILD:**
```bash
# Ver builds recentes
eas build:list --platform android --limit 5

# Verificar se build foi feito APÓS configurar FCM
```

### **3. VERIFICAR TOKEN:**
```bash
# Token deve ser obtido do app compilado com EAS
# NÃO do Expo Go
# Pode ser diferente após novo build
```

---

## 🛠️ **SOLUÇÕES ALTERNATIVAS:**

### **OPÇÃO 1: CONFIGURAÇÃO MANUAL VIA DASHBOARD**
```
1. Acesse: https://expo.dev/
2. Vá para seu projeto: tecbizappass
3. Credentials → Android
4. Add FCM Server Key
5. Cole: AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk
```

### **OPÇÃO 2: EAS CREDENTIALS CONFIGURE**
```bash
npx eas credentials:configure --platform android

# Escolher:
# → Google Service Account Key
# → Upload google-services.json
# → Ou inserir chave manualmente
```

### **OPÇÃO 3: REBUILD COMPLETO**
```bash
# 1. Limpar credenciais
eas credentials:delete --platform android

# 2. Reconfigurar
npx expo push:android:upload --api-key AIzaSyBPH6CRnvBcEIxJ2IoKngkuEL9CCbWpDKk

# 3. Novo build
eas build --platform android --profile preview --clear-cache
```

---

## 📊 **CHECKLIST DE VERIFICAÇÃO:**

### **✅ ANTES DO BUILD:**
- [ ] FCM configurado no EAS (`npx expo push:android:show`)
- [ ] google-services.json presente
- [ ] Chave FCM válida
- [ ] Logado no Expo CLI

### **✅ APÓS O BUILD:**
- [ ] Build concluído com sucesso
- [ ] APK instalado no dispositivo
- [ ] Login feito no app
- [ ] Novo token obtido
- [ ] Teste de push executado

### **✅ TESTE FINAL:**
- [ ] App fechado completamente
- [ ] Push enviado via PHP
- [ ] Notificação aparece na tela
- [ ] Som/vibração funciona
- [ ] Mensagem aparece no app

---

## 🎯 **COMANDOS RESUMIDOS:**

### **CONFIGURAÇÃO RÁPIDA:**
```bash
# 1. Configurar FCM
node configurar_fcm_eas.js

# 2. Novo build
eas build --platform android --profile preview --clear-cache

# 3. Testar
php test_eas_push.php
```

### **VERIFICAÇÃO:**
```bash
# Ver configuração
npx expo push:android:show

# Ver builds
eas build:list --platform android --limit 3
```

---

## 🚨 **PONTOS IMPORTANTES:**

### **🔑 CREDENCIAIS:**
- **EAS Build** ≠ **Expo Go**
- Cada build precisa de configuração própria
- FCM deve ser configurado ANTES do build

### **📱 TOKEN:**
- Token pode mudar após novo build
- Sempre obter token do app EAS
- Não usar token do Expo Go

### **⏰ TIMING:**
- Configurar FCM → Build → Instalar → Testar
- Não pular nenhum passo
- Aguardar build completar

---

## 🎉 **RESULTADO ESPERADO:**

Após seguir os passos:

✅ **FCM configurado** no projeto EAS  
✅ **Build com credenciais** corretas  
✅ **Push funciona** com app fechado  
✅ **Notificações aparecem** na tela  
✅ **App mostra mensagens** na lista  

**🚀 EXECUTE OS COMANDOS NA ORDEM E SEU SISTEMA VAI FUNCIONAR PERFEITAMENTE!**
