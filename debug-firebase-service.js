#!/usr/bin/env node

const fs = require('fs');

console.log('🔍 Diagnosticando problema do Firebase Production Service...\n');

// 1. Verificar se o serviço está sendo importado corretamente
console.log('📋 VERIFICANDO IMPORTAÇÕES:');

try {
  const appNavigator = fs.readFileSync('src/navigation/AppNavigator.tsx', 'utf8');
  
  if (appNavigator.includes("import firebaseProductionService from '../services/firebaseProductionService'")) {
    console.log('✅ Import do firebaseProductionService está correto');
  } else {
    console.log('❌ Import do firebaseProductionService não encontrado');
  }
  
  if (appNavigator.includes('await firebaseProductionService.initialize()')) {
    console.log('✅ Chamada do initialize() está presente');
  } else {
    console.log('❌ Chamada do initialize() não encontrada');
  }
} catch (error) {
  console.log('❌ Erro ao verificar AppNavigator:', error.message);
}

// 2. Verificar se o serviço tem problemas de sintaxe
console.log('\n📋 VERIFICANDO SERVIÇO:');

try {
  const service = fs.readFileSync('src/services/firebaseProductionService.ts', 'utf8');
  
  // Verificar imports do Firebase
  if (service.includes('import { initializeApp, FirebaseApp }')) {
    console.log('✅ Import do Firebase App está correto');
  } else {
    console.log('❌ Import do Firebase App não encontrado');
  }
  
  if (service.includes('import { getMessaging, getToken, onMessage, Messaging }')) {
    console.log('✅ Import do Firebase Messaging está correto');
  } else {
    console.log('❌ Import do Firebase Messaging não encontrado');
  }
  
  // Verificar se o método initialize existe
  if (service.includes('async initialize(): Promise<string | null>')) {
    console.log('✅ Método initialize() existe');
  } else {
    console.log('❌ Método initialize() não encontrado');
  }
  
  // Verificar se initializeFirebaseSDK existe
  if (service.includes('private async initializeFirebaseSDK()')) {
    console.log('✅ Método initializeFirebaseSDK() existe');
  } else {
    console.log('❌ Método initializeFirebaseSDK() não encontrado');
  }
  
  // Verificar se há logs de produção
  if (service.includes('productionLog(')) {
    console.log('✅ Logs de produção estão presentes');
  } else {
    console.log('❌ Logs de produção não encontrados');
  }
  
} catch (error) {
  console.log('❌ Erro ao verificar serviço:', error.message);
}

// 3. Verificar configuração de produção
console.log('\n📋 VERIFICANDO CONFIGURAÇÃO DE PRODUÇÃO:');

try {
  const prodConfig = fs.readFileSync('src/config/production.ts', 'utf8');
  
  if (prodConfig.includes('enableDetailedLogs')) {
    console.log('✅ Configuração de logs está presente');
  } else {
    console.log('❌ Configuração de logs não encontrada');
  }
  
  // Verificar se logs estão habilitados
  if (prodConfig.includes('enableDetailedLogs: !Constants.executionEnvironment')) {
    console.log('⚠️ Logs podem estar desabilitados em produção');
    console.log('   Isso explica por que não vemos os logs do Firebase Service');
  }
  
} catch (error) {
  console.log('❌ Erro ao verificar configuração:', error.message);
}

// 4. Verificar se Firebase está instalado
console.log('\n📋 VERIFICANDO DEPENDÊNCIAS:');

try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.dependencies && packageJson.dependencies.firebase) {
    console.log('✅ Firebase está instalado:', packageJson.dependencies.firebase);
  } else {
    console.log('❌ Firebase não está instalado');
  }
  
  if (packageJson.dependencies && packageJson.dependencies['expo-notifications']) {
    console.log('✅ Expo Notifications está instalado:', packageJson.dependencies['expo-notifications']);
  } else {
    console.log('❌ Expo Notifications não está instalado');
  }
  
} catch (error) {
  console.log('❌ Erro ao verificar package.json:', error.message);
}

console.log('\n' + '='.repeat(60));
console.log('🔍 DIAGNÓSTICO COMPLETO');
console.log('\n💡 POSSÍVEIS CAUSAS DO PROBLEMA:');
console.log('1. 🤐 Logs desabilitados em produção (mais provável)');
console.log('2. 🚫 Firebase SDK não funciona em ambiente bare');
console.log('3. ⚠️ Erro silencioso na inicialização');
console.log('4. 🔧 Configuração de ambiente incorreta');

console.log('\n🛠️ SOLUÇÕES RECOMENDADAS:');
console.log('1. Habilitar logs forçadamente para debug');
console.log('2. Adicionar try-catch mais detalhado');
console.log('3. Usar apenas Expo Notifications (mais confiável)');
console.log('4. Criar versão simplificada do serviço');

console.log('\n🎯 PRÓXIMO PASSO:');
console.log('Vou criar uma versão simplificada que força logs e usa apenas Expo Notifications');
