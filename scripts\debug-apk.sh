#!/bin/bash

# Script para debug de APK instalado em dispositivo físico
# Captura logs específicos do Firebase e push notifications

echo "📱 SCRIPT DE DEBUG APK - TECBIZ"
echo "================================"

# Verificar se ADB está disponível
if ! command -v adb &> /dev/null; then
    echo "❌ ADB não encontrado. Instale o Android SDK Platform Tools"
    echo "   Download: https://developer.android.com/studio/releases/platform-tools"
    exit 1
fi

# Verificar dispositivos conectados
echo "🔍 Verificando dispositivos conectados..."
DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)

if [ $DEVICES -eq 0 ]; then
    echo "❌ Nenhum dispositivo conectado"
    echo "   1. Conecte o dispositivo via USB"
    echo "   2. Ative 'Depuração USB' nas opções do desenvolvedor"
    echo "   3. Execute: adb devices"
    exit 1
fi

echo "✅ $DEVICES dispositivo(s) conectado(s)"

# Verificar se o app está instalado
PACKAGE_NAME="com.tecbiz.tecbizassociadospush"
echo "🔍 Verificando se app está instalado..."

if adb shell pm list packages | grep -q $PACKAGE_NAME; then
    echo "✅ App TecBiz encontrado: $PACKAGE_NAME"
else
    echo "❌ App TecBiz não encontrado"
    echo "   Instale o APK primeiro: adb install app.apk"
    exit 1
fi

# Função para capturar logs
capture_logs() {
    echo ""
    echo "📋 CAPTURANDO LOGS ESPECÍFICOS..."
    echo "================================="
    echo "Pressione Ctrl+C para parar"
    echo ""
    
    # Limpar logs antigos
    adb logcat -c
    
    # Capturar logs filtrados
    adb logcat | grep -E "(firebase|expo|token|notification|tecbiz|push|FCM|ExponentPushToken)" --color=always
}

# Função para logs completos
capture_all_logs() {
    echo ""
    echo "📋 CAPTURANDO TODOS OS LOGS..."
    echo "============================="
    echo "Pressione Ctrl+C para parar"
    echo ""
    
    # Limpar logs antigos
    adb logcat -c
    
    # Capturar todos os logs
    adb logcat
}

# Função para testar conectividade
test_connectivity() {
    echo ""
    echo "🌐 TESTANDO CONECTIVIDADE..."
    echo "============================"
    
    # Testar internet
    echo "📡 Testando conexão com internet..."
    if adb shell ping -c 3 ******* > /dev/null 2>&1; then
        echo "✅ Internet OK"
    else
        echo "❌ Sem internet"
    fi
    
    # Testar Firebase
    echo "🔥 Testando conexão com Firebase..."
    if adb shell ping -c 3 firebase.googleapis.com > /dev/null 2>&1; then
        echo "✅ Firebase OK"
    else
        echo "❌ Firebase inacessível"
    fi
    
    # Testar Expo
    echo "📱 Testando conexão com Expo..."
    if adb shell ping -c 3 exp.host > /dev/null 2>&1; then
        echo "✅ Expo OK"
    else
        echo "❌ Expo inacessível"
    fi
}

# Função para info do dispositivo
device_info() {
    echo ""
    echo "📱 INFORMAÇÕES DO DISPOSITIVO"
    echo "============================="
    
    echo "🏷️  Modelo: $(adb shell getprop ro.product.model)"
    echo "🤖 Android: $(adb shell getprop ro.build.version.release)"
    echo "🔧 API Level: $(adb shell getprop ro.build.version.sdk)"
    echo "🏭 Fabricante: $(adb shell getprop ro.product.manufacturer)"
    echo "📦 App Version: $(adb shell dumpsys package $PACKAGE_NAME | grep versionName | head -1 | cut -d'=' -f2)"
    
    # Verificar permissões
    echo ""
    echo "🔐 PERMISSÕES DO APP:"
    adb shell dumpsys package $PACKAGE_NAME | grep -A 20 "requested permissions:" | grep -E "(NOTIFICATION|WAKE_LOCK|RECEIVE|VIBRATE)"
}

# Função para forçar parada e reinício do app
restart_app() {
    echo ""
    echo "🔄 REINICIANDO APP..."
    echo "===================="
    
    echo "⏹️  Parando app..."
    adb shell am force-stop $PACKAGE_NAME
    
    echo "⏳ Aguardando 2 segundos..."
    sleep 2
    
    echo "▶️  Iniciando app..."
    adb shell monkey -p $PACKAGE_NAME -c android.intent.category.LAUNCHER 1
    
    echo "✅ App reiniciado"
}

# Menu principal
show_menu() {
    echo ""
    echo "🎛️  MENU DE DEBUG"
    echo "================="
    echo "1. 📋 Logs Específicos (Firebase/Token)"
    echo "2. 📄 Todos os Logs"
    echo "3. 📱 Info do Dispositivo"
    echo "4. 🌐 Testar Conectividade"
    echo "5. 🔄 Reiniciar App"
    echo "6. 🚪 Sair"
    echo ""
    read -p "Escolha uma opção (1-6): " choice
    
    case $choice in
        1) capture_logs ;;
        2) capture_all_logs ;;
        3) device_info ;;
        4) test_connectivity ;;
        5) restart_app ;;
        6) echo "👋 Saindo..."; exit 0 ;;
        *) echo "❌ Opção inválida"; show_menu ;;
    esac
    
    # Voltar ao menu após execução
    echo ""
    read -p "Pressione Enter para voltar ao menu..."
    show_menu
}

# Executar menu
show_menu
