# Correções do Expo Doctor

## Problema Identificado
O projeto estava configurado como **bare/ejected** (com pastas `android/` e `ios/`) mas mantinha configurações no `app.json` específicas para projetos **managed/prebuild**.

## Alterações Realizadas

### 1. Limpeza do app.json
Removidas as seguintes configurações que não são aplicadas em projetos bare:
- `orientation`: "portrait"
- `icon`: "./assets/icon.png"  
- `userInterfaceStyle`: "light"
- `splash`: { ... }
- `ios`: { ... }
- `android`: { ... }
- `plugins`: [ ... ]
- `web`: { ... }

**<PERSON><PERSON><PERSON> apenas as configurações essenciais:**
- `name`, `slug`, `version`
- `newArchEnabled`: true
- `extra.eas.projectId`
- `owner`

### 2. Supressão de avisos no package.json
Adicionada configuração para suprimir avisos sobre pacotes não listados no React Native Directory:

```json
"expo": {
  "doctor": {
    "reactNativeDirectoryCheck": {
      "listUnknownPackages": false
    }
  }
}
```

## Configurações Removidas (para referência)

### Ícones e Splash Screen
```json
"icon": "./assets/icon.png",
"splash": {
  "image": "./assets/splash-icon.png",
  "resizeMode": "contain",
  "backgroundColor": "#ffffff"
},
"android": {
  "adaptiveIcon": {
    "foregroundImage": "./assets/adaptive-icon.png",
    "backgroundColor": "#ffffff"
  }
}
```

### Plugins de Notificação
```json
"plugins": [
  [
    "expo-notifications",
    {
      "icon": "./assets/notification-icon.png",
      "color": "#ffffff",
      "sounds": [
        "./assets/notification.wav"
      ]
    }
  ]
]
```

### Configurações Android
```json
"android": {
  "edgeToEdgeEnabled": false,
  "googleServicesFile": "./google-services.json",
  "package": "com.tecbiz.tecbizassociadospush"
}
```

## Como Aplicar Essas Configurações Manualmente

Como você manteve as pastas nativas, essas configurações devem ser aplicadas diretamente nos arquivos nativos:

### Android
- **Ícones**: Coloque em `android/app/src/main/res/mipmap-*/`
- **Splash Screen**: Configure em `android/app/src/main/res/drawable/`
- **Notificações**: Configure no `AndroidManifest.xml`
- **Google Services**: O arquivo `google-services.json` já está na raiz

### iOS
- **Ícones**: Configure no Xcode ou em `ios/TecBizExpoApp/Images.xcassets/`
- **Splash Screen**: Configure no Xcode
- **Notificações**: Configure no `Info.plist`

## Resultado
- ✅ Expo doctor não deve mais reportar conflitos
- ✅ Projeto mantém funcionalidade completa
- ✅ Build EAS continuará funcionando normalmente
- ✅ Configurações nativas preservadas

## Problemas Identificados no EAS Build

### Erro Original:
```
Could not resolve project :react-native-async-storage_async-storage
Could not resolve project :react-native-edge-to-edge
- No variants exist.
```

### Causa:
- O `react-native-edge-to-edge` estava sendo incluído automaticamente pelo Expo mas não estava no package.json
- Problemas de autolinking com dependências nativas

### Soluções Aplicadas:
1. **Removido react-native-edge-to-edge**: Esta dependência estava causando conflitos no build
2. **Limpeza completa**: Removido node_modules e reinstalado dependências
3. **Configurações do app.json**: Mantidas apenas as essenciais para projetos bare

### Próximos Passos para EAS Build:

#### Problemas Identificados Durante Correção:
- `react-native-edge-to-edge` continua sendo referenciado pelo autolinking mesmo após remoção
- Arquivos do node_modules ficaram bloqueados/corrompidos
- Build local falha devido a referências antigas no CMake/autolinking

#### Soluções Recomendadas:

**Opção 1 - Limpeza Completa (Recomendada):**
1. Fechar todos os processos (VS Code, terminais)
2. Reiniciar o computador se necessário
3. Executar limpeza completa:
   ```bash
   rm -rf node_modules android/.gradle android/app/.cxx android/app/build
   npm cache clean --force
   npm install
   ```
4. Testar EAS build diretamente: `eas build --platform android --profile production`

**Opção 2 - Converter para Managed:**
1. Remover pastas nativas: `rm -rf android ios`
2. Usar prebuild: `npx expo prebuild`
3. Build com EAS: `eas build --platform android --profile production`

#### Status Final:
- ✅ Expo Doctor: 15/15 checks passed
- ✅ Configurações otimizadas para projeto bare
- ⚠️ Build requer limpeza completa devido a cache corrompido
