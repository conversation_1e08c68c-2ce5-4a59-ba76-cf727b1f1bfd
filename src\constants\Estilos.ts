import { StyleSheet } from 'react-native';
import Cores from './Cores';

// Estilos reutilizáveis do aplicativo
export const EstilosComuns = StyleSheet.create({
  // Containers
  containerPrincipal: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  
  containerCentralizado: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Cores.fundoPrincipal,
  },
  
  // Headers
  header: {
    backgroundColor: Cores.fundoHeader,
    paddingTop: 40,
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  
  tituloHeader: {
    color: Cores.textoBranco,
    fontSize: 20,
    fontWeight: 'bold',
  },
  
  // Botões
  botaoPrimario: {
    backgroundColor: Cores.primaria,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  
  textoBotaoPrimario: {
    color: Cores.textoBranco,
    fontSize: 18,
    fontWeight: 'bold',
  },
  
  botaoSecundario: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: Cores.primaria,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
  },
  
  textoBotaoSecundario: {
    color: Cores.primaria,
    fontSize: 18,
    fontWeight: 'bold',
  },
  
  // Cards
  card: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    padding: 15,
    marginVertical: 5,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  
  // Inputs
  input: {
    backgroundColor: Cores.fundoCard,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: Cores.bordaClara,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  
  // Textos
  tituloGrande: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    textAlign: 'center',
  },
  
  tituloMedio: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
  },
  
  textoPrincipal: {
    fontSize: 16,
    color: Cores.textoEscuro,
  },
  
  textoSecundario: {
    fontSize: 14,
    color: Cores.textoMedio,
  },
  
  textoAjuda: {
    fontSize: 12,
    color: Cores.textoClaro,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  
  // Loading
  containerLoading: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Cores.fundoPrincipal,
  },
  
  textoLoading: {
    marginTop: 15,
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
  },
  
  // Separadores
  separador: {
    height: 1,
    backgroundColor: Cores.bordaClara,
    marginVertical: 10,
  },
  
  separadorEspesso: {
    height: 2,
    backgroundColor: Cores.bordaMedia,
    marginVertical: 15,
  },
});

export default EstilosComuns;
