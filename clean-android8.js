#!/usr/bin/env node

/**
 * Script para limpar cache e preparar para teste Android 8.0
 * Execute: node clean-android8.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Limpando cache para teste Android 8.0...\n');

// Função para executar comando e mostrar output
function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} concluído\n`);
  } catch (error) {
    console.log(`⚠️ ${description} falhou, mas continuando...\n`);
  }
}

// Função para remover diretório se existir
function removeDir(dirPath, description) {
  if (fs.existsSync(dirPath)) {
    console.log(`🗑️ Removendo ${description}...`);
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ ${description} removido\n`);
    } catch (error) {
      console.log(`⚠️ Erro ao remover ${description}: ${error.message}\n`);
    }
  } else {
    console.log(`ℹ️ ${description} não existe, pulando...\n`);
  }
}

// 1. Parar Metro bundler se estiver rodando
console.log('🛑 Parando processos...');
try {
  execSync('taskkill /f /im node.exe', { stdio: 'ignore' });
} catch (error) {
  // Ignorar erro se não houver processos node rodando
}

// 2. Limpar cache do Metro
removeDir('.metro', 'Cache do Metro');
removeDir('node_modules/.cache', 'Cache dos node_modules');

// 3. Limpar cache do Expo
runCommand('npx expo r -c', 'Limpeza do cache do Expo');

// 4. Limpar build do Android
removeDir('android/app/build', 'Build do Android');
removeDir('android/build', 'Build do Gradle');
removeDir('android/.gradle', 'Cache do Gradle');

// 5. Limpar cache do npm/yarn
runCommand('npm cache clean --force', 'Limpeza do cache do npm');

// 6. Reinstalar dependências
console.log('📦 Reinstalando dependências...');
removeDir('node_modules', 'node_modules');
runCommand('npm install', 'Instalação das dependências');

console.log('🎯 Resumo das correções aplicadas:');
console.log('1. ✅ Interceptador global de fetch REMOVIDO (evita loop infinito)');
console.log('2. ✅ compatibleFetch ignora requisições do Metro bundler');
console.log('3. ✅ Polyfills aplicados apenas quando necessário');
console.log('4. ✅ Logs reduzidos para evitar spam');
console.log('5. ✅ Cache limpo completamente');

console.log('\n🚀 Próximos passos:');
console.log('1. Execute: npx expo run:android');
console.log('2. Aguarde o build completar');
console.log('3. Teste o login no Android 8.0');
console.log('4. Verifique se não há mais loops infinitos');

console.log('\n✅ Limpeza concluída! Pronto para testar no Android 8.0.');
