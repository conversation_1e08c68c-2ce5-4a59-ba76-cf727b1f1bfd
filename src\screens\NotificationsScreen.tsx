import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import Cores from '../constants/Cores';
import SafeHeader from '../components/SafeHeader';
import { useCustomBackNavigation } from '../hooks/useBackHandler';
import pushNotificationService, { PushNotification, notificationEvents } from '../services/pushNotificationService';
import { NavigationProp } from '../types/navigation';

interface NotificationsScreenProps {
  navigation: NavigationProp;
}

const NotificationsScreen: React.FC<NotificationsScreenProps> = ({ navigation }) => {
  const [notifications, setNotifications] = useState<PushNotification[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Responsividade
  const screenWidth = Dimensions.get('window').width;
  const isSmallScreen = screenWidth < 350;

  // Controle de navegação com botão voltar
  useCustomBackNavigation(() => {
    if (navigation.goBack) {
      navigation.goBack();
    } else {
      navigation.navigate('Home');
    }
  });

  useEffect(() => {
    loadNotifications();

    // Listeners para eventos automáticos de notificações
    const handleNotificationAdded = (data: any) => {
      loadNotifications(); // Recarregar lista
    };

    const handleNotificationRead = (data: any) => {
      loadNotifications(); // Recarregar lista
    };

    const handleAllNotificationsRead = (data: any) => {
      loadNotifications(); // Recarregar lista
    };

    // ✨ NOVO: Handler para notificações recuperadas do background
    const handleNotificationRecovered = (data: any) => {
      console.log('🔄 Notificação recuperada do background:', data.notification.title);
      console.log('📊 Total recuperadas:', data.totalCount);
      loadNotifications(); // Recarregar lista
    };

    // ✨ NOVO: Handler para atualizações gerais
    const handleNotificationsUpdated = (data: any) => {
      console.log('🔄 Notificações atualizadas:', {
        total: data.totalCount,
        unread: data.unreadCount,
        recovered: data.recoveredCount
      });
      loadNotifications(); // Recarregar lista
    };

    // ✨ NOVO: Handler para notificações recarregadas (novo usuário)
    const handleNotificationsReloaded = (data: any) => {
      console.log('🔄 Notificações recarregadas para novo usuário:', {
        total: data.totalCount,
        unread: data.unreadCount,
        userKey: data.userKey
      });
      loadNotifications(); // Recarregar lista
    };

    // ✨ NOVO: Handler para limpeza de usuário anterior
    const handlePreviousUserCleared = (data: any) => {
      console.log('🧹 Usuário anterior limpo:', {
        total: data.totalCount,
        unread: data.unreadCount
      });
      // Limpar lista imediatamente
      setNotifications([]);
    };

    // ✨ NOVO: Handler para notificação que já existe (clique na notificação)
    const handleNotificationExists = (data: any) => {
      console.log('🔄 Notificação já existe (clique):', {
        notification: data.notification.title,
        total: data.totalCount,
        unread: data.unreadCount
      });
      loadNotifications(); // Recarregar lista para garantir que apareça
    };

    // Registrar listeners
    notificationEvents.on('notificationAdded', handleNotificationAdded);
    notificationEvents.on('notificationRead', handleNotificationRead);
    notificationEvents.on('allNotificationsRead', handleAllNotificationsRead);
    notificationEvents.on('notificationRecovered', handleNotificationRecovered);
    notificationEvents.on('notificationsUpdated', handleNotificationsUpdated);
    notificationEvents.on('notificationsReloaded', handleNotificationsReloaded);
    notificationEvents.on('previousUserCleared', handlePreviousUserCleared);
    notificationEvents.on('notificationExists', handleNotificationExists);

    // Cleanup
    return () => {
      notificationEvents.off('notificationAdded', handleNotificationAdded);
      notificationEvents.off('notificationRead', handleNotificationRead);
      notificationEvents.off('allNotificationsRead', handleAllNotificationsRead);
      notificationEvents.off('notificationRecovered', handleNotificationRecovered);
      notificationEvents.off('notificationsUpdated', handleNotificationsUpdated);
      notificationEvents.off('notificationsReloaded', handleNotificationsReloaded);
      notificationEvents.off('previousUserCleared', handlePreviousUserCleared);
      notificationEvents.off('notificationExists', handleNotificationExists);
    };
  }, []);

  const loadNotifications = async () => {
    try {
      console.log('🔄 Carregando notificações...');

      // Verificar se o serviço está disponível
      if (!pushNotificationService) {
        console.warn('⚠️ Serviço de notificações não disponível');
        setNotifications([]);
        return;
      }

      // Obter notificações do serviço de forma segura
      const serviceNotifications = pushNotificationService.getNotifications() || [];

      // Log detalhado das notificações para debug
      if (serviceNotifications.length > 0) {
        console.log('🔍 Primeiras 3 notificações:', serviceNotifications.slice(0, 3).map(n => ({
          id: n.id,
          title: n.title,
          body: n.body?.substring(0, 50) + '...',
          read: n.read,
          timestamp: n.timestamp
        })));
      }

      // Filtrar notificações vazias ou inválidas de forma mais segura
      const filteredNotifications = serviceNotifications.filter(notification => {
        // Verificar se a notificação existe
        if (!notification) {
          return false;
        }

        // Filtrar notificações com título "Notificação" e corpo vazio
        if (notification.title === 'Notificação' && (!notification.body || notification.body.trim() === '')) {
          return false;
        }

        // Filtrar notificações sem título e sem corpo
        if ((!notification.title || notification.title.trim() === '') &&
            (!notification.body || notification.body.trim() === '')) {
          return false;
        }

        return true;
      });

      console.log('📊 Notificações encontradas:', serviceNotifications.length);
      console.log('✅ Notificações após filtro:', filteredNotifications.length);

      // Log das notificações não lidas
      const unreadNotifications = filteredNotifications.filter(n => !n.read);
      console.log('📊 Notificações não lidas:', unreadNotifications.length);

      setNotifications(filteredNotifications);

    } catch (error) {
      console.error('❌ Erro ao carregar notificações:', error);
      setNotifications([]);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadNotifications();
    setTimeout(() => setRefreshing(false), 500);
  };

  const handleNotificationPress = async (notification: PushNotification) => {
    if (!notification.read) {
      try {
        // Marcar como lida no serviço
        await pushNotificationService.markAsRead(notification.id);
        console.log('✅ Notificação marcada como lida');

        // Recarregar notificações para atualizar UI
        await loadNotifications();
      } catch (error) {
        console.error('❌ Erro ao marcar como lida:', error);
      }
    }

    // Mostrar detalhes da notificação
    Alert.alert(
      notification.title,
      notification.body,
      [{ text: 'OK' }]
    );
  };

  const handleMarkAllAsRead = async () => {
    try {
      console.log('🔄 Marcando todas as notificações como lidas...');

      //  dimin como lidas
      await pushNotificationService.markAllAsRead();
      console.log('✅ Todas marcadas como lidas');

      // Recarregar notificações para atualizar UI
      await loadNotifications();

      Alert.alert('Sucesso', 'Todas as notificações foram marcadas como lidas');
    } catch (error) {
      console.error('❌ Erro ao marcar todas como lidas:', error);
      Alert.alert('Erro', 'Não foi possível marcar as notificações como lidas');
    }
  };



  const formatTimestamp = (timestamp: number): string => {
    try {
      // Verificar se timestamp é válido
      if (!timestamp || isNaN(timestamp)) {
        console.warn('⚠️ Timestamp inválido:', timestamp);
        return 'Agora';
      }

      const date = new Date(timestamp);

      // Verificar se a data é válida
      if (isNaN(date.getTime())) {
        console.warn('⚠️ Data inválida para timestamp:', timestamp);
        return 'Data inválida';
      }

      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

      if (diffInHours < 1) {
        const diffInMinutes = Math.floor(diffInHours * 60);
        return diffInMinutes <= 0 ? 'Agora' : `${diffInMinutes}min atrás`;
      } else if (diffInHours < 24) {
        return `${Math.floor(diffInHours)}h atrás`;
      } else {
        // Usar formatação mais simples para evitar problemas de localização
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear().toString().slice(-2);
        const hour = date.getHours().toString().padStart(2, '0');
        const minute = date.getMinutes().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hour}:${minute}`;
      }
    } catch (error) {
      console.error('❌ Erro ao formatar timestamp:', error);
      return 'Data inválida';
    }
  };

  const renderNotification = ({ item }: { item: PushNotification }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        !item.read && styles.unreadNotification
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <Text style={[
            styles.notificationTitle,
            !item.read && styles.unreadText
          ]}>
            {item.title}
          </Text>
          <Text style={styles.notificationTime}>
            {formatTimestamp(item.timestamp)}
          </Text>
        </View>
        <Text style={[
          styles.notificationBody,
          !item.read && styles.unreadText
        ]}>
          {item.body}
        </Text>
        {!item.read && <View style={styles.unreadDot} />}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🔔</Text>
      <Text style={styles.emptyTitle}>Nenhuma notificação</Text>
      <Text style={styles.emptyMessage}>
        Você receberá notificações importantes aqui
      </Text>
    </View>
  );

  const unreadCount = notifications.filter(n => !n.read).length;

  try {
    return (
      <View style={styles.container}>
      <SafeHeader
        title="Notificações"
        onBackPress={() => navigation.goBack?.()}
      />

      {/* Actions */}
      {notifications.length > 0 && (
        <View style={[styles.actionsContainer, isSmallScreen && styles.actionsContainerSmall]}>
          <Text style={[styles.notificationCount, { fontSize: isSmallScreen ? 12 : 14 }]} numberOfLines={2}>
            {notifications.length} notificação(ões)
            {unreadCount > 0 && ` • ${unreadCount} não lida(s)`}
          </Text>
          {unreadCount > 0 && (
            <TouchableOpacity
              style={[styles.markAllButton, isSmallScreen && styles.markAllButtonSmall]}
              onPress={handleMarkAllAsRead}
            >
              <Text style={[styles.markAllButtonText, { fontSize: isSmallScreen ? 10 : 12 }]} numberOfLines={2}>
                {isSmallScreen ? 'Marcar\ncomo lidas' : 'Marcar todas como lidas'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Lista de notificações */}
      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        style={styles.list}
        contentContainerStyle={notifications.length === 0 ? styles.emptyList : undefined}
        ListEmptyComponent={renderEmptyState}
        onRefresh={handleRefresh}
        refreshing={refreshing}
        showsVerticalScrollIndicator={false}
      />


    </View>
    );
  } catch (error) {
    console.error('❌ ERRO CRÍTICO na renderização do NotificationsScreen:', error);

    // Retornar uma tela de erro simples
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <Text style={{ fontSize: 18, color: '#333', marginBottom: 10 }}>Erro ao carregar notificações</Text>
        <Text style={{ fontSize: 14, color: '#666', textAlign: 'center', paddingHorizontal: 20 }}>
          Ocorreu um erro inesperado. Tente voltar e acessar novamente.
        </Text>
        <TouchableOpacity
          style={{
            marginTop: 20,
            backgroundColor: '#007AFF',
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 5
          }}
          onPress={() => {
            console.log('🔙 Botão voltar da tela de erro pressionado');
            navigation.goBack?.() || navigation.navigate('Home');
          }}
        >
          <Text style={{ color: '#fff', fontSize: 16 }}>Voltar</Text>
        </TouchableOpacity>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Cores.fundoPrincipal,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: Cores.bordaClara,
  },
  notificationCount: {
    fontSize: 14,
    color: Cores.textoMedio,
  },
  markAllButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: Cores.primaria,
    borderRadius: 6,
    flexShrink: 1,
  },
  markAllButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: 14,
  },
  // Estilos responsivos para telas pequenas
  actionsContainerSmall: {
    flexDirection: 'column',
    alignItems: 'stretch',
    gap: 8,
  },
  markAllButtonSmall: {
    paddingHorizontal: 8,
    paddingVertical: 8,
    alignSelf: 'center',
    minWidth: 100,
    maxWidth: 120,
    flex: 0,
  },
  list: {
    flex: 1,
  },
  emptyList: {
    flex: 1,
  },
  notificationItem: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginVertical: 4,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  unreadNotification: {
    backgroundColor: '#f8f9ff',
    borderLeftWidth: 4,
    borderLeftColor: Cores.primaria,
  },
  notificationContent: {
    padding: 16,
    position: 'relative',
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  notificationTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginRight: 10,
  },
  notificationTime: {
    fontSize: 12,
    color: Cores.textoMedio,
  },
  notificationBody: {
    fontSize: 14,
    color: Cores.textoMedio,
    lineHeight: 20,
  },
  unreadText: {
    color: Cores.textoEscuro,
  },
  unreadDot: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Cores.primaria,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Cores.textoEscuro,
    marginBottom: 10,
  },
  emptyMessage: {
    fontSize: 16,
    color: Cores.textoMedio,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default NotificationsScreen;
