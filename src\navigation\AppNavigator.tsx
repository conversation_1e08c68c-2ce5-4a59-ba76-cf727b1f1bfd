import React, { useState, useEffect } from 'react';
import { View, StatusBar, Alert, LogBox } from 'react-native';
// import { SafeAreaView } from 'react-native-safe-area-context';
// AsyncStorage removido - usando storage em memória
import { lockToPortrait } from '../utils/orientation';
import SplashScreen from '../screens/SplashScreen';
import LoginScreen from '../screens/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import SaldoScreen from '../screens/SaldoScreen';
import ExtratoScreen from '../screens/ExtratoScreen';
import EsqueceuSenhaScreen from '../screens/EsqueceuSenhaScreen';
import AutorizacoesPendentesScreen from '../screens/AutorizacoesPendentesScreen';
import AlterarSenhaScreen from '../screens/AlterarSenhaScreen';
import RedeConveniadaScreen from '../screens/RedeConveniadaScreen';
import RedeConveniadaResultadosScreen from '../screens/RedeConveniadaResultadosScreen';
import CartaoVirtualScreen from '../screens/CartaoVirtualScreen';
import BloquearCartaoScreen from '../screens/BloquearCartaoScreen';
import NotificationsScreen from '../screens/NotificationsScreen';
import SendMessageScreen from '../screens/SendMessageScreen';
import DebugTokenScreen from '../screens/DebugTokenScreen';
import DebugLogsScreen from '../screens/DebugLogsScreen';
import { setupDeepLinkListener, confirmarEmail, DeepLinkData } from '../services/deepLinking';
import firebaseSimpleService from '../services/firebaseSimpleService';
import pushNotificationService, { notificationEvents } from '../services/pushNotificationService';
import { usePushNotifications } from '../store/pushNotificationStore';
import { ScreenName } from '../types/navigation';

// Storage em memória para controle de notificações de exemplo
let hasSampleNotifications = false;

// Suprimir warnings do Expo Go sobre push notifications
LogBox.ignoreLogs([
  'expo-notifications: Android Push notifications',
  'expo-notifications functionality is not fully supported in Expo Go',
  'We recommend you instead use a development build'
]);

// Navegação simples sem dependências nativas
export default function AppNavigator() {
  const [currentScreen, setCurrentScreen] = useState<ScreenName>('Splash');
  const [homeParams, setHomeParams] = useState<any>(null);
  const [autorizacoesParams, setAutorizacoesParams] = useState<any>(null);
  const [alterarSenhaParams, setAlterarSenhaParams] = useState<any>(null);
  const [screenParams, setScreenParams] = useState<any>(null);

  // Push notifications store
  const pushStore = usePushNotifications();

  // Controle de orientação - bloquear rotação
  useEffect(() => {
    lockToPortrait();
  }, []);

  // Configurar deep linking
  useEffect(() => {
    console.log('🔗 Configurando deep linking...');

    const handleDeepLink = async (data: DeepLinkData) => {
      console.log('🔗 Deep link recebido:', data);

      if (data.action === 'email_confirmation') {
        try {
          console.log('📧 Processando confirmação de email via deep link');
          console.log('📦 Parâmetros recebidos:', data.params);

          // Verificar se temos os dados essenciais do usuário vindos do PHP
          if (data.params.codass && data.params.codent) {
            console.log('✅ Dados do usuário encontrados no deep link:', {
              codass: data.params.codass,
              codent: data.params.codent,
              cartao: data.params.cartao ? data.params.cartao.substring(0, 4) + '****' : 'N/A'
            });

            // Confirmar email via API (apenas para marcar como processado)
            const result = await confirmarEmail(data.params.modo, data.params.dados);

            if (result.success) {
              // Email confirmado - ir para tela de alterar senha com dados do PHP
              Alert.alert(
                'Email Confirmado!',
                'Agora você pode definir sua nova senha.',
                [
                  {
                    text: 'OK',
                    onPress: async () => {
                      console.log('🔗 Processando deep link - limpando dados de notificações...');

                      // ✨ NOVO: Limpar dados de notificações antes de processar deep link
                      try {
                        await pushNotificationService.clearPreviousUserData();
                        console.log('✅ Dados de notificações limpos para deep link');
                      } catch (error) {
                        console.error('❌ Erro ao limpar dados para deep link:', error);
                      }

                      setAlterarSenhaParams({
                        fromEmail: true,
                        emailConfirmed: true,
                        userData: {
                          codass: data.params.codass,
                          codent: data.params.codent,
                          cartao: data.params.cartao,
                          emailCadastro: data.params.emailCadastro,
                          portador: data.params.portador
                        },
                        hideMenu: true,
                        senhaInicial: true
                      });
                      navigate('AlterarSenha');
                    }
                  }
                ]
              );
            } else {
              Alert.alert('Erro', result.message);
            }
          } else {
            console.warn('⚠️ Dados do usuário não encontrados no deep link');
            Alert.alert(
              'Erro',
              'Não foi possível obter os dados necessários. Tente fazer login novamente.',
              [{ text: 'OK', onPress: () => navigate('Login') }]
            );
          }
        } catch (error) {
          console.error('❌ Erro ao processar confirmação:', error);
          Alert.alert('Erro', 'Erro ao confirmar email. Tente novamente.');
        }
      }
    };

    // Configurar listener
    const removeListener = setupDeepLinkListener(handleDeepLink);

    // Cleanup
    return removeListener;
  }, []);

  // Inicializar push notifications
  useEffect(() => {
    const initializePushNotifications = async () => {
      try {
        console.log('🔔 Inicializando push notifications...');

        // Usar o novo serviço completo de push notifications
        console.log('🔄 Inicializando Push Notification Service...');
        let token = await pushNotificationService.initialize();

        // Se falhar, tentar o serviço simplificado como backup
        if (!token) {
          console.log('🔄 Fallback para Firebase Simple Service...');
          token = await firebaseSimpleService.initialize();
        }

        // Salvar token na store
        if (token) {
          pushStore.setFcmToken(token);
          console.log('🎯 Token salvo na store:', token.substring(0, 50) + '...');

          // Verificar se é token real ou simulado
          const isRealToken = token.startsWith('ExponentPushToken') || token.length > 100;

          if (isRealToken) {
            console.log('🚨 TOKEN REAL OBTIDO!');
            console.log('📱 Tipo: Expo Push Token REAL');
            console.log('🎯 Token completo:', token);
          } else {
            console.log('⚠️ Token simulado obtido (Expo Go limitation)');
            console.log('💡 Para tokens reais, use um development build ou dispositivo físico');
          }
        } else {
          console.log('❌ Não foi possível obter token');
        }

        // Atualizar estado na store
        pushStore.setInitialized(true);
        pushStore.setPermission(true); // Se chegou até aqui, tem permissão

        console.log('✅ Push notifications REAIS configuradas no AppNavigator');

      } catch (error) {
        console.error('❌ Erro ao inicializar push notifications:', error);
      }
    };

    initializePushNotifications();
  }, []);

  // Listener para navegação automática quando notificação é tocada
  useEffect(() => {
    const handleNotificationTapped = (data: any) => {
      console.log('🧭 Notificação tocada - navegando para tela de notificações');
      console.log('📋 Dados da notificação:', data.notification?.title);

      // Navegar para tela de notificações
      navigate('Notifications');
    };

    // Registrar listener
    notificationEvents.on('notificationTapped', handleNotificationTapped);

    // Cleanup
    return () => {
      notificationEvents.off('notificationTapped', handleNotificationTapped);
    };
  }, []);

  const navigate = (screen: ScreenName, params?: any) => {
    console.log('🧭 Navegando para:', screen);
    console.log('🧭 Params recebidos:', JSON.stringify(params, null, 2));
    console.log('🧭 Tela atual antes:', currentScreen);

    if (params) {
      if (screen === 'Home') {
        console.log('🧭 Salvando em homeParams:', JSON.stringify(params, null, 2));
        setHomeParams(params);
      } else if (screen === 'AutorizacoesPendentes') {
        console.log('🧭 Salvando em autorizacoesParams:', JSON.stringify(params, null, 2));
        setAutorizacoesParams(params);
      } else if (screen === 'AlterarSenha') {
        console.log('🧭 Salvando em alterarSenhaParams:', JSON.stringify(params, null, 2));
        setAlterarSenhaParams(params);
      } else if (screen === 'RedeConveniadaResultados') {
        console.log('🧭 Salvando em screenParams:', JSON.stringify(params, null, 2));
        setScreenParams(params);
      }
    } else {
      console.log('🧭 Nenhum parâmetro para salvar');
    }

    // Aguardar um pouco para o estado ser atualizado antes de mudar a tela
    setTimeout(() => {
      console.log('🧭 Mudando tela para:', screen);
      setCurrentScreen(screen);
    }, 50);
  };

  const renderScreen = () => {
    console.log('🎬 Renderizando tela:', currentScreen);
    console.log('🎬 homeParams atual:', JSON.stringify(homeParams, null, 2));

    switch (currentScreen) {
      case 'Splash':
        return <SplashScreen navigation={{ navigate }} />;
      case 'Login':
        return <LoginScreen navigation={{ navigate }} />;
      case 'Home':
        return <HomeScreen navigation={{ navigate }} route={{ params: { userData: homeParams } }} />;
      case 'Saldo':
        return <SaldoScreen navigation={{ navigate }} />;
      case 'Extrato':
        return <ExtratoScreen navigation={{ navigate }} />;
      case 'EsqueceuSenha':
        return <EsqueceuSenhaScreen navigation={{ navigate, goBack: () => navigate('Login') }} />;
      case 'AutorizacoesPendentes':
        console.log('🎬 Renderizando AutorizacoesPendentes com params:', JSON.stringify(autorizacoesParams, null, 2));
        return <AutorizacoesPendentesScreen navigation={{ navigate }} route={{ params: autorizacoesParams }} />;
      case 'AlterarSenha':
        console.log('🎬 Renderizando AlterarSenha com params:', JSON.stringify(alterarSenhaParams, null, 2));
        return <AlterarSenhaScreen navigation={{ navigate, goBack: () => navigate('Home') }} route={{ params: alterarSenhaParams }} />;
      case 'RedeConveniada':
        return <RedeConveniadaScreen navigation={{ navigate, goBack: () => navigate('Home') }} />;
      case 'RedeConveniadaResultados':
        return <RedeConveniadaResultadosScreen navigation={{ goBack: () => navigate('RedeConveniada') }} route={{ params: screenParams }} />;
      case 'CartaoVirtual':
        return <CartaoVirtualScreen navigation={{ goBack: () => navigate('Home'), navigate }} />;
      case 'BloquearCartao':
        return <BloquearCartaoScreen navigation={{ goBack: () => navigate('Home'), navigate }} />;
      case 'Notifications':
        return <NotificationsScreen navigation={{ navigate, goBack: () => navigate('Home') }} />;
      default:
        return <SplashScreen navigation={{ navigate }} />;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {renderScreen()}
    </View>
  );
}