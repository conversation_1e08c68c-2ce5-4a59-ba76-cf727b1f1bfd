{"name": "tecbizexpoapp", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "expo": "~53.0.17", "expo-status-bar": "~2.2.3", "firebase": "^12.0.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-navigation": "^5.0.0", "expo-notifications": "~0.31.4", "expo-device": "~7.1.4", "@react-native-async-storage/async-storage": "2.1.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}