// Serviço Firebase SIMPLIFICADO e ROBUSTO para PRODUÇÃO
// Foco em Expo Notifications com logs forçados para debug
import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Alert, Platform } from 'react-native';
import { nativeStorage } from '../utils/nativeStorage';
import { FIREBASE_CONFIG } from '../config/firebase';

// FORÇAR LOGS SEMPRE (para debug)
const forceLog = (message: string, ...args: any[]) => {
  console.log(`[FIREBASE-SIMPLE] ${message}`, ...args);
};

// Interface para notificações
export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: number;
  read: boolean;
}

class FirebaseSimpleService {
  private isInitialized = false;
  private currentToken: string | null = null;
  private tokenType: 'expo' | 'fallback' = 'expo';
  private notifications: NotificationData[] = [];

  /**
   * Inicializa o serviço Firebase SIMPLIFICADO
   */
  async initialize(): Promise<string | null> {
    try {
      forceLog('🔥 INICIANDO Firebase Simple Service...');
      forceLog('📱 Dispositivo físico:', Device.isDevice);
      forceLog('🏭 Ambiente:', Constants.executionEnvironment);
      forceLog('🆔 Project ID:', Constants.expoConfig?.extra?.eas?.projectId);

      // Verificar se é um dispositivo físico
      if (!Device.isDevice) {
        forceLog('⚠️ SIMULADOR DETECTADO - Push notifications limitadas');
        return this.initializeFallback();
      }

      // Configurar comportamento das notificações
      await this.setupNotifications();
      forceLog('✅ Notificações configuradas');

      // Solicitar permissões
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        forceLog('❌ PERMISSÃO NEGADA');
        return this.initializeFallback();
      }
      forceLog('✅ Permissões concedidas');

      // Obter token com múltiplas tentativas
      const token = await this.getTokenWithRetry();
      
      if (token) {
        this.currentToken = token;
        this.isInitialized = true;
        
        // Salvar token
        await nativeStorage.setItem('push_token', token);
        await nativeStorage.setItem('token_type', this.tokenType);
        
        // Configurar handlers
        this.setupMessageHandlers();
        
        forceLog('🎉 SUCESSO! Token obtido:', token.substring(0, 50) + '...');
        forceLog('🔧 Tipo do token:', this.tokenType);
        forceLog('📊 É token real:', this.isRealToken(token));
        
        return token;
      } else {
        forceLog('💀 FALHA TOTAL - Nenhum token obtido');
        return this.initializeFallback();
      }
      
    } catch (error) {
      forceLog('❌ ERRO CRÍTICO na inicialização:', error);
      return this.initializeFallback();
    }
  }

  /**
   * Configurar comportamento das notificações
   */
  private async setupNotifications(): Promise<void> {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
      }),
    });
    forceLog('📱 Handler de notificações configurado');
  }

  /**
   * Solicitar permissões
   */
  private async requestPermissions(): Promise<boolean> {
    try {
      forceLog('🔐 Solicitando permissões...');
      
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      forceLog('📋 Status atual das permissões:', existingStatus);
      
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        forceLog('🔄 Solicitando novas permissões...');
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
        forceLog('📋 Novo status das permissões:', finalStatus);
      }
      
      const granted = finalStatus === 'granted';
      forceLog('✅ Permissões concedidas:', granted);
      
      return granted;
    } catch (error) {
      forceLog('❌ Erro ao solicitar permissões:', error);
      return false;
    }
  }

  /**
   * Obter token com múltiplas tentativas
   */
  private async getTokenWithRetry(): Promise<string | null> {
    const maxAttempts = 10;
    let token: string | null = null;

    forceLog('🎯 Iniciando obtenção de token...');
    forceLog('🔧 Máximo de tentativas:', maxAttempts);

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        forceLog(`🔄 Tentativa ${attempt}/${maxAttempts}...`);

        let tokenData;
        const projectId = Constants.expoConfig?.extra?.eas?.projectId || 
                         Constants.expoConfig?.extra?.expoProjectId || 
                         FIREBASE_CONFIG.projectId;

        forceLog('🆔 Usando Project ID:', projectId);

        // Estratégias diferentes por tentativa
        if (attempt <= 3) {
          // Estratégia 1: Com projectId
          forceLog('📋 Estratégia: Com Project ID');
          tokenData = await Notifications.getExpoPushTokenAsync({
            projectId: projectId,
          });
        } else if (attempt <= 6) {
          // Estratégia 2: Sem projectId
          forceLog('📋 Estratégia: Sem Project ID');
          tokenData = await Notifications.getExpoPushTokenAsync();
        } else {
          // Estratégia 3: Com configurações alternativas
          forceLog('📋 Estratégia: Configurações alternativas');
          try {
            tokenData = await Notifications.getExpoPushTokenAsync({
              projectId: projectId,
              applicationId: FIREBASE_CONFIG.appId,
            });
          } catch {
            tokenData = await Notifications.getExpoPushTokenAsync();
          }
        }

        token = tokenData?.data || null;

        if (token) {
          forceLog(`✅ TOKEN OBTIDO na tentativa ${attempt}!`);
          forceLog('🎯 Token:', token.substring(0, 50) + '...');
          
          const isReal = this.isRealToken(token);
          forceLog('📊 É token real:', isReal);
          
          this.tokenType = 'expo';
          break;
        } else {
          forceLog(`⚠️ Tentativa ${attempt} falhou - token vazio`);
        }

        // Delay entre tentativas
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
      } catch (error) {
        forceLog(`❌ Erro na tentativa ${attempt}:`, error);
        
        // Delay maior em caso de erro
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    return token;
  }

  /**
   * Verificar se token é real
   */
  private isRealToken(token: string | null): boolean {
    if (!token) return false;
    
    // Token real do Expo sempre começa com ExponentPushToken[
    // OU pode ser um token FCM válido (mais de 140 caracteres)
    return (token.startsWith('ExponentPushToken[') && token.length > 50) ||
           (token.length > 140 && !token.includes('simulator') && !token.includes('development'));
  }

  /**
   * Inicializar fallback
   */
  private async initializeFallback(): Promise<string | null> {
    forceLog('🔄 Inicializando modo FALLBACK...');
    
    const fallbackToken = `tecbiz_fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentToken = fallbackToken;
    this.tokenType = 'fallback';
    this.isInitialized = true;
    
    await nativeStorage.setItem('push_token', fallbackToken);
    await nativeStorage.setItem('token_type', 'fallback');
    
    forceLog('✅ Fallback inicializado:', fallbackToken);
    
    return fallbackToken;
  }

  /**
   * Configurar handlers de mensagem
   */
  private setupMessageHandlers(): void {
    forceLog('📨 Configurando handlers de mensagem...');
    
    // Listener para notificações recebidas quando app está em foreground
    Notifications.addNotificationReceivedListener(notification => {
      forceLog('📨 Notificação recebida (foreground):', notification.request.content.title);
      this.addNotification({
        id: notification.request.identifier,
        title: notification.request.content.title || 'Notificação',
        body: notification.request.content.body || '',
        data: notification.request.content.data,
        timestamp: Date.now(),
        read: false
      });
    });

    // Listener para quando usuário toca na notificação
    Notifications.addNotificationResponseReceivedListener(response => {
      forceLog('👆 Notificação tocada:', response.notification.request.content.title);
    });
    
    forceLog('✅ Handlers configurados');
  }

  /**
   * Adicionar notificação ao histórico local
   */
  private async addNotification(notification: NotificationData): Promise<void> {
    this.notifications.unshift(notification);
    
    // Manter apenas as últimas 50 notificações
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50);
    }
    
    forceLog('📝 Notificação adicionada ao histórico:', notification.title);
  }

  // Métodos públicos para acesso externo
  getToken(): string | null {
    return this.currentToken;
  }

  getTokenType(): string {
    return this.tokenType;
  }

  isTokenReal(): boolean {
    return this.isRealToken(this.currentToken);
  }

  getNotifications(): NotificationData[] {
    return this.notifications;
  }

  getServiceInfo(): any {
    return {
      initialized: this.isInitialized,
      token: this.currentToken?.substring(0, 50) + '...',
      tokenType: this.tokenType,
      isReal: this.isRealToken(this.currentToken),
      notificationCount: this.notifications.length,
      environment: Constants.executionEnvironment,
      isDevice: Device.isDevice
    };
  }
}

// Exportar instância única
const firebaseSimpleService = new FirebaseSimpleService();
export default firebaseSimpleService;
