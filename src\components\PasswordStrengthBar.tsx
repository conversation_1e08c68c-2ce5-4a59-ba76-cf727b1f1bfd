import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Cores from '../constants/Cores';

interface PasswordStrengthBarProps {
  strength: {
    erro: string;
    avaliacao: string;
    complemento: string;
  } | null;
}

const PasswordStrengthBar = ({ strength }: PasswordStrengthBarProps) => {
  if (!strength) return null;

  // Determinar a força baseada no erro (0 = forte, 1+ = fraca)
  const isStrong = strength.erro === '0' || strength.erro === 0;
  const isMedium = strength.avaliacao === 'MEDIA' || (!isStrong && strength.complemento?.toLowerCase().includes('média'));
  const isWeak = !isStrong && !isMedium;

  // Determinar a cor e largura da barra
  const getBarColor = () => {
    if (isStrong) return '#4CAF50'; // Verde
    if (isMedium) return '#FF9800'; // Amarelo/Laranja
    return '#F44336'; // Vermelho
  };

  const getBarWidth = () => {
    if (isStrong) return '100%'; // Barra completa
    if (isMedium) return '60%'; // Barra até o meio
    return '30%'; // Barra pequena
  };

  const getStrengthText = () => {
    if (isStrong) return 'Senha Forte';
    if (isMedium) return 'Senha Média';
    return 'Senha Fraca';
  };

  return (
    <View style={styles.container}>
      {/* Barra de força */}
      <View style={styles.barContainer}>
        <View style={styles.barBackground}>
          <View 
            style={[
              styles.barFill,
              {
                backgroundColor: getBarColor(),
                width: getBarWidth(),
              }
            ]} 
          />
        </View>
      </View>

      {/* Texto de força */}
      <View style={styles.textContainer}>
        <Text style={[styles.strengthText, { color: getBarColor() }]}>
          {getStrengthText()}
        </Text>
        {strength.complemento && (
          <Text style={styles.complementText}>
            {strength.complemento}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 5,
  },
  barContainer: {
    marginBottom: 8,
  },
  barBackground: {
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  barFill: {
    height: '100%',
    borderRadius: 3,
    transition: 'width 0.3s ease',
  },
  textContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  strengthText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  complementText: {
    fontSize: 11,
    color: Cores.textoMedio,
    fontStyle: 'italic',
    flex: 1,
    textAlign: 'right',
  },
});

export default PasswordStrengthBar;
