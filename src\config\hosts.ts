// Configuração centralizada de hosts/URLs do projeto

// Tipos para configuração
export interface HostConfig {
  HOST_QUENTE: string;
  HOST_TESTE: string;
  CURRENT_HOST: string;
}

// Configuração dos hosts
export const HOSTS: HostConfig = {
  // Servidor de produção (quente)
  HOST_QUENTE: 'https://www2.tecbiz.com.br/tecbiz/tecbiz.php',
  
  // Servidor de teste/desenvolvimento
  HOST_TESTE: 'http://*************:8888/tecbiz/tecbiz.php',
  
  // Host atual - altere aqui para trocar entre teste e produção
  CURRENT_HOST: 'https://www2.tecbiz.com.br/tecbiz/tecbiz.php', // <<< ENDEREÇO ATUAL
};

// Função para obter o host atual
export const getCurrentHost = (): string => {
  return HOSTS.CURRENT_HOST;
};

// Função para verificar se está em modo de teste
export const isTestMode = (): boolean => {
  return HOSTS.CURRENT_HOST === HOSTS.HOST_TESTE;
};

// Função para verificar se está em modo de produção
export const isProductionMode = (): boolean => {
  return HOSTS.CURRENT_HOST === HOSTS.HOST_QUENTE;
};

// Função para trocar para modo de teste
export const setTestMode = (): void => {
  HOSTS.CURRENT_HOST = HOSTS.HOST_TESTE;
  console.log('🔧 Modo alterado para TESTE:', HOSTS.CURRENT_HOST);
};

// Função para trocar para modo de produção
export const setProductionMode = (): void => {
  HOSTS.CURRENT_HOST = HOSTS.HOST_QUENTE;
  console.log('🔧 Modo alterado para PRODUÇÃO:', HOSTS.CURRENT_HOST);
};

// Função para construir URL completa com parâmetros
export const buildUrl = (params: string): string => {
  const host = getCurrentHost();
  const separator = host.includes('?') ? '&' : '?';
  return `${host}${separator}${params}`;
};

// Função especial para esqueceu senha (sempre usar HOST_QUENTE)
export const buildUrlEsqueceuSenha = (params: string): string => {
  const host = HOSTS.HOST_QUENTE; // Sempre usar produção para email
  const separator = host.includes('?') ? '&' : '?';
  return `${host}${separator}${params}`;
};

// Log da configuração atual
console.log('🌐 Configuração de hosts carregada:');
console.log('📍 HOST_QUENTE:', HOSTS.HOST_QUENTE);
console.log('📍 HOST_TESTE:', HOSTS.HOST_TESTE);
console.log('🎯 CURRENT_HOST:', HOSTS.CURRENT_HOST);
console.log('🔧 Modo atual:', isTestMode() ? 'TESTE' : 'PRODUÇÃO');
