#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 CORREÇÃO FINAL - Removendo todas as referências ao edge-to-edge...\n');

// 1. Limpar arquivos de build
const buildPaths = [
  'android/app/build',
  'android/app/.cxx',
  'android/build',
  'android/.gradle',
  'node_modules/react-native-edge-to-edge/android/build'
];

console.log('🗑️ Limpando arquivos de build...');
buildPaths.forEach(buildPath => {
  if (fs.existsSync(buildPath)) {
    console.log(`   Removendo: ${buildPath}`);
    fs.rmSync(buildPath, { recursive: true, force: true });
  }
});

// 2. Criar/atualizar react-native.config.js
const configContent = `module.exports = {
  dependencies: {
    'react-native-edge-to-edge': {
      platforms: {
        android: null, // disable Android platform auto linking
        ios: null, // disable iOS platform auto linking
      },
    },
  },
};`;

console.log('\n📝 Configurando autolinking...');
try {
  fs.writeFileSync('react-native.config.js', configContent);
  console.log('   ✅ react-native.config.js criado/atualizado');
} catch (error) {
  console.log('   ❌ Erro ao criar react-native.config.js:', error.message);
}

// 3. Corrigir styles.xml
const stylesPath = 'android/app/src/main/res/values/styles.xml';
console.log('\n🎨 Corrigindo styles.xml...');
if (fs.existsSync(stylesPath)) {
  try {
    let stylesContent = fs.readFileSync(stylesPath, 'utf8');
    if (stylesContent.includes('Theme.EdgeToEdge')) {
      stylesContent = stylesContent.replace(
        'parent="Theme.EdgeToEdge"',
        'parent="Theme.AppCompat.Light.NoActionBar"'
      );
      fs.writeFileSync(stylesPath, stylesContent);
      console.log('   ✅ Removida referência ao Theme.EdgeToEdge');
    } else {
      console.log('   ✅ Styles.xml já está correto');
    }
  } catch (error) {
    console.log('   ❌ Erro ao corrigir styles.xml:', error.message);
  }
} else {
  console.log('   ⚠️ Arquivo styles.xml não encontrado');
}

// 4. Verificar gradle.properties
const gradlePropsPath = 'android/gradle.properties';
console.log('\n⚙️ Verificando gradle.properties...');
if (fs.existsSync(gradlePropsPath)) {
  try {
    const propsContent = fs.readFileSync(gradlePropsPath, 'utf8');
    if (propsContent.includes('expo.edgeToEdgeEnabled=false')) {
      console.log('   ✅ Edge-to-edge já está desabilitado');
    } else {
      console.log('   ⚠️ Adicione expo.edgeToEdgeEnabled=false ao gradle.properties');
    }
  } catch (error) {
    console.log('   ❌ Erro ao verificar gradle.properties:', error.message);
  }
}

// 5. Verificar package names
console.log('\n📦 Verificando package names...');
const correctPackage = 'com.tecbiz.tecbizassociadospush';

// Verificar app.config.js
try {
  const configContent = fs.readFileSync('app.config.js', 'utf8');
  if (configContent.includes(`package: "${correctPackage}"`)) {
    console.log('   ✅ Package name correto no app.config.js');
  } else {
    console.log('   ❌ Package name incorreto no app.config.js');
  }
} catch (error) {
  console.log('   ❌ Erro ao verificar app.config.js');
}

// Verificar build.gradle
try {
  const gradleContent = fs.readFileSync('android/app/build.gradle', 'utf8');
  if (gradleContent.includes(correctPackage)) {
    console.log('   ✅ Package name correto no build.gradle');
  } else {
    console.log('   ❌ Package name incorreto no build.gradle');
  }
} catch (error) {
  console.log('   ❌ Erro ao verificar build.gradle');
}

console.log('\n' + '='.repeat(60));
console.log('🎉 CORREÇÃO FINAL CONCLUÍDA!');
console.log('\n🚀 PRÓXIMOS PASSOS:');
console.log('1. Execute: eas build --platform android --profile preview --clear-cache');
console.log('2. Aguarde o build completar');
console.log('3. Verifique se não há mais erros relacionados ao edge-to-edge');
console.log('\n⚠️ IMPORTANTE: Use sempre --clear-cache no EAS build!');
console.log('='.repeat(60));
