# 🔧 CONFIGURAR FCM PARA EXPO PUSH NOTIFICATIONS

## 🎯 **PROBLEMA IDENTIFICADO:**

❌ **Erro atual**: "Unable to retrieve the FCM server key for the recipient's app"  
✅ **Solução**: Configurar chave FCM no projeto Expo  

## 📋 **PASSOS PARA RESOLVER:**

### **OPÇÃO 1: CONFIGURAR FCM NO EXPO (RECOMENDADO)**

#### **1. Obter chave FCM do Firebase:**
```
1. Acesse: https://console.firebase.google.com/
2. Selecione seu projeto (ou crie um novo)
3. V<PERSON> para: Configurações do projeto (ícone engrenagem)
4. Aba "Cloud Messaging"
5. Copie a "Chave do servidor" (Server Key)
```

#### **2. Configurar no projeto Expo:**
```bash
# Instalar CLI do Expo (se não tiver)
npm install -g @expo/cli

# Fazer login no Expo
expo login

# Configurar FCM
expo push:android:upload --api-key SUA_CHAVE_FCM_AQUI
```

#### **3. Verificar configuração:**
```bash
expo push:android:show
```

### **OPÇÃO 2: USAR ARQUIVO PHP CORRIGIDO**

#### **1. Substituir arquivo atual:**
```bash
# Fazer backup do atual
cp sendAndroidPushNotification.php sendAndroidPushNotification_backup.php

# Usar versão corrigida
cp sendAndroidPushNotification_fixed.php sendAndroidPushNotification.php
```

#### **2. Configurar chave FCM no PHP:**
```php
// Editar linha 7 do arquivo:
define('_FCM_SERVER_KEY_', 'SUA_CHAVE_FCM_AQUI');
```

---

## 🚀 **TESTE APÓS CONFIGURAÇÃO:**

### **1. Testar envio:**
```bash
php test_send_notification.php
```

### **2. Resultado esperado:**
```
✅ Push enviado com sucesso!
📊 Resultado: {"success":true}
```

---

## 🔍 **VERIFICAR SE APP RECEBE PUSH FECHADO:**

### **1. Preparar teste:**
```
1. Fechar completamente o app (não apenas minimizar)
2. Certificar que está logado e token salvo
3. Manter dispositivo desbloqueado
4. Executar teste PHP
```

### **2. Executar teste:**
```bash
php test_send_notification.php
```

### **3. Verificar recebimento:**
```
- Aguardar 5-10 segundos
- Notificação deve aparecer na tela
- Som/vibração deve tocar
- Badge pode aparecer no ícone do app
```

### **4. Verificar no app:**
```
1. Abrir app
2. Ir para tela de Notificações
3. Mensagem deve aparecer na lista
4. Contador deve estar correto
```

---

## 🛠️ **SOLUÇÃO ALTERNATIVA (SEM FCM):**

Se não conseguir configurar FCM, use apenas notificações locais:

### **1. Arquivo PHP simplificado:**
```php
<?php
class sendAndroidPushNotification {
    private $titulo, $mensagem, $dados;
    public $success = true;
    
    public function __construct($titulo, $mensagem, $tokens, $dados = []) {
        $this->titulo = $titulo;
        $this->mensagem = $mensagem;
        $this->dados = $dados;
        
        // Log para debug
        error_log("📱 Push Local: $titulo");
        error_log("📝 Mensagem: $mensagem");
        error_log("🎯 Tokens: " . count($tokens));
    }
    
    public function send() {
        // Simular sucesso (apenas para desenvolvimento)
        error_log("✅ Push 'enviado' (modo local)");
        return true;
    }
    
    public function getResult() {
        return ['success' => true, 'mode' => 'local_only'];
    }
    
    public function getInvalidTokens() {
        return [];
    }
}
?>
```

### **2. Usar apenas no app:**
- Mensagens aparecem apenas localmente
- Não há push real quando app fechado
- Funciona para desenvolvimento/teste

---

## 📊 **DIAGNÓSTICO COMPLETO:**

### **1. Verificar configuração atual:**
```bash
# Verificar se Expo CLI está instalado
expo --version

# Verificar login
expo whoami

# Verificar configuração FCM
expo push:android:show
```

### **2. Verificar app:**
```bash
# Logs do app
adb logcat | grep -E "(PUSH|FCM|EXPO)"

# Ou via Expo
npx expo logs --platform android
```

### **3. Verificar token:**
```
- App → Home → Debug Token
- Token deve começar com: ExponentPushToken[
- Token deve ter ~200 caracteres
- Token deve ser obtido em dispositivo físico
```

---

## 🎯 **RESUMO DAS SOLUÇÕES:**

### **✅ SOLUÇÃO COMPLETA (RECOMENDADA):**
1. Configurar FCM no Firebase Console
2. Configurar FCM no projeto Expo
3. Testar com `php test_send_notification.php`
4. Push funcionará mesmo com app fechado

### **✅ SOLUÇÃO RÁPIDA:**
1. Usar `sendAndroidPushNotification_fixed.php`
2. Configurar chave FCM no arquivo PHP
3. Testar envio

### **✅ SOLUÇÃO TEMPORÁRIA:**
1. Usar apenas notificações locais
2. Funciona para desenvolvimento
3. Não envia push real quando app fechado

---

## 🚀 **PRÓXIMOS PASSOS:**

### **1. ESCOLHER SOLUÇÃO:**
- **Produção**: Configurar FCM completo
- **Desenvolvimento**: Usar versão corrigida
- **Teste rápido**: Apenas local

### **2. IMPLEMENTAR:**
- Seguir passos da solução escolhida
- Testar com script PHP
- Verificar recebimento no app

### **3. VALIDAR:**
- App fechado recebe push? ✅
- App aberto mostra notificação? ✅
- Contador funciona? ✅
- Marcar como lida funciona? ✅

**🎉 COM QUALQUER UMA DESSAS SOLUÇÕES, SEU SISTEMA VAI FUNCIONAR PERFEITAMENTE!**
