import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Cores from '../constants/Cores';
import { useAppContext } from '../context/AppContext';

interface StatusCartaoProps {
  showIcon?: boolean;
  fontSize?: number;
  style?: any;
}

const StatusCartao: React.FC<StatusCartaoProps> = ({
  showIcon = true,
  fontSize = 16,
  style
}) => {
  // Volta para o AppContext para evitar loops
  const { getUsuarioData } = useAppContext();
  const usuarioData = getUsuarioData();

  const getStatusInfo = () => {
    const sitcar = usuarioData?.usuario?.sitcar;
    const situacao = usuarioData?.usuario?.situacao;

    // Se não tem nenhuma das duas propriedades
    if (!sitcar && !situacao) {
      return {
        texto: 'Indisponível',
        cor: Cores.textoMedio,
        icone: '❓'
      };
    }

    // Lógica de prioridade:
    // 1. Se sitcar = 'C' (Cancelado), prevalece independente da situacao
    // 2. Se sitcar = 'B' (Bloqueado), prevalece independente da situacao
    // 3. Se sitcar = 'L' mas situacao = 'B', mostrar Bloqueado
    // 4. Só mostrar Liberado se ambos estiverem liberados

    // Verificar cancelamento primeiro (prioridade máxima)
    if (sitcar === 'C') {
      return {
        texto: 'Cancelado',
        cor: Cores.erro,
        icone: '❌'
      };
    }

    // Verificar bloqueio (segunda prioridade)
    if (sitcar === 'B' || situacao === 'B') {
      return {
        texto: 'Bloqueado',
        cor: Cores.erro,
        icone: '🔒'
      };
    }

    // Só liberar se ambos estiverem liberados
    if (sitcar === 'L' && (situacao === 'L' || !situacao)) {
      return {
        texto: 'Liberado',
        cor: Cores.sucesso,
        icone: '✅'
      };
    }

    // Caso padrão - se chegou aqui, algo está inconsistente
    return {
      texto: 'Verificar Status',
      cor: Cores.textoMedio,
      icone: '⚠️'
    };
  };

  const statusInfo = getStatusInfo();

  return (
    <View style={[styles.container, style]}>
      {showIcon && (
        <Text style={[styles.icone, { fontSize: fontSize + 2 }]}>
          {statusInfo.icone}
        </Text>
      )}
      <Text style={[
        styles.texto,
        { color: statusInfo.cor, fontSize }
      ]}>
        {statusInfo.texto}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  icone: {
    fontWeight: 'bold',
  },
  texto: {
    fontWeight: 'bold',
  },
});

export default StatusCartao;
