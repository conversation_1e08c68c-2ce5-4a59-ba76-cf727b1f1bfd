import { useEffect } from 'react';
import { BackHandler, Alert } from 'react-native';

interface UseBackHandlerProps {
  onBack?: () => boolean; // Retorna true se o evento foi tratado
  showExitConfirmation?: boolean;
  currentScreen?: string;
}

export const useBackHandler = ({ 
  onBack, 
  showExitConfirmation = false,
  currentScreen 
}: UseBackHandlerProps) => {
  
  useEffect(() => {
    const backAction = () => {
      // Se há uma função customizada de voltar, usa ela
      if (onBack) {
        return onBack();
      }

      // Se está na tela Home e deve mostrar confirmação
      if (showExitConfirmation && currentScreen === 'Home') {
        Alert.alert(
          'Sair do Aplicativo',
          'Deseja realmente sair do aplicativo?',
          [
            {
              text: 'Cancelar',
              onPress: () => null,
              style: 'cancel',
            },
            {
              text: 'Sair',
              onPress: () => BackHandler.exitApp(),
            },
          ]
        );
        return true; // Previne o comportamento padrão
      }

      // Para outras telas, permite o comportamento padrão (voltar)
      return false;
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [onBack, showExitConfirmation, currentScreen]);
};

// Hook específico para confirmação de saída
export const useExitConfirmation = (currentScreen: string) => {
  useBackHandler({
    showExitConfirmation: true,
    currentScreen,
  });
};

// Hook para navegação customizada
export const useCustomBackNavigation = (navigationFunction: () => void) => {
  useBackHandler({
    onBack: () => {
      navigationFunction();
      return true; // Indica que o evento foi tratado
    },
  });
};
